# AIDollars Virtual Currency Implementation Plan

## Overview
Implementation of a virtual currency system called "AIDollars" with automatic top-up functionality similar to fal.ai, where users are auto-charged when their balance falls below a threshold. Each AI request will cost 0.10 AIDollars.

## Current Infrastructure Analysis

### Existing Billing/Transaction Tables:
1. **Order** table - Stores one-time purchases with:
   - Total amount, currency, status
   - OrderItem for line items
   - Provider (Stripe)

2. **Subscription** table - For recurring subscriptions
   - Status, currency, period dates
   - SubscriptionItem for plan details

3. **AiUsage** table - Already tracks AI usage with:
   - Organization, contact, conversation IDs
   - Provider (openrouter, zai)
   - Model, tokens used
   - Cost tracking
   - Timestamps

4. **Invoice Integration** - Via Stripe API (not stored locally)
   - Fetched on-demand from Stripe
   - Shows payment history

### What We Have ✅
- AI usage tracking (AiUsage table)
- Stripe integration for payments
- Order history for purchases
- Invoice fetching from Stripe

### What We Need to Add ❌
- AIDollars balance per organization
- Credit/debit transaction history
- Auto-charge configuration & thresholds
- Progressive tier logic ($10→$50→$100→$1000)

## Database Schema Changes

### New Tables Needed:

1. **AIDollarsBalance** table:
   ```sql
   - id: UUID (PK)
   - organizationId: UUID (FK to Organization)
   - balance: Decimal (current balance)
   - lastTopupAt: DateTime
   - autoChargeEnabled: Boolean
   - minimumBalance: Decimal (threshold for auto-charge)
   - createdAt: DateTime
   - updatedAt: DateTime
   ```

2. **AIDollarsTransaction** table:
   ```sql
   - id: UUID (PK)
   - organizationId: UUID (FK to Organization)
   - type: Enum (CREDIT, DEBIT, REFUND, ADJUSTMENT)
   - amount: Decimal
   - description: String
   - referenceId: String (order ID, usage ID, etc.)
   - balanceAfter: Decimal (balance after transaction)
   - createdAt: DateTime
   ```

3. **AIDollarsTopupRule** table:
   ```sql
   - id: UUID (PK)
   - organizationId: UUID (FK to Organization)
   - tierLevel: Int (1=$10, 2=$50, 3=$100, 4=$1000)
   - topupAmount: Decimal
   - usageThreshold: Decimal (usage rate to trigger tier)
   - isActive: Boolean
   - createdAt: DateTime
   - updatedAt: DateTime
   ```

## Stripe Integration Enhancement

### Payment Intents for Top-ups:
- Create one-time charges for balance top-ups
- Store payment method for future auto-charges
- Implement progressive charging tiers ($10 → $50 → $100 → $1000)

### Auto-charge Logic:
- Monitor balance thresholds
- Automatically charge based on usage patterns
- Progressive tier selection based on daily usage rate

## Core Features Implementation

### 1. Balance Management Service (`packages/aidollars/`)
- Check balance before AI requests
- Deduct 0.10 AIDollars per request
- Track usage patterns
- Trigger auto-charges when needed

### 2. Progressive Charging Algorithm
- Start with $10 top-up
- Analyze usage rate (requests/day)
- Automatically increase tier based on:
  - Usage velocity (how fast credits are consumed)
  - Historical patterns
  - Configurable thresholds

### 3. Dashboard Components
- Balance display widget
- Transaction history table
- Auto-charge settings panel
- Usage analytics charts

### 4. API Middleware
- Balance check before processing requests
- Automatic deduction on successful requests
- Queue system for pending charges

## Implementation Steps

1. **Database Schema**
   - Create migrations for new AIDollars tables
   - Update Organization model relationships

2. **AIDollars Service Package**
   - Build core balance management logic
   - Implement transaction recording
   - Create auto-charge algorithms

3. **Stripe Integration**
   - Extend existing billing provider
   - Add top-up payment intent creation
   - Implement saved payment method usage

4. **API Integration**
   - Add balance checking to AI request flow
   - Integrate with existing AiUsage tracking
   - Handle insufficient balance scenarios

5. **Dashboard UI**
   - Create balance widgets and components
   - Build transaction history views
   - Add auto-charge configuration settings

6. **Background Workers**
   - Implement auto-charge monitoring
   - Usage pattern analysis
   - Tier adjustment logic

7. **Analytics & Reporting**
   - Usage analytics dashboard
   - Cost prediction models
   - Balance forecasting

## Key Files to Create/Modify

### New Package
- `packages/aidollars/` - New package for currency logic
  - `src/balance.ts` - Balance management
  - `src/transactions.ts` - Transaction recording
  - `src/auto-charge.ts` - Auto-charge logic
  - `src/tiers.ts` - Progressive tier management

### Database
- `packages/database/prisma/schema.prisma` - Add new models
- `packages/database/migrations/` - New migration files

### Billing Integration
- `packages/billing/src/provider/stripe/` - Extend for top-ups
- `packages/billing/src/aidollars.ts` - AIDollars-specific billing logic

### Dashboard
- `apps/dashboard/components/aidollars/` - UI components
  - `balance-widget.tsx`
  - `transaction-history.tsx`
  - `auto-charge-settings.tsx`
- `apps/dashboard/actions/aidollars/` - Server actions
- `apps/dashboard/data/aidollars/` - Data fetching

### API Integration
- `apps/public-api/middleware/balance-check.ts` - Balance checking middleware
- Integration with existing AI request handlers

## Technical Considerations

### Cost Per Request
- 0.10 AIDollars per AI request
- Deducted immediately after successful request
- Failed requests should not be charged

### Auto-charge Thresholds
- Default minimum balance: $5 (50 AIDollars)
- Progressive tiers based on usage velocity:
  - Low usage (< 10 requests/day): $10 top-up
  - Medium usage (10-50 requests/day): $50 top-up
  - High usage (50-200 requests/day): $100 top-up
  - Enterprise usage (200+ requests/day): $1000 top-up

### Balance Management
- Real-time balance updates
- Transaction atomicity
- Audit trail for all changes
- Prevent negative balances

### Security
- Secure payment method storage via Stripe
- Audit logging for all transactions
- Rate limiting for top-up requests
- Fraud detection integration

---

*Alex, I got you - keeping it simple with KISS principle!*