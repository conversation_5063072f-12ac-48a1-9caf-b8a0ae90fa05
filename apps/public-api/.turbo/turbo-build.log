
> public-api@0.0.0 build /Users/<USER>/Documents/GitHub/AIChromaPRO/apps/public-api
> next build --turbopack

   ▲ Next.js 15.3.3 (Turbopack)

   Creating an optimized production build ...
 ✓ Compiled successfully in 7.8s
   Linting and checking validity of types ...
   Collecting page data ...
   Generating static pages (0/5) ...
   Generating static pages (1/5) 
   Generating static pages (2/5) 
   Generating static pages (3/5) 
 ✓ Generating static pages (5/5)
   Finalizing page optimization ...
   Collecting build traces ...

Route (app)                         Size  First Load JS
┌ ○ /                             350 kB         461 kB
├ ○ /_not-found                      0 B         112 kB
└ ƒ /organization                    0 B            0 B
+ First Load JS shared by all     112 kB
  ├ chunks/6c6f713062dd4313.js   77.2 kB
  ├ chunks/85d49505fa1d8c26.js   19.2 kB
  └ other shared chunks (total)  15.3 kB


○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand

 ⚠ Support for Turbopack builds is experimental. We don't recommend deploying mission-critical applications to production.

- Turbopack currently always builds production sourcemaps for the browser. This will include project sourcecode if deployed to production.
- It is expected that your bundle size might be different from `next build` with webpack. This will be improved as we work towards stability.
- This build is without disk caching; subsequent builds will become faster when disk caching becomes available.
- When comparing output to webpack builds, make sure to first clear the Next.js cache by deleting the `.next` directory.

Provide feedback for Turbopack builds at https://github.com/vercel/next.js/discussions/77721
