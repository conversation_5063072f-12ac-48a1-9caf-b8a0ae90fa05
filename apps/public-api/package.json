{"name": "public-api", "version": "0.0.0", "private": true, "scripts": {"dev": "next dev --port 3002 --turbopack", "build": "next build --turbopack", "start": "next start --port 3002", "analyze": "BUNDLE_ANALYZE=both next build --turbopack", "clean": "git clean -xdf .cache .next .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@workspace/api-keys": "workspace:*", "@workspace/common": "workspace:*", "@workspace/database": "workspace:*", "next": "15.3.3", "next-swagger-doc": "0.4.1", "react": "19.0.0", "react-dom": "19.0.0", "swagger-ui-react": "5.24.0"}, "devDependencies": {"@next/bundle-analyzer": "15.3.3", "@types/node": "22.15.30", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@types/swagger-ui-react": "5.18.0", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "prettier": "@workspace/prettier-config"}