import { literal, z } from 'zod';
import { FollowupStatus } from '@workspace/database';

import { SortDirection } from '~/types/sort-direction';

const MAX_INT32 = +2147483647;

export enum GetFollowupsSortBy {
  ScheduledAt = 'scheduledAt',
  Status = 'status',
  ContactNickname = 'contactNickname',
  CreatedAt = 'createdAt',
  SendViaExtension = 'sendViaExtension',
  ContactEngagementPriority = 'contactEngagementPriority'
}

export const getFollowupsSchema = z.object({
  pageIndex: z.coerce
    .number({
      required_error: 'Page index is required.',
      invalid_type_error: 'Page index must be a number.'
    })
    .int()
    .min(0, 'Page number must be equal or greater than 0.')
    .max(MAX_INT32, `Page number must be equal or smaller than ${MAX_INT32}.`),
  pageSize: z.coerce
    .number({
      required_error: 'Page size is required.',
      invalid_type_error: 'Page size must be a number.'
    })
    .int()
    .min(1, 'Page size must be equal or greater than 1.')
    .max(100, 'Page size must be equal or smaller than 100.'),
  sortBy: z.nativeEnum(GetFollowupsSortBy, {
    required_error: 'Sort by is required.',
    invalid_type_error: 'Sort by must be a string.'
  }),
  sortDirection: z.nativeEnum(SortDirection, {
    required_error: 'Sort direction is required.',
    invalid_type_error: 'Sort direction must be a string.'
  }),
  searchQuery: z
    .string({
      invalid_type_error: 'Search query must be a string.'
    })
    .max(2000, 'Maximum 2000 characters allowed.')
    .optional()
    .or(literal('')),
  status: z.nativeEnum(FollowupStatus, {
    invalid_type_error: 'Status must be a valid followup status.'
  }).optional(),
  showSent: z.boolean().default(false)
});

export type GetFollowupsSchema = z.infer<typeof getFollowupsSchema>;