import { z } from 'zod';

export const updateOrganizationDetailsSchema = z.object({
  name: z
    .string({
      required_error: 'Organization name is required.',
      invalid_type_error: 'Name must be a string.'
    })
    .trim()
    .min(1, 'Organization name is required.')
    .max(255, 'Maximum 255 characters allowed.'),
  email: z
    .string({
      invalid_type_error: 'Email must be a string.'
    })
    .trim()
    .max(255, 'Maximum 255 characters allowed.')
    .email('Enter a valid email address.')
    .optional()
    .or(z.literal(''))
});

export type UpdateOrganizationDetailsSchema = z.infer<
  typeof updateOrganizationDetailsSchema
>;
