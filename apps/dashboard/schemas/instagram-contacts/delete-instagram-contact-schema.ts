import { z } from 'zod';

export const deleteInstagramContactSchema = z.object({
  instagramContactId: z
    .string({
      required_error: 'Instagram contact ID is required.',
      invalid_type_error: 'Instagram contact ID must be a string.'
    })
    .trim()
    .uuid('Instagram contact ID is invalid.')
    .min(1, 'Instagram contact ID is required.')
    .max(36, 'Maximum 36 characters allowed.')
});

export type DeleteInstagramContactSchema = z.infer<typeof deleteInstagramContactSchema>;