import { literal, z } from 'zod';
import { InstagramStage } from '@workspace/database';

import { SortDirection } from '~/types/sort-direction';

const MAX_INT32 = +2147483647;

export enum GetInstagramContactsSortBy {
  Nickname = 'nickname',
  LastActivity = 'lastActivity',
  MessageCount = 'messageCount',
  Stage = 'stage',
  CreatedAt = 'createdAt'
}

export const getInstagramContactsSchema = z.object({
  pageIndex: z.coerce
    .number({
      required_error: 'Page index is required.',
      invalid_type_error: 'Page index must be a number.'
    })
    .int()
    .min(0, 'Page number must be equal or greater than 1.')
    .max(MAX_INT32, `Page number must be equal or smaller than ${MAX_INT32}.`),
  pageSize: z.coerce
    .number({
      required_error: 'Page size is required.',
      invalid_type_error: 'Page size must be a number.'
    })
    .int()
    .min(1, 'Page size must be equal or greater than 1.')
    .max(100, 'Page number must be equal or smaller than 100.'),
  sortBy: z.nativeEnum(GetInstagramContactsSortBy, {
    required_error: 'Sort by is required.',
    invalid_type_error: 'Sort by must be a string.'
  }),
  sortDirection: z.nativeEnum(SortDirection, {
    required_error: 'Sort direction is required.',
    invalid_type_error: 'Sort direction must be a string.'
  }),
  searchQuery: z
    .string({
      invalid_type_error: 'Search query must be a string.'
    })
    .max(2000, 'Maximum 2000 characters allowed.')
    .optional()
    .or(literal('')),
  stage: z.nativeEnum(InstagramStage, {
    invalid_type_error: 'Stage must be a valid Instagram stage.'
  }).optional(),
  stages: z.array(z.nativeEnum(InstagramStage)).optional(),
  firstActivityDateRange: z.object({
    from: z.date(),
    to: z.date().optional()
  }).optional(),
  lastActivityDateRange: z.object({
    from: z.date(),
    to: z.date().optional()
  }).optional()
});

export type GetInstagramContactsSchema = z.infer<typeof getInstagramContactsSchema>;