import { z } from 'zod';

import { FileUploadAction } from '~/lib/file-upload';

export const updatePersonalDetailsSchema = z.object({
  action: z.nativeEnum(FileUploadAction, {
    required_error: 'Action is required',
    invalid_type_error: 'Action must be a string'
  }),
  image: z
    .string({
      invalid_type_error: 'Image must be a string.'
    })
    .optional()
    .or(z.literal('')),
  name: z
    .string({
      required_error: 'Name is required.',
      invalid_type_error: 'Name must be a string.'
    })
    .trim()
    .min(1, 'Name is required.')
    .max(64, 'Maximum 64 characters allowed.')
});

export type UpdatePersonalDetailsSchema = z.infer<
  typeof updatePersonalDetailsSchema
>;
