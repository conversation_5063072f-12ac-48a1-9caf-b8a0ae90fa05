import { z } from 'zod';

export const updateInstagramSettingsSchema = z.object({
  instagramBotEnabled: z.boolean(),
  instagramResponseTimeMin: z.number().min(10).max(180),
  instagramResponseTimeMax: z.number().min(10).max(180),
  instagramFollowupsEnabled: z.boolean()
}).refine(
  (data) => data.instagramResponseTimeMin <= data.instagramResponseTimeMax,
  {
    message: "Min response time must be less than or equal to max response time",
    path: ["instagramResponseTimeMax"]
  }
);

export type UpdateInstagramSettingsSchema = z.infer<typeof updateInstagramSettingsSchema>;