import { z } from 'zod';

import { SortDirection } from '~/types/sort-direction';

export const getAttackListSortByEnum = z.enum([
  'scheduledAt',
  'priority',
  'status',
  'nickname',
  'createdAt'
]);

export type GetAttackListSortBy = z.infer<typeof getAttackListSortByEnum>;

export const GetAttackListSortBy = {
  ScheduledAt: 'scheduledAt' as const,
  Priority: 'priority' as const,
  Status: 'status' as const,
  Nickname: 'nickname' as const,
  CreatedAt: 'createdAt' as const
};

export const attackListStatusEnum = z.enum(['pending', 'sent', 'failed']);
export type AttackListStatus = z.infer<typeof attackListStatusEnum>;

export const attackListPriorityEnum = z.enum(['1', '2', '3']);
export type AttackListPriority = z.infer<typeof attackListPriorityEnum>;

export const getAttackListSchema = z.object({
  pageIndex: z.number().min(0).default(0),
  pageSize: z.number().min(1).max(100).default(10),
  sortBy: getAttackListSortByEnum.default(GetAttackListSortBy.ScheduledAt),
  sortDirection: z.nativeEnum(SortDirection).default(SortDirection.Desc),
  status: attackListStatusEnum.optional(),
  priority: attackListPriorityEnum.optional(),
  searchQuery: z.string().optional(),
  showSent: z.boolean().optional().default(false)
});

export type GetAttackListSchema = z.infer<typeof getAttackListSchema>;