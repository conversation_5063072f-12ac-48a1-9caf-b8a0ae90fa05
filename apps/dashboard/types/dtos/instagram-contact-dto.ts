import { type InstagramStage, type FollowupStatus } from '@workspace/database';

export type InstagramContactDto = {
  id: string;
  instagramUserId: string;
  nickname?: string;
  profilePhoto?: string;
  notes?: string;
  lastActivity?: Date;
  messageCount: number;
  firstMessageAt?: Date;
  lastMessageAt?: Date;
  lastContactMessageAt?: Date;
  aiEnabled: boolean;
  stage: InstagramStage;
  createdAt: Date;
  updatedAt: Date;
  // Follow-up fields
  followup1Text?: string;
  followup1Time?: Date;
  followup1Status?: FollowupStatus;
  followup1Extension?: boolean;
  followup2Text?: string;
  followup2Time?: Date;
  followup2Status?: FollowupStatus;
  followup2Extension?: boolean;
  followup3Text?: string;
  followup3Time?: Date;
  followup3Status?: FollowupStatus;
  followup3Extension?: boolean;
  followup4Text?: string;
  followup4Time?: Date;
  followup4Status?: FollowupStatus;
  followup4Extension?: boolean;
  followup5Text?: string;
  followup5Time?: Date;
  followup5Status?: FollowupStatus;
  followup5Extension?: boolean;
  followup6Text?: string;
  followup6Time?: Date;
  followup6Status?: FollowupStatus;
  followup6Extension?: boolean;
};