import { type FollowupStatus, type EngagementPriority } from '@workspace/database';

export type FollowupDto = {
  id: string;
  followupNumber: number;
  text?: string;
  scheduledAt?: Date;
  status?: FollowupStatus;
  sendViaExtension: boolean;
  // Contact information
  contactId: string;
  contactNickname?: string;
  contactInstagramUserId: string;
  contactProfilePhoto?: string;
  contactEngagementPriority: EngagementPriority;
};