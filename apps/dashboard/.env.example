# -------------------------- ANALYTICS --------------------------

# Select an analytics provider: <PERSON>sol<PERSON> (default), Google Analytics, PostHog or Umami.
# Set environment variables based on your choice.
# Configuration file: packages/analytics/provider/index.ts

# Provider: Google Analytics
NEXT_PUBLIC_ANALYTICS_GA_MEASUREMENT_ID=
NEXT_PUBLIC_ANALYTICS_GA_DISABLE_LOCALHOST_TRACKING=false
NEXT_PUBLIC_ANALYTICS_GA_DISABLE_PAGE_VIEWS_TRACKING=false

# Provider: PostHog
NEXT_PUBLIC_ANALYTICS_POSTHOG_KEY=phc_1234
NEXT_PUBLIC_ANALYTICS_POSTHOG_HOST=https://us.i.posthog.com

# Provider: Umami
NEXT_PUBLIC_ANALYTICS_UMAMI_HOST=https://cloud.umami.is/script.js
NEXT_PUBLIC_ANALYTICS_UMAMI_WEBSITE_ID=
NEXT_PUBLIC_ANALYTICS_UMAMI_DISABLE_LOCALHOST_TRACKING=false

# -------------------------- AUTH --------------------------

AUTH_SECRET='G+yQTy4q+qj6VA6K76B5S8zmvpm3BvyXgJzIXhk0JMs=' # run 'npx auth secret' or 'openssl rand -base64 32' to generate a new AUTH_SECRET
AUTH_GOOGLE_CLIENT_ID=
AUTH_GOOGLE_CLIENT_SECRET=
AUTH_MICROSOFT_ENTRA_ID_CLIENT_ID=
AUTH_MICROSOFT_ENTRA_ID_CLIENT_SECRET=

# -------------------------- BILLING --------------------------

# Select a billing provider: Stripe or Lemon Squeezy.
# Set environment variables based on your choice.
# Configuration file: packages/billing/provider/index.ts

NEXT_PUBLIC_BILLING_PRICE_PRO_MONTH_ID=
NEXT_PUBLIC_BILLING_PRICE_PRO_YEAR_ID=
NEXT_PUBLIC_BILLING_PRICE_LIFETIME_ID=
NEXT_PUBLIC_BILLING_PRICE_ENTERPRISE_MONTH_ID=
NEXT_PUBLIC_BILLING_PRICE_ENTERPRISE_YEAR_ID=

# Provider: Stripe
BILLING_STRIPE_SECRET_KEY=
BILLING_STRIPE_WEBHOOK_SECRET=

# -------------------------- DATABASE --------------------------

DATABASE_URL=postgresql://postgres:password@localhost:5432/database

# -------------------------- EMAIL --------------------------

EMAIL_FROM=<EMAIL>
EMAIL_FEEDBACK_INBOX=<EMAIL>

# Select an email provider: NodeMailer (default) or Resend.
# Set environment variables based on your choice.
# Configuration file: packages/email/provider/index.ts

# Provider: NodeMailer
EMAIL_NODEMAILER_URL=smtp://<EMAIL>:suyz <NAME_EMAIL>:465

# Provider: Postmark
EMAIL_POSTMARK_SERVER_TOKEN=

# Provider: Resend
EMAIL_RESEND_API_KEY=

# Provider: SendGrid
EMAIL_SENDGRID_API_KEY=

# -------------------------- MONITORING --------------------------

# Select a monitoring provider: Console (default) or Sentry.
# Set environment variables based on your choice.
# Configuration file: packages/monitoring/provider/index.ts

# Provider: Sentry
MONITORING_SENTRY_ORG='my-org-slug'
MONITORING_SENTRY_PROJECT='my-project-name'
MONITORING_SENTRY_AUTH_TOKEN='sntrys_ey....'
NEXT_PUBLIC_MONITORING_SENTRY_DSN='https://123456789.ingest.de.sentry.io/123456789'
SENTRY_SUPPRESS_TURBOPACK_WARNING=1 # https://github.com/getsentry/sentry-javascript/blob/develop/packages/nextjs/src/config/withSentryConfig.ts#L156

# -------------------------- ROUTES --------------------------

NEXT_PUBLIC_DASHBOARD_URL=http://localhost:3000
NEXT_PUBLIC_MARKETING_URL=http://localhost:3001
NEXT_PUBLIC_PUBLIC_API_URL=http://localhost:3002
