'use client';

import * as React from 'react';
import { Button } from '@workspace/ui/components/button';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@workspace/ui/components/dialog';
import { ScrollArea } from '@workspace/ui/components/scroll-area';
import { Textarea } from '@workspace/ui/components/textarea';
import { toast } from '@workspace/ui/components/sonner';
import { Eye, Copy } from 'lucide-react';
import { useActiveOrganization } from '~/hooks/use-active-organization';

interface BotStylePreviewModalProps {
  botStyleId: string;
  variableValues?: Record<string, any>;
  trigger?: React.ReactNode;
}

export function BotStylePreviewModal({ 
  botStyleId, 
  variableValues = {},
  trigger 
}: BotStylePreviewModalProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [processedPrompt, setProcessedPrompt] = React.useState('');
  const [originalPrompt, setOriginalPrompt] = React.useState('');
  const [variablesUsed, setVariablesUsed] = React.useState<string[]>([]);
  const [loading, setLoading] = React.useState(false);
  const organization = useActiveOrganization();

  const loadPreview = async () => {
    if (!isOpen || !organization) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/bot-styles/${botStyleId}/preview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationId: organization.id,
          variableValues
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to load preview');
      }

      const data = await response.json();
      setProcessedPrompt(data.processedPrompt);
      setOriginalPrompt(data.originalPrompt);
      setVariablesUsed(data.variablesUsed || []);
    } catch (error) {
      console.error('Preview error:', error);
      toast.error('Failed to load prompt preview');
      setProcessedPrompt('Error loading preview. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    loadPreview();
  }, [isOpen, botStyleId, variableValues, organization?.id]);

  const handleCopyPrompt = () => {
    navigator.clipboard.writeText(processedPrompt);
    toast.success('Prompt copied to clipboard');
  };

  const highlightUnprocessedVariables = (text: string) => {
    // Highlight any remaining {{variable}} patterns in red
    return text.replace(/\{\{([^}]+)\}\}/g, '<span style="color: red; font-weight: bold;">{{$1}}</span>');
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {trigger ? (
        <div onClick={() => setIsOpen(true)}>{trigger}</div>
      ) : (
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(true)}
        >
          <Eye className="h-4 w-4 mr-1" />
          Preview My Prompt
        </Button>
      )}

      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Preview My Prompt</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">Generating preview...</span>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <span>Variables processed: {variablesUsed.length}</span>
                  {variablesUsed.length > 0 && (
                    <span>({variablesUsed.join(', ')})</span>
                  )}
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleCopyPrompt}
                >
                  <Copy className="h-4 w-4 mr-1" />
                  Copy
                </Button>
              </div>

              <ScrollArea className="h-[500px] w-full rounded border">
                <div className="p-4">
                  <pre className="whitespace-pre-wrap text-sm font-mono">
                    {processedPrompt.includes('{{') ? (
                      <div 
                        dangerouslySetInnerHTML={{ 
                          __html: highlightUnprocessedVariables(processedPrompt) 
                        }} 
                      />
                    ) : (
                      processedPrompt
                    )}
                  </pre>
                </div>
              </ScrollArea>

              {processedPrompt.includes('{{') && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded text-sm">
                  <p className="text-yellow-800">
                    <strong>Warning:</strong> Some variables (highlighted in red) were not processed. 
                    Make sure to configure their values in the Variables tab.
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}