'use client';

import * as React from 'react';
import { usePathname } from 'next/navigation';

import {
  baseUrl,
  getPathname,
  replaceOrgSlug,
  routes
} from '@workspace/routes';

import { AppSidebar } from '~/components/organizations/slug/app-sidebar';
import { ChromeExtensionSidebar } from '~/components/organizations/slug/chrome-extension/chrome-extension-sidebar';
import { SettingsSidebar } from '~/components/organizations/slug/settings/settings-sidebar';
import { useActiveOrganization } from '~/hooks/use-active-organization';
import type { OrganizationDto } from '~/types/dtos/organization-dto';
import type { ProfileDto } from '~/types/dtos/profile-dto';

export type SidebarRendererProps = {
  organizations: OrganizationDto[];
  profile: ProfileDto;
  isPlatformAdmin?: boolean;
};

export function SidebarRenderer(
  props: SidebarRendererProps
): React.JSX.Element {
  const pathname = usePathname();
  const activeOrganization = useActiveOrganization();
  const settingsRoute = replaceOrgSlug(
    routes.dashboard.organizations.slug.settings.Index,
    activeOrganization.slug
  );
  const chromeExtensionRoute = replaceOrgSlug(
    routes.dashboard.organizations.slug.chromeExtension.Index,
    activeOrganization.slug
  );

  if (pathname.startsWith(getPathname(settingsRoute, baseUrl.Dashboard))) {
    return <SettingsSidebar isPlatformAdmin={props.isPlatformAdmin} />;
  }

  if (pathname.startsWith(getPathname(chromeExtensionRoute, baseUrl.Dashboard))) {
    return <ChromeExtensionSidebar />;
  }

  return <AppSidebar organizations={props.organizations} profile={props.profile} />;
}
