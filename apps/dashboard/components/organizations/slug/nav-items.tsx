import {
  BellIcon,
  BotIcon,
  CalendarIcon,
  CodeIcon,
  CreditCardIcon,
  FileTextIcon,
  FlaskConicalIcon,
  HomeIcon,
  KeyIcon,
  ListIcon,
  LockKeyholeIcon,
  MessageSquareIcon,
  PuzzleIcon,
  SettingsIcon,
  ShieldIcon,
  StoreIcon,
  UserIcon,
  UserPlus2Icon,
  UsersIcon
} from 'lucide-react';
import type { LucideIcon } from 'lucide-react';

import { replaceOrgSlug, routes } from '@workspace/routes';

type NavItem = {
  title: string;
  href: string;
  disabled?: boolean;
  external?: boolean;
  icon: LucideIcon;
};

export function createMainNavItems(slug: string): NavItem[] {
  return [
    {
      title: 'Home',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.Home, slug),
      icon: HomeIcon
    },
    {
      title: 'Conversations',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.InstagramContacts, slug),
      icon: MessageSquareIcon
    },
    {
      title: 'Follow ups',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.Followups, slug),
      icon: CalendarIcon
    },
    {
      title: 'AI Settings',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.InstagramSettings, slug),
      icon: BotIcon
    },
    {
      title: 'Prompts',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.Prompts, slug),
      icon: FileTextIcon
    },
    {
      title: 'Integrations',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.integrations.Index, slug),
      icon: PuzzleIcon
    },
    {
      title: 'Settings',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.Index,
        slug
      ),
      icon: SettingsIcon
    }
  ];
}

export function createAccountNavItems(slug: string): NavItem[] {
  return [
    {
      title: 'Profile',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.account.Profile,
        slug
      ),
      icon: UserIcon
    },
    {
      title: 'Security',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.account.Security,
        slug
      ),
      icon: LockKeyholeIcon
    },
    {
      title: 'Notifications',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.account.Notifications,
        slug
      ),
      icon: BellIcon
    }
  ];
}

export function createOrganizationNavItems(slug: string): NavItem[] {
  return [
    {
      title: 'General',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.organization.General,
        slug
      ),
      icon: StoreIcon
    },
    {
      title: 'Members',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.organization.Members,
        slug
      ),
      icon: UserPlus2Icon
    },
    {
      title: 'Billing',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.organization.Billing,
        slug
      ),
      icon: CreditCardIcon
    },
    {
      title: 'Developers',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.settings.organization.Developers,
        slug
      ),
      icon: CodeIcon
    }
  ];
}

export function createSaasNavItems(slug: string): NavItem[] {
  return [
    {
      title: 'General',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.settings.saas.General, slug),
      icon: SettingsIcon
    },
    {
      title: 'Bot Styles',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.settings.saas.BotStyles, slug),
      icon: BotIcon
    },
    {
      title: 'Testosteron',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.settings.saas.AITest, slug),
      icon: FlaskConicalIcon
    },
    {
      title: 'Organizations',
      href: replaceOrgSlug(routes.dashboard.organizations.slug.settings.saas.Organizations, slug),
      icon: UsersIcon
    }
  ];
}

export function createChromeExtensionNavItems(slug: string): NavItem[] {
  return [
    {
      title: 'Attack List',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.chromeExtension.AttackList,
        slug
      ),
      icon: ListIcon
    },
    {
      title: 'Settings',
      href: replaceOrgSlug(
        routes.dashboard.organizations.slug.chromeExtension.Settings,
        slug
      ),
      icon: SettingsIcon
    }
  ];
}
