import 'server-only';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { ValidationError } from '@workspace/common/errors';
import { Prisma } from '@workspace/database';
import { prisma } from '@workspace/database/client';
import { z } from 'zod';

const getInstagramMessagesSchema = z.object({
  conversationId: z.string().min(1, 'Conversation ID is required'),
  pageIndex: z.number().int().min(0).default(0),
  pageSize: z.number().int().min(1).max(100).default(50),
});

export type GetInstagramMessagesSchema = z.infer<typeof getInstagramMessagesSchema>;

export type InstagramMessageDto = {
  id: string;
  messageId: string;
  instagramUserId: string;
  isFromBusiness: boolean;
  messageType: string;
  content: string | null;
  attachmentUrl: string | null;
  instagramCreatedTime: Date;
  createdAt: Date;
};

export async function getInstagramMessages(input: GetInstagramMessagesSchema): Promise<{
  messages: InstagramMessageDto[];
  totalCount: number;
  hasMore: boolean;
}> {
  const ctx = await getAuthOrganizationContext();

  const result = getInstagramMessagesSchema.safeParse(input);
  if (!result.success) {
    throw new ValidationError(JSON.stringify(result.error.flatten()));
  }
  const parsedInput = result.data;

  // DEBUG: Log the query parameters
  console.log(`[DEBUG] Fetching messages with parameters:`);
  console.log(`[DEBUG]   organizationId: ${ctx.organization.id}`);
  console.log(`[DEBUG]   conversationId: ${parsedInput.conversationId}`);
  console.log(`[DEBUG]   pageIndex: ${parsedInput.pageIndex}, pageSize: ${parsedInput.pageSize}`);

  // Get messages and total count
  const [messages, totalCount] = await prisma.$transaction([
    prisma.instagramMessage.findMany({
      skip: parsedInput.pageIndex * parsedInput.pageSize,
      take: parsedInput.pageSize,
      where: {
        organizationId: ctx.organization.id,
        conversationId: parsedInput.conversationId
      },
      select: {
        id: true,
        messageId: true,
        instagramUserId: true,
        isFromBusiness: true,
        messageType: true,
        content: true,
        attachmentUrl: true,
        instagramCreatedTime: true,
        createdAt: true
      },
      orderBy: {
        instagramCreatedTime: 'desc' // Newest first for pagination, then reverse for display
      }
    }),
    prisma.instagramMessage.count({
      where: {
        organizationId: ctx.organization.id,
        conversationId: parsedInput.conversationId
      }
    })
  ]);

  const hasMore = (parsedInput.pageIndex + 1) * parsedInput.pageSize < totalCount;

  // DEBUG: Log the results
  console.log(`[DEBUG] Messages query results:`);
  console.log(`[DEBUG]   Found ${messages.length} messages`);
  console.log(`[DEBUG]   Total count: ${totalCount}`);
  console.log(`[DEBUG]   Has more: ${hasMore}`);
  
  if (messages.length > 0) {
    console.log(`[DEBUG] First message details:`);
    console.log(`[DEBUG]   messageId: ${messages[0].messageId}`);
    console.log(`[DEBUG]   instagramUserId: ${messages[0].instagramUserId}`);
    console.log(`[DEBUG]   content: "${messages[0].content}"`);
    console.log(`[DEBUG]   isFromBusiness: ${messages[0].isFromBusiness}`);
  } else {
    console.log(`[DEBUG] No messages found - checking if any messages exist for this organization...`);
    // Check if any messages exist at all for this organization
    const anyMessages = await prisma.instagramMessage.findFirst({
      where: { organizationId: ctx.organization.id },
      select: { conversationId: true, instagramUserId: true, content: true }
    });
    if (anyMessages) {
      console.log(`[DEBUG] Found other messages in organization with different conversationId:`);
      console.log(`[DEBUG]   conversationId: ${anyMessages.conversationId}`);
      console.log(`[DEBUG]   instagramUserId: ${anyMessages.instagramUserId}`);
      console.log(`[DEBUG]   content: "${anyMessages.content}"`);
    } else {
      console.log(`[DEBUG] No messages found in organization at all`);
    }
  }

  const mapped: InstagramMessageDto[] = messages.map((message) => ({
    id: message.id,
    messageId: message.messageId,
    instagramUserId: message.instagramUserId,
    isFromBusiness: message.isFromBusiness,
    messageType: message.messageType,
    content: message.content,
    attachmentUrl: message.attachmentUrl,
    instagramCreatedTime: message.instagramCreatedTime,
    createdAt: message.createdAt
  }));

  return { messages: mapped, totalCount, hasMore };
}