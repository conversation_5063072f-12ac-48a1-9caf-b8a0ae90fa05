import 'server-only';

import { unstable_cache as cache } from 'next/cache';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';

import {
  Caching,
  defaultRevalidateTimeInSeconds,
  OrganizationCacheKey
} from '~/data/caching';
import type { InstagramSettingsDto } from '~/types/dtos/instagram-settings-dto';

export async function getInstagramSettings(): Promise<InstagramSettingsDto> {
  const ctx = await getAuthOrganizationContext();

  return cache(
    async () => {
      const organization = await prisma.organization.findUnique({
        where: {
          id: ctx.organization.id
        },
        select: {
          instagramBotEnabled: true,
          instagramResponseTimeMin: true,
          instagramResponseTimeMax: true,
          instagramFollowupsEnabled: true,
          instagramIsConnected: true,
          instagramUsername: true,
          instagramName: true,
          instagramProfilePicture: true,
          instagramConnectedAt: true,
          instagramAccountId: true,
          instagramTokenExpiresAt: true
        }
      });

      if (!organization) {
        throw new NotFoundError('Organization not found');
      }

      return {
        instagramBotEnabled: organization.instagramBotEnabled,
        instagramResponseTimeMin: organization.instagramResponseTimeMin,
        instagramResponseTimeMax: organization.instagramResponseTimeMax,
        instagramFollowupsEnabled: organization.instagramFollowupsEnabled,
        instagramIsConnected: organization.instagramIsConnected,
        instagramUsername: organization.instagramUsername,
        instagramName: organization.instagramName,
        instagramProfilePicture: organization.instagramProfilePicture,
        instagramConnectedAt: organization.instagramConnectedAt,
        instagramAccountId: organization.instagramAccountId,
        instagramTokenExpiresAt: organization.instagramTokenExpiresAt
      };
    },
    Caching.createOrganizationKeyParts(
      OrganizationCacheKey.InstagramSettings,
      ctx.organization.id
    ),
    {
      revalidate: defaultRevalidateTimeInSeconds,
      tags: [
        Caching.createOrganizationTag(
          OrganizationCacheKey.InstagramSettings,
          ctx.organization.id
        )
      ]
    }
  )();
}