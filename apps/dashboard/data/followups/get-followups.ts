import 'server-only';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { ValidationError } from '@workspace/common/errors';
import { Prisma, FollowupStatus } from '@workspace/database';
import { prisma } from '@workspace/database/client';

import {
  getFollowupsSchema,
  GetFollowupsSortBy,
  type GetFollowupsSchema
} from '~/schemas/followups/get-followups-schema';
import type { FollowupDto } from '~/types/dtos/followup-dto';

export async function getFollowups(input: GetFollowupsSchema): Promise<{
  followups: FollowupDto[];
  filteredCount: number;
  totalCount: number;
}> {
  const ctx = await getAuthOrganizationContext();

  const result = getFollowupsSchema.safeParse(input);
  if (!result.success) {
    throw new ValidationError(JSON.stringify(result.error.flatten()));
  }
  const parsedInput = result.data;

  const searchCriteria: Prisma.StringFilter | undefined =
    parsedInput.searchQuery
      ? { contains: parsedInput.searchQuery, mode: 'insensitive' }
      : undefined;

  // Build the where clause for followups
  const followupWhere = (followupNum: number) => ({
    [`followup${followupNum}_text`]: { not: null },
    ...(parsedInput.status && {
      [`followup${followupNum}_status`]: parsedInput.status
    }),
    // Default to showing only PENDING if showSent is false
    ...(!parsedInput.showSent && !parsedInput.status && {
      [`followup${followupNum}_status`]: 'PENDING'
    }),
    ...(searchCriteria && {
      OR: [
        { nickname: searchCriteria },
        { instagramUserId: searchCriteria },
        { [`followup${followupNum}_text`]: searchCriteria }
      ]
    })
  });

  // Get all contacts with followups
  const contacts = await prisma.instagramContact.findMany({
    where: {
      organizationId: ctx.organization.id,
      OR: [
        followupWhere(1),
        followupWhere(2),
        followupWhere(3),
        followupWhere(4),
        followupWhere(5),
        followupWhere(6)
      ]
    },
    select: {
      id: true,
      instagramUserId: true,
      nickname: true,
      profilePhoto: true,
      engagementPriority: true,
      followup1_text: true,
      followup1_scheduledAt: true,
      followup1_status: true,
      followup1_sendViaExtension: true,
      followup2_text: true,
      followup2_scheduledAt: true,
      followup2_status: true,
      followup2_sendViaExtension: true,
      followup3_text: true,
      followup3_scheduledAt: true,
      followup3_status: true,
      followup3_sendViaExtension: true,
      followup4_text: true,
      followup4_scheduledAt: true,
      followup4_status: true,
      followup4_sendViaExtension: true,
      followup5_text: true,
      followup5_scheduledAt: true,
      followup5_status: true,
      followup5_sendViaExtension: true,
      followup6_text: true,
      followup6_scheduledAt: true,
      followup6_status: true,
      followup6_sendViaExtension: true,
      createdAt: true
    }
  });

  // Flatten the followups from all contacts
  const allFollowups: FollowupDto[] = [];
  
  for (const contact of contacts) {
    for (let i = 1; i <= 6; i++) {
      const text = contact[`followup${i}_text` as keyof typeof contact] as string | null;
      const scheduledAt = contact[`followup${i}_scheduledAt` as keyof typeof contact] as Date | null;
      const status = contact[`followup${i}_status` as keyof typeof contact] as FollowupStatus | null;
      const sendViaExtension = contact[`followup${i}_sendViaExtension` as keyof typeof contact] as boolean;

      if (text) {
        // Apply status filter
        if (parsedInput.status && status !== parsedInput.status) {
          continue;
        }

        // Default to showing only PENDING if showSent is false and no specific status is set
        if (!parsedInput.showSent && !parsedInput.status && status !== 'PENDING') {
          continue;
        }

        // Apply search filter
        if (searchCriteria) {
          const matchesSearch = 
            contact.nickname?.toLowerCase().includes(parsedInput.searchQuery!.toLowerCase()) ||
            contact.instagramUserId.toLowerCase().includes(parsedInput.searchQuery!.toLowerCase()) ||
            text.toLowerCase().includes(parsedInput.searchQuery!.toLowerCase());
          
          if (!matchesSearch) {
            continue;
          }
        }

        allFollowups.push({
          id: `${contact.id}-${i}`,
          followupNumber: i,
          text: text,
          scheduledAt: scheduledAt || undefined,
          status: status || undefined,
          sendViaExtension: sendViaExtension,
          contactId: contact.id,
          contactNickname: contact.nickname || undefined,
          contactInstagramUserId: contact.instagramUserId,
          contactProfilePhoto: contact.profilePhoto || undefined,
          contactEngagementPriority: contact.engagementPriority
        });
      }
    }
  }

  const totalCount = allFollowups.length;

  // Sort the followups
  allFollowups.sort((a, b) => {
    let aValue, bValue;
    
    switch (parsedInput.sortBy) {
      case GetFollowupsSortBy.ScheduledAt:
        aValue = a.scheduledAt?.getTime() || 0;
        bValue = b.scheduledAt?.getTime() || 0;
        break;
      case GetFollowupsSortBy.Status:
        aValue = a.status || '';
        bValue = b.status || '';
        break;
      case GetFollowupsSortBy.ContactNickname:
        aValue = a.contactNickname || a.contactInstagramUserId;
        bValue = b.contactNickname || b.contactInstagramUserId;
        break;
      case GetFollowupsSortBy.SendViaExtension:
        aValue = a.sendViaExtension ? 1 : 0;
        bValue = b.sendViaExtension ? 1 : 0;
        break;
      case GetFollowupsSortBy.ContactEngagementPriority:
        aValue = a.contactEngagementPriority || '';
        bValue = b.contactEngagementPriority || '';
        break;
      case GetFollowupsSortBy.CreatedAt:
      default:
        aValue = a.scheduledAt?.getTime() || 0;
        bValue = b.scheduledAt?.getTime() || 0;
        break;
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const comparison = aValue.localeCompare(bValue);
      return parsedInput.sortDirection === 'desc' ? -comparison : comparison;
    }

    const comparison = (aValue as number) - (bValue as number);
    return parsedInput.sortDirection === 'desc' ? -comparison : comparison;
  });

  // Apply pagination
  const startIndex = parsedInput.pageIndex * parsedInput.pageSize;
  const paginatedFollowups = allFollowups.slice(startIndex, startIndex + parsedInput.pageSize);

  return {
    followups: paginatedFollowups,
    filteredCount: totalCount,
    totalCount: totalCount
  };
}