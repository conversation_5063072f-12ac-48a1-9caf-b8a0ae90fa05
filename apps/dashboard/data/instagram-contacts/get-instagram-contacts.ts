import 'server-only';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { ValidationError } from '@workspace/common/errors';
import { Prisma } from '@workspace/database';
import { prisma } from '@workspace/database/client';

import {
  getInstagramContactsSchema,
  type GetInstagramContactsSchema
} from '~/schemas/instagram-contacts/get-instagram-contacts-schema';
import type { InstagramContactDto } from '~/types/dtos/instagram-contact-dto';

export async function getInstagramContacts(input: GetInstagramContactsSchema): Promise<{
  instagramContacts: InstagramContactDto[];
  filteredCount: number;
  totalCount: number;
}> {
  const ctx = await getAuthOrganizationContext();

  const result = getInstagramContactsSchema.safeParse(input);
  if (!result.success) {
    throw new ValidationError(JSON.stringify(result.error.flatten()));
  }
  const parsedInput = result.data;

  const searchCriteria: Prisma.StringFilter | undefined =
    parsedInput.searchQuery
      ? { contains: parsedInput.searchQuery, mode: 'insensitive' }
      : undefined;

  // Handle stage filtering - support both single stage and multiple stages
  const stageFilter = parsedInput.stages && parsedInput.stages.length > 0
    ? { in: parsedInput.stages }
    : parsedInput.stage
    ? parsedInput.stage
    : undefined;

  // Handle date range filtering
  const firstActivityFilter = parsedInput.firstActivityDateRange ? {
    gte: parsedInput.firstActivityDateRange.from,
    ...(parsedInput.firstActivityDateRange.to && { 
      lte: new Date(parsedInput.firstActivityDateRange.to.getTime() + 24 * 60 * 60 * 1000 - 1) // End of day
    })
  } : undefined;

  const lastActivityFilter = parsedInput.lastActivityDateRange ? {
    gte: parsedInput.lastActivityDateRange.from,
    ...(parsedInput.lastActivityDateRange.to && { 
      lte: new Date(parsedInput.lastActivityDateRange.to.getTime() + 24 * 60 * 60 * 1000 - 1) // End of day
    })
  } : undefined;

  const [instagramContacts, filteredCount, totalCount] = await prisma.$transaction([
    prisma.instagramContact.findMany({
      skip: parsedInput.pageIndex * parsedInput.pageSize,
      take: parsedInput.pageSize,
      where: {
        organizationId: ctx.organization.id,
        stage: stageFilter,
        firstMessageAt: firstActivityFilter,
        lastMessageAt: lastActivityFilter,
        OR: searchCriteria
          ? [
              { nickname: searchCriteria },
              { instagramUserId: searchCriteria }
            ]
          : undefined
      },
      select: {
        id: true,
        instagramUserId: true,
        nickname: true,
        profilePhoto: true,
        notes: true,
        lastActivity: true,
        messageCount: true,
        firstMessageAt: true,
        lastMessageAt: true,
        lastContactMessageAt: true,
        aiEnabled: true,
        stage: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        [parsedInput.sortBy]: parsedInput.sortDirection
      }
    }),
    prisma.instagramContact.count({
      where: {
        organizationId: ctx.organization.id,
        stage: stageFilter,
        firstMessageAt: firstActivityFilter,
        lastMessageAt: lastActivityFilter,
        OR: searchCriteria
          ? [
              { nickname: searchCriteria },
              { instagramUserId: searchCriteria }
            ]
          : undefined
      }
    }),
    prisma.instagramContact.count({
      where: {
        organizationId: ctx.organization.id
      }
    })
  ]);

  const mapped: InstagramContactDto[] = instagramContacts.map((contact) => ({
    id: contact.id,
    instagramUserId: contact.instagramUserId,
    nickname: contact.nickname || undefined,
    profilePhoto: contact.profilePhoto || undefined,
    notes: contact.notes || undefined,
    lastActivity: contact.lastActivity || undefined,
    messageCount: contact.messageCount,
    firstMessageAt: contact.firstMessageAt || undefined,
    lastMessageAt: contact.lastMessageAt || undefined,
    lastContactMessageAt: contact.lastContactMessageAt || undefined,
    aiEnabled: contact.aiEnabled,
    stage: contact.stage,
    createdAt: contact.createdAt,
    updatedAt: contact.updatedAt
  }));

  return { instagramContacts: mapped, filteredCount, totalCount };
}