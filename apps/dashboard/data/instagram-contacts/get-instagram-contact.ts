import 'server-only';

import { notFound } from 'next/navigation';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { ValidationError } from '@workspace/common/errors';
import { prisma } from '@workspace/database/client';

import {
  getInstagramContactSchema,
  type GetInstagramContactSchema
} from '~/schemas/instagram-contacts/get-instagram-contact-schema';
import type { InstagramContactDto } from '~/types/dtos/instagram-contact-dto';

export async function getInstagramContact(
  input: GetInstagramContactSchema
): Promise<InstagramContactDto & { organizationLogo?: string }> {
  const ctx = await getAuthOrganizationContext();

  const result = getInstagramContactSchema.safeParse(input);
  if (!result.success) {
    throw new ValidationError(JSON.stringify(result.error.flatten()));
  }
  const parsedInput = result.data;

  const instagramContact = await prisma.instagramContact.findFirst({
    where: {
      organizationId: ctx.organization.id,
      id: parsedInput.id
    },
    select: {
      id: true,
      instagramUserId: true,
      nickname: true,
      profilePhoto: true,
      notes: true,
      lastActivity: true,
      messageCount: true,
      firstMessageAt: true,
      lastMessageAt: true,
      lastContactMessageAt: true,
      aiEnabled: true,
      stage: true,
      createdAt: true,
      updatedAt: true,
      followup1_text: true,
      followup1_scheduledAt: true,
      followup1_status: true,
      followup1_sendViaExtension: true,
      followup2_text: true,
      followup2_scheduledAt: true,
      followup2_status: true,
      followup2_sendViaExtension: true,
      followup3_text: true,
      followup3_scheduledAt: true,
      followup3_status: true,
      followup3_sendViaExtension: true,
      followup4_text: true,
      followup4_scheduledAt: true,
      followup4_status: true,
      followup4_sendViaExtension: true,
      followup5_text: true,
      followup5_scheduledAt: true,
      followup5_status: true,
      followup5_sendViaExtension: true,
      followup6_text: true,
      followup6_scheduledAt: true,
      followup6_status: true,
      followup6_sendViaExtension: true,
      organization: {
        select: {
          logo: true
        }
      }
    }
  });

  if (!instagramContact) {
    return notFound();
  }

  const response: InstagramContactDto & { organizationLogo?: string } = {
    id: instagramContact.id,
    instagramUserId: instagramContact.instagramUserId,
    nickname: instagramContact.nickname || undefined,
    profilePhoto: instagramContact.profilePhoto || undefined,
    notes: instagramContact.notes || undefined,
    lastActivity: instagramContact.lastActivity || undefined,
    messageCount: instagramContact.messageCount,
    firstMessageAt: instagramContact.firstMessageAt || undefined,
    lastMessageAt: instagramContact.lastMessageAt || undefined,
    lastContactMessageAt: instagramContact.lastContactMessageAt || undefined,
    aiEnabled: instagramContact.aiEnabled,
    stage: instagramContact.stage,
    createdAt: instagramContact.createdAt,
    updatedAt: instagramContact.updatedAt,
    // Follow-up fields
    followup1Text: instagramContact.followup1_text || undefined,
    followup1Time: instagramContact.followup1_scheduledAt || undefined,
    followup1Status: instagramContact.followup1_status || undefined,
    followup1Extension: instagramContact.followup1_sendViaExtension || undefined,
    followup2Text: instagramContact.followup2_text || undefined,
    followup2Time: instagramContact.followup2_scheduledAt || undefined,
    followup2Status: instagramContact.followup2_status || undefined,
    followup2Extension: instagramContact.followup2_sendViaExtension || undefined,
    followup3Text: instagramContact.followup3_text || undefined,
    followup3Time: instagramContact.followup3_scheduledAt || undefined,
    followup3Status: instagramContact.followup3_status || undefined,
    followup3Extension: instagramContact.followup3_sendViaExtension || undefined,
    followup4Text: instagramContact.followup4_text || undefined,
    followup4Time: instagramContact.followup4_scheduledAt || undefined,
    followup4Status: instagramContact.followup4_status || undefined,
    followup4Extension: instagramContact.followup4_sendViaExtension || undefined,
    followup5Text: instagramContact.followup5_text || undefined,
    followup5Time: instagramContact.followup5_scheduledAt || undefined,
    followup5Status: instagramContact.followup5_status || undefined,
    followup5Extension: instagramContact.followup5_sendViaExtension || undefined,
    followup6Text: instagramContact.followup6_text || undefined,
    followup6Time: instagramContact.followup6_scheduledAt || undefined,
    followup6Status: instagramContact.followup6_status || undefined,
    followup6Extension: instagramContact.followup6_sendViaExtension || undefined,
    organizationLogo: instagramContact.organization.logo || undefined
  };

  return response;
}