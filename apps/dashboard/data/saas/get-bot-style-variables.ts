import { cache } from 'react';
import { unstable_cache } from 'next/cache';

import { prisma } from '@workspace/database/client';
import type { BotStyleVariableDefinition, VariableGroup } from '~/lib/bot-style-variables';

async function _getBotStyleVariables(botStyleId: string) {
  const [variables, groups] = await Promise.all([
    prisma.botStyleVariable.findMany({
      where: { botStyleId },
      orderBy: [
        { order: 'asc' },
        { createdAt: 'asc' }
      ]
    }),
    prisma.botStyleVariableGroup.findMany({
      where: { botStyleId },
      orderBy: { order: 'asc' }
    })
  ]);

  // Map to our interface
  const variableDefinitions: BotStyleVariableDefinition[] = variables.map(v => ({
    id: v.id,
    botStyleId: v.botStyleId,
    groupId: v.groupId || undefined,
    name: v.name,
    displayName: v.displayName,
    type: v.type as any,
    required: v.required,
    helpText: v.helpText || undefined,
    defaultValue: v.defaultValue || undefined,
    config: v.config as any,
    order: v.order
  }));

  // Group variables
  const groupMap = new Map<string, VariableGroup>(groups.map(g => [g.id, {
    id: g.id,
    name: g.name,
    description: g.description,
    order: g.order,
    collapsible: g.collapsible,
    variables: [] as BotStyleVariableDefinition[]
  }]));

  // Add ungrouped variables group
  const ungroupedGroup: VariableGroup = {
    id: 'ungrouped',
    name: 'Other Variables',
    description: null,
    order: 999,
    collapsible: true,
    variables: []
  };

  // Distribute variables into groups
  for (const variable of variableDefinitions) {
    if (variable.groupId && groupMap.has(variable.groupId)) {
      groupMap.get(variable.groupId)!.variables.push(variable);
    } else {
      ungroupedGroup.variables.push(variable);
    }
  }

  const resultGroups = Array.from(groupMap.values()).sort((a, b) => a.order - b.order);
  
  // Only add ungrouped if it has variables
  if (ungroupedGroup.variables.length > 0) {
    resultGroups.push(ungroupedGroup);
  }

  return {
    variables: variableDefinitions,
    groups: resultGroups
  };
}

export const getBotStyleVariables = cache(
  (botStyleId: string) => unstable_cache(
    () => _getBotStyleVariables(botStyleId),
    [`bot-style-variables-${botStyleId}`],
    {
      tags: [`bot-style-variables-${botStyleId}`, 'bot-styles'],
      revalidate: 300 // 5 minutes
    }
  )()
);

async function _getOrganizationBotStyleVariableValues(organizationId: string, botStyleId: string) {
  const values = await prisma.organizationBotStyleVariable.findMany({
    where: {
      organizationId,
      botStyleId
    },
    include: {
      variable: true
    }
  });

  return values.reduce((acc, item) => {
    acc[item.variable.name] = item.value;
    return acc;
  }, {} as Record<string, any>);
}

export const getOrganizationBotStyleVariableValues = cache(
  (organizationId: string, botStyleId: string) => unstable_cache(
    () => _getOrganizationBotStyleVariableValues(organizationId, botStyleId),
    [`organization-bot-style-variables-${organizationId}-${botStyleId}`],
    {
      tags: [`organization-bot-style-variables-${organizationId}-${botStyleId}`, 'organization-prompts'],
      revalidate: 300 // 5 minutes
    }
  )()
);