import { cache } from 'react';
import { unstable_cache } from 'next/cache';

import { prisma } from '@workspace/database/client';

async function _getBotStyles() {
  const botStyles = await prisma.botStyle.findMany({
    orderBy: [
      { isDefault: 'desc' }, // Default styles first
      { createdAt: 'desc' }   // Then by creation date
    ]
  });
  return botStyles;
}

export const getBotStyles = cache(
  unstable_cache(_getBotStyles, ['bot-styles'], {
    tags: ['bot-styles'],
    revalidate: 300 // 5 minutes
  })
);