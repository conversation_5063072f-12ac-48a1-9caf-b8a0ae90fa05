import { cache } from 'react';
import { unstable_cache } from 'next/cache';

import { prisma } from '@workspace/database/client';

async function _getPlatformSettings() {
  const settings = await prisma.platformSettings.findFirst();
  return settings;
}

export const getPlatformSettings = cache(
  unstable_cache(_getPlatformSettings, ['platform-settings'], {
    tags: ['platform-settings'],
    revalidate: 300 // 5 minutes
  })
);