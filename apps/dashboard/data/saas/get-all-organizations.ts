import 'server-only';

import { unstable_cache as cache } from 'next/cache';
import { isPlatformAdmin } from '@workspace/auth/permissions';
import { getAuthContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { Caching, defaultRevalidateTimeInSeconds, UserCacheKey } from '~/data/caching';

export type OrganizationWithUsage = {
  id: string;
  name: string;
  slug: string;
  logo: string | null;
  memberCount: number;
  instagramConnected: boolean;
  instagramUsername: string | null;
  monthlyTokens: number;
  monthlyCachedTokens: number;
  monthlyCost: number;
  monthlyRequests: number;
};

export type GetOrganizationsOptions = {
  searchQuery?: string;
  dateFrom?: Date;
  dateTo?: Date;
  sortBy?: 'name' | 'memberCount' | 'monthlyCost' | 'monthlyRequests';
  sortDirection?: 'asc' | 'desc';
  pageIndex?: number;
  pageSize?: number;
};

export type GetOrganizationsResult = {
  organizations: OrganizationWithUsage[];
  totalCount: number;
  filteredCount?: number;
};

export async function getAllOrganizationsWithUsage(options: GetOrganizationsOptions = {}): Promise<GetOrganizationsResult> {
  const ctx = await getAuthContext();
  
  // Check if user is platform admin
  const userIsPlatformAdmin = await isPlatformAdmin(ctx.session.user.id);
  if (!userIsPlatformAdmin) {
    throw new Error('Access denied: Platform admin required');
  }

  return cache(
    async () => {
      const {
        searchQuery,
        dateFrom,
        dateTo,
        sortBy = 'name',
        sortDirection = 'asc',
        pageIndex = 0,
        pageSize = 50
      } = options;

      // Default to current month if no date range provided
      const now = new Date();
      const defaultDateFrom = dateFrom || new Date(now.getFullYear(), now.getMonth(), 1);
      const defaultDateTo = dateTo || new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

      // Build where clause for search
      const whereClause: any = {};
      if (searchQuery) {
        whereClause.OR = [
          { name: { contains: searchQuery, mode: 'insensitive' } },
          { slug: { contains: searchQuery, mode: 'insensitive' } }
        ];
      }

      // Get total count before pagination
      const totalCount = await prisma.organization.count({
        where: whereClause
      });

      // Build sort order
      const orderBy: any = {};
      if (sortBy === 'name') {
        orderBy.name = sortDirection;
      } else if (sortBy === 'memberCount') {
        orderBy.memberships = { _count: sortDirection };
      }
      // For monthlyCost and monthlyRequests, we'll sort in memory after calculation

      const organizations = await prisma.organization.findMany({
        where: whereClause,
        select: {
          id: true,
          name: true,
          slug: true,
          logo: true,
          instagramIsConnected: true,
          instagramUsername: true,
          _count: {
            select: {
              memberships: true
            }
          },
          aiUsage: {
            where: {
              createdAt: {
                gte: defaultDateFrom,
                lte: defaultDateTo
              }
            },
            select: {
              totalTokens: true,
              cachedTokens: true,
              totalCost: true
            }
          }
        },
        ...(sortBy === 'name' || sortBy === 'memberCount' ? { orderBy } : {})
      });

      let processedOrganizations = organizations.map((org): OrganizationWithUsage => {
        const monthlyTokens = org.aiUsage.reduce((sum: number, usage: any) => sum + usage.totalTokens, 0);
        const monthlyCachedTokens = org.aiUsage.reduce((sum: number, usage: any) => sum + usage.cachedTokens, 0);
        const monthlyCost = org.aiUsage.reduce((sum: number, usage: any) => sum + Number(usage.totalCost), 0);
        const monthlyRequests = org.aiUsage.length;

        return {
          id: org.id,
          name: org.name,
          slug: org.slug,
          logo: org.logo,
          memberCount: org._count.memberships,
          instagramConnected: org.instagramIsConnected,
          instagramUsername: org.instagramUsername,
          monthlyTokens,
          monthlyCachedTokens,
          monthlyCost,
          monthlyRequests
        };
      });

      // Sort by calculated fields if needed
      if (sortBy === 'monthlyCost' || sortBy === 'monthlyRequests') {
        processedOrganizations.sort((a, b) => {
          const aValue = sortBy === 'monthlyCost' ? a.monthlyCost : a.monthlyRequests;
          const bValue = sortBy === 'monthlyCost' ? b.monthlyCost : b.monthlyRequests;
          
          if (sortDirection === 'desc') {
            return bValue - aValue;
          }
          return aValue - bValue;
        });
      }

      const filteredCount = processedOrganizations.length;
      
      // Apply pagination
      const startIndex = pageIndex * pageSize;
      const paginatedOrganizations = processedOrganizations.slice(startIndex, startIndex + pageSize);

      return {
        organizations: paginatedOrganizations,
        totalCount,
        filteredCount
      };
    },
    Caching.createUserKeyParts(UserCacheKey.Organizations, ctx.session.user.id, 'all-with-usage'),
    {
      revalidate: defaultRevalidateTimeInSeconds,
      tags: ['organizations-usage']
    }
  )();
}