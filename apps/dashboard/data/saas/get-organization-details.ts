import 'server-only';

import { unstable_cache as cache } from 'next/cache';
import { isPlatformAdmin } from '@workspace/auth/permissions';
import { getAuthContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { Caching, defaultRevalidateTimeInSeconds, UserCacheKey } from '~/data/caching';

export type OrganizationDetails = {
  id: string;
  name: string;
  slug: string;
  logo: string | null;
  email: string | null;
  
  // Instagram connection
  instagramConnected: boolean;
  instagramUsername: string | null;
  instagramName: string | null;
  instagramConnectedAt: Date | null;
  
  // Billing info
  billingCustomerId: string | null;
  billingEmail: string | null;
  
  // Members
  members: {
    id: string;
    name: string;
    email: string | null;
    role: string;
    isOwner: boolean;
    joinedAt: Date;
  }[];
  
  // Usage analytics
  monthlyUsage: {
    totalTokens: number;
    promptTokens: number;
    completionTokens: number;
    reasoningTokens: number;
    cachedTokens: number;
    audioTokens: number;
    totalCost: number;
    requestCount: number;
    breakdown: {
      provider: string;
      model: string;
      tokens: number;
      promptTokens: number;
      completionTokens: number;
      reasoningTokens: number;
      cachedTokens: number;
      audioTokens: number;
      cost: number;
      requests: number;
    }[];
  };
  
  // Historical data (last 6 months)
  historicalUsage: {
    month: string;
    tokens: number;
    cost: number;
    requests: number;
  }[];
};

export async function getOrganizationDetails(organizationId: string): Promise<OrganizationDetails> {
  const ctx = await getAuthContext();
  
  // Check if user is platform admin
  const userIsPlatformAdmin = await isPlatformAdmin(ctx.session.user.id);
  if (!userIsPlatformAdmin) {
    throw new Error('Access denied: Platform admin required');
  }

  return cache(
    async () => {
      const organization = await prisma.organization.findUnique({
        where: { id: organizationId },
        select: {
          id: true,
          name: true,
          slug: true,
          logo: true,
          email: true,
          instagramIsConnected: true,
          instagramUsername: true,
          instagramName: true,
          instagramConnectedAt: true,
          billingCustomerId: true,
          billingEmail: true,
          memberships: {
            select: {
              id: true,
              role: true,
              isOwner: true,
              createdAt: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          },
          aiUsage: {
            select: {
              totalTokens: true,
              promptTokens: true,
              completionTokens: true,
              reasoningTokens: true,
              cachedTokens: true,
              audioTokens: true,
              totalCost: true,
              provider: true,
              model: true,
              createdAt: true
            }
          }
        }
      });

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Calculate current month usage
      const now = new Date();
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const currentMonthUsage = organization.aiUsage.filter(
        (usage: any) => usage.createdAt >= monthStart
      );

      const breakdownMap: Record<string, {
        provider: string;
        model: string;
        tokens: number;
        promptTokens: number;
        completionTokens: number;
        reasoningTokens: number;
        cachedTokens: number;
        audioTokens: number;
        cost: number;
        requests: number;
      }> = {};

      currentMonthUsage.forEach((usage: any) => {
        const key = `${usage.provider}-${usage.model}`;
        if (!breakdownMap[key]) {
          breakdownMap[key] = {
            provider: usage.provider,
            model: usage.model,
            tokens: 0,
            promptTokens: 0,
            completionTokens: 0,
            reasoningTokens: 0,
            cachedTokens: 0,
            audioTokens: 0,
            cost: 0,
            requests: 0
          };
        }
        breakdownMap[key].tokens += usage.totalTokens;
        breakdownMap[key].promptTokens += usage.promptTokens;
        breakdownMap[key].completionTokens += usage.completionTokens;
        breakdownMap[key].reasoningTokens += usage.reasoningTokens;
        breakdownMap[key].cachedTokens += usage.cachedTokens;
        breakdownMap[key].audioTokens += usage.audioTokens;
        breakdownMap[key].cost += Number(usage.totalCost);
        breakdownMap[key].requests += 1;
      });

      const monthlyUsage = {
        totalTokens: currentMonthUsage.reduce((sum: number, usage: any) => sum + usage.totalTokens, 0),
        promptTokens: currentMonthUsage.reduce((sum: number, usage: any) => sum + usage.promptTokens, 0),
        completionTokens: currentMonthUsage.reduce((sum: number, usage: any) => sum + usage.completionTokens, 0),
        reasoningTokens: currentMonthUsage.reduce((sum: number, usage: any) => sum + usage.reasoningTokens, 0),
        cachedTokens: currentMonthUsage.reduce((sum: number, usage: any) => sum + usage.cachedTokens, 0),
        audioTokens: currentMonthUsage.reduce((sum: number, usage: any) => sum + usage.audioTokens, 0),
        totalCost: currentMonthUsage.reduce((sum: number, usage: any) => sum + Number(usage.totalCost), 0),
        requestCount: currentMonthUsage.length,
        breakdown: Object.values(breakdownMap)
      };

      // Calculate historical data (last 6 months)
      const historicalUsage: OrganizationDetails['historicalUsage'] = [];
      for (let i = 5; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const nextMonth = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
        
        const monthUsage = organization.aiUsage.filter(
          (usage: any) => usage.createdAt >= date && usage.createdAt < nextMonth
        );

        historicalUsage.push({
          month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          tokens: monthUsage.reduce((sum: number, usage: any) => sum + usage.totalTokens, 0),
          cost: monthUsage.reduce((sum: number, usage: any) => sum + Number(usage.totalCost), 0),
          requests: monthUsage.length
        });
      }

      return {
        id: organization.id,
        name: organization.name,
        slug: organization.slug,
        logo: organization.logo,
        email: organization.email,
        instagramConnected: organization.instagramIsConnected,
        instagramUsername: organization.instagramUsername,
        instagramName: organization.instagramName,
        instagramConnectedAt: organization.instagramConnectedAt,
        billingCustomerId: organization.billingCustomerId,
        billingEmail: organization.billingEmail,
        members: organization.memberships.map((membership: any) => ({
          id: membership.user.id,
          name: membership.user.name,
          email: membership.user.email,
          role: membership.role,
          isOwner: membership.isOwner,
          joinedAt: membership.createdAt
        })),
        monthlyUsage,
        historicalUsage
      };
    },
    Caching.createUserKeyParts(UserCacheKey.Organizations, ctx.session.user.id, `details-${organizationId}`),
    {
      revalidate: defaultRevalidateTimeInSeconds,
      tags: [`organization-details-${organizationId}`]
    }
  )();
}