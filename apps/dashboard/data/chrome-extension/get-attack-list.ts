import 'server-only';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { ValidationError } from '@workspace/common/errors';
import { Prisma } from '@workspace/database';
import { prisma } from '@workspace/database/client';

import {
  getAttackListSchema,
  type GetAttackListSchema
} from '~/schemas/chrome-extension/get-attack-list-schema';
import type { AttackListItemDto } from '~/types/dtos/attack-list-dto';

export async function getAttackList(input: GetAttackListSchema): Promise<{
  attackList: AttackListItemDto[];
  filteredCount: number;
  totalCount: number;
}> {
  const ctx = await getAuthOrganizationContext();

  const result = getAttackListSchema.safeParse(input);
  if (!result.success) {
    throw new ValidationError(JSON.stringify(result.error.flatten()));
  }
  const parsedInput = result.data;

  const searchCriteria: Prisma.StringFilter | undefined =
    parsedInput.searchQuery
      ? { contains: parsedInput.searchQuery, mode: 'insensitive' }
      : undefined;

  // Determine status filter based on showSent parameter
  const statusFilter = parsedInput.showSent 
    ? parsedInput.status // If showing sent, use the status filter as-is
    : parsedInput.status || 'pending'; // Default to pending if not showing sent

  const [attackList, filteredCount, totalCount] = await prisma.$transaction([
    prisma.chromeExtensionAttackList.findMany({
      skip: parsedInput.pageIndex * parsedInput.pageSize,
      take: parsedInput.pageSize,
      where: {
        organizationId: ctx.organization.id,
        status: statusFilter,
        priority: parsedInput.priority ? parseInt(parsedInput.priority) : undefined,
        OR: searchCriteria
          ? [
              { nickname: searchCriteria },
              { instagramUserId: searchCriteria }
            ]
          : undefined
      },
      select: {
        id: true,
        instagramUserId: true,
        nickname: true,
        profilePhoto: true,
        priority: true,
        messageContent: true,
        messageSource: true,
        scheduledAt: true,
        status: true,
        sentAt: true,
        createdAt: true,
        messageBatch: {
          select: {
            name: true
          }
        }
      },
      orderBy: [
        { priority: 'asc' }, // 1 first (new followers), then 2 (followups), then 3 (non-responders)
        { scheduledAt: 'desc' } // Within each priority, latest scheduled first
      ]
    }),
    prisma.chromeExtensionAttackList.count({
      where: {
        organizationId: ctx.organization.id,
        status: statusFilter,
        priority: parsedInput.priority ? parseInt(parsedInput.priority) : undefined,
        OR: searchCriteria
          ? [
              { nickname: searchCriteria },
              { instagramUserId: searchCriteria }
            ]
          : undefined
      }
    }),
    prisma.chromeExtensionAttackList.count({
      where: {
        organizationId: ctx.organization.id
      }
    })
  ]);

  const mapped: AttackListItemDto[] = attackList.map((item) => ({
    id: item.id,
    instagramUserId: item.instagramUserId,
    nickname: item.nickname || undefined,
    profilePhoto: item.profilePhoto || undefined,
    priority: item.priority,
    messageContent: item.messageContent as string[],
    messageSource: item.messageSource,
    name: item.messageBatch?.name || undefined,
    scheduledAt: item.scheduledAt,
    status: item.status as 'pending' | 'sent' | 'failed',
    sentAt: item.sentAt || undefined,
    createdAt: item.createdAt
  }));

  return { attackList: mapped, filteredCount, totalCount };
}