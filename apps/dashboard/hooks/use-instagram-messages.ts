'use client';

import { useState, useCallback } from 'react';

export type Message = {
  id: string;
  content: string;
  from: 'user' | 'assistant';
  timestamp: Date;
  messageType: string;
  attachmentUrl?: string;
  aiReasoning?: string;
};

type UseInstagramMessagesReturn = {
  messages: Message[];
  loading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  error: string | null;
  loadMoreMessages: () => Promise<void>;
  refreshMessages: () => Promise<void>;
  addMessage: (message: Message) => void;
  totalCount: number;
};

export function useInstagramMessages(conversationId: string | null, initialMessages: Message[] = []): UseInstagramMessagesReturn {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalCount, setTotalCount] = useState(0);

  const loadMessages = useCallback(async (page: number, append: boolean = false) => {
    if (!conversationId) return;

    try {
      if (page === 0) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }
      setError(null);

      const response = await fetch(
        `/api/instagram/messages?conversationId=${conversationId}&page=${page}&pageSize=20`
      );

      if (!response.ok) {
        throw new Error('Failed to load messages');
      }

      const data = await response.json();
      
      if (append) {
        // Append older messages to the end (they come in DESC order from API)
        setMessages(prev => [...prev, ...data.messages]);
      } else {
        // Replace all messages (initial load - they come in DESC order from API)
        setMessages(data.messages);
      }

      setHasMore(data.hasMore);
      setTotalCount(data.totalCount);
      setCurrentPage(page);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load messages');
      console.error('❌ Error loading messages:', err);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [conversationId]);

  const loadMoreMessages = useCallback(async () => {
    if (!hasMore || loadingMore || loading) return;
    
    const nextPage = currentPage + 1;
    await loadMessages(nextPage, true);
  }, [hasMore, loadingMore, loading, currentPage, loadMessages]);

  const refreshMessages = useCallback(async () => {
    setCurrentPage(0);
    await loadMessages(0, false);
  }, [loadMessages]);

  const addMessage = useCallback((message: Message) => {
    setMessages(prev => [...prev, message]);
  }, []);

  return {
    messages,
    loading,
    loadingMore,
    hasMore,
    error,
    loadMoreMessages,
    refreshMessages,
    addMessage,
    totalCount
  };
}