import { NextRequest, NextResponse } from 'next/server';
import { slackOAuthService } from '@workspace/ai';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { env } from '~/env';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    // Handle OAuth error
    if (error) {
      console.error('❌ Slack OAuth error:', error);
      const url = new URL('/integrations/slack', env.NEXT_PUBLIC_DASHBOARD_URL);
      url.searchParams.set('slack', 'error');
      return NextResponse.redirect(url);
    }

    // Validate required parameters
    if (!code || !state) {
      console.error('❌ Missing code or state parameter');
      const url = new URL('/integrations/slack', env.NEXT_PUBLIC_DASHBOARD_URL);
      url.searchParams.set('slack', 'error');
      return NextResponse.redirect(url);
    }

    // Get user session and organization
    const session = await auth();
    if (!session?.user?.id) {
      console.error('❌ No authenticated user found');
      const url = new URL('/auth/signin', request.url);
      return NextResponse.redirect(url);
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: { userId: session.user.id },
      include: { organization: true },
    });

    if (!membership?.organizationId) {
      console.error('❌ No organization found for user');
      const url = new URL('/integrations/slack', env.NEXT_PUBLIC_DASHBOARD_URL);
      url.searchParams.set('slack', 'error');
      return NextResponse.redirect(url);
    }

    // Exchange code for access token using manual OAuth flow
    const result = await slackOAuthService.exchangeOAuthCode(code, membership.organizationId);
    
    if (result.success) {
      const url = new URL(`/organizations/${membership.organization.slug}/integrations/slack`, env.NEXT_PUBLIC_DASHBOARD_URL);
      url.searchParams.set('slack', 'success');
      return NextResponse.redirect(url);
    } else {
      console.error('❌ OAuth exchange failed:', result.error);
      const url = new URL(`/organizations/${membership.organization.slug}/integrations/slack`, env.NEXT_PUBLIC_DASHBOARD_URL);
      url.searchParams.set('slack', 'error');
      return NextResponse.redirect(url);
    }
  } catch (error) {
    console.error('❌ Error handling Slack OAuth callback:', error);
    
    const url = new URL('/integrations/slack', env.NEXT_PUBLIC_DASHBOARD_URL);
    url.searchParams.set('slack', 'error');
    return NextResponse.redirect(url);
  }
}