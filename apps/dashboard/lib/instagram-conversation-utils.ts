/**
 * Utility functions for Instagram conversations
 * Simple KISS approach for conversation ID management
 */

import 'server-only';

import { prisma } from '@workspace/database/client';

/**
 * Get conversation ID for an Instagram contact
 * This checks recent messages to find the conversation ID
 */
export async function getConversationIdForContact(
  instagramUserId: string,
  organizationId: string
): Promise<string | null> {
  // Look for any recent message with this Instagram user to get the conversation ID
  const recentMessage = await prisma.instagramMessage.findFirst({
    where: {
      organizationId,
      instagramUserId
    },
    select: {
      conversationId: true
    },
    orderBy: {
      instagramCreatedTime: 'desc'
    }
  });

  return recentMessage?.conversationId || null;
}

/**
 * Generate a conversation ID if none exists
 * Simple fallback for contacts without messages yet
 */
export function generateConversationId(
  instagramUserId: string,
  businessInstagramId: string
): string {
  // Simple deterministic conversation ID generation
  const ids = [instagramUserId, businessInstagramId].sort();
  return `conversation_${ids[0]}_${ids[1]}`;
}