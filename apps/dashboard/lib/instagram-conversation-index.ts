/**
 * Simple Instagram Conversation INDEX with Auto-Incremental Updates (KISS)
 * 
 * Automatically gathers conversation IDs and participant info as a lookup index.
 * Smart incremental updates: only gathers new conversations, stops when finding existing ones.
 */

import { prisma } from '@workspace/database/client';

// Simple logger
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[CONV-INDEX] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[CONV-INDEX] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[CONV-INDEX] ${message}`, ...args)
};

interface InstagramConversation {
  id: string;
  updated_time?: string;
  participants?: {
    data: Array<{
      id: string;
      username?: string;
    }>;
  };
}

interface InstagramResponse {
  data: InstagramConversation[];
  paging?: {
    next?: string;
  };
}

/**
 * Auto-gather conversation index - handles both initial and incremental gathering
 */
export async function autoGatherConversationIndex(
  accessToken: string,
  organizationId: string
): Promise<{ success: boolean; newConversations: number; message: string }> {
  const startTime = Date.now();
  
  try {
    logger.info(`🚀 Starting auto-gather for organization ${organizationId}`);

    // Get business account info to filter it out
    const businessAccountResponse = await fetch(`https://graph.instagram.com/v23.0/me?fields=id,username&access_token=${accessToken}`);
    if (!businessAccountResponse.ok) {
      throw new Error(`Failed to get business account info: ${businessAccountResponse.statusText}`);
    }
    
    const businessAccount = await businessAccountResponse.json();
    const businessAccountId = businessAccount.id;
    const businessAccountUsername = businessAccount.username;
    logger.info(`📱 Business account: @${businessAccountUsername} (ID: ${businessAccountId})`);

    // Check if we have existing conversations (determines if this is initial or incremental)
    const existingCount = await prisma.instagramConversationIndex.count({
      where: { organizationId }
    });

    const isInitialGather = existingCount === 0;
    logger.info(`${isInitialGather ? '🆕 Initial' : '🔄 Incremental'} gather - existing conversations: ${existingCount}`);

    // Fetch conversations from Instagram API (newest first for incremental efficiency)
    const conversationsUrl = `https://graph.instagram.com/v23.0/me/conversations?fields=id,participants.limit(2){id,username},updated_time&access_token=${accessToken}&limit=50`;
    
    let totalNewConversations = 0;
    let nextUrl: string | undefined = conversationsUrl;
    let shouldStop = false;
    let pageCount = 0;


    while (nextUrl && !shouldStop) {
      pageCount++;
      logger.info(`📄 Processing page ${pageCount}`);
      
      const response = await fetch(nextUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch conversations: ${response.statusText}`);
      }

      const data: InstagramResponse = await response.json();
      const conversations = data.data || [];
      
      logger.info(`📥 Page ${pageCount}: Fetched ${conversations.length} conversations`);

      // For incremental gathering, check what's new vs existing on this page
      let existingConversationsOnPage = 0;
      let newConversationsOnPage = 0;
      let existingConversationIds: Set<string> = new Set();

      // First pass: check what's new vs existing on this page
      if (!isInitialGather) {
        const conversationIds = conversations.map(c => c.id);
        const existingOnThisPage = await prisma.instagramConversationIndex.findMany({
          where: {
            organizationId,
            conversationId: { in: conversationIds }
          },
          select: { conversationId: true }
        });
        
        existingConversationsOnPage = existingOnThisPage.length;
        newConversationsOnPage = conversations.length - existingConversationsOnPage;
        existingConversationIds = new Set(existingOnThisPage.map(e => e.conversationId));
        
        logger.info(`📊 Page ${pageCount} analysis: ${newConversationsOnPage} new, ${existingConversationsOnPage} existing`);
        
        // Stop ONLY if ALL conversations on this page already exist (we've caught up to old conversations)
        if (existingConversationsOnPage === conversations.length && conversations.length > 0) {
          logger.info(`🛑 All ${conversations.length} conversations on page ${pageCount} already exist - stopping incremental gather`);
          shouldStop = true;
        }
      }

      // Process conversations (only add new ones)
      for (const conversation of conversations) {
        // For incremental gathering, skip if conversation already exists (use cached result)
        if (!isInitialGather && existingConversationIds.has(conversation.id)) {
          continue; // Skip existing conversation
        }

        // Extract participant info (filter out business account by username)
        const participants = conversation.participants?.data || [];
        const customerParticipant = participants.find(p => 
          p.username !== businessAccountUsername && p.username
        );

        if (customerParticipant) {
          // Add to index
          await prisma.instagramConversationIndex.create({
            data: {
              organizationId,
              conversationId: conversation.id,
              participantId: customerParticipant.id,
              participantUsername: customerParticipant.username || null,
              lastUpdated: conversation.updated_time ? new Date(conversation.updated_time) : new Date()
            }
          });

          totalNewConversations++;
          logger.info(`✅ Added conversation with @${customerParticipant.username}`);
        }
      }

      // Log page completion
      logger.info(`📋 Page ${pageCount} complete: ${totalNewConversations} total new conversations so far`);
      
      // Continue to next page if not stopping
      nextUrl = !shouldStop ? data.paging?.next : undefined;
      
      if (shouldStop) {
        logger.info(`🛑 Pagination stopped at page ${pageCount}`);
      } else if (!nextUrl) {
        logger.info(`🏁 Pagination complete at page ${pageCount}`);
      }
    }

    const totalTime = Date.now() - startTime;
    const message = `${isInitialGather ? 'Initial' : 'Incremental'} gather completed: ${totalNewConversations} new conversations from ${pageCount} pages in ${totalTime}ms`;
    
    logger.info(`🎉 ${message}`);

    return {
      success: true,
      newConversations: totalNewConversations,
      message
    };

  } catch (error) {
    const totalTime = Date.now() - startTime;
    const errorMessage = `Auto-gather failed after ${totalTime}ms: ${error instanceof Error ? error.message : 'Unknown error'}`;
    
    logger.error(`❌ ${errorMessage}`, error);

    return {
      success: false,
      newConversations: 0,
      message: errorMessage
    };
  }
}

/**
 * Get conversation ID for specific user (for future conversation fetching)
 */
export async function getConversationIdForUser(
  organizationId: string,
  username: string
): Promise<string | null> {
  const conversation = await prisma.instagramConversationIndex.findFirst({
    where: {
      organizationId,
      participantUsername: username
    },
    select: {
      conversationId: true
    }
  });

  return conversation?.conversationId || null;
}

/**
 * Check if organization has conversation with specific user
 */
export async function hasConversationWith(
  organizationId: string,
  username: string
): Promise<boolean> {
  const conversation = await getConversationIdForUser(organizationId, username);
  return conversation !== null;
}

/**
 * Get index statistics
 */
export async function getIndexStats(organizationId: string): Promise<{
  totalConversations: number;
  lastGatheredAt?: Date;
}> {
  const count = await prisma.instagramConversationIndex.count({
    where: { organizationId }
  });

  const latest = await prisma.instagramConversationIndex.findFirst({
    where: { organizationId },
    orderBy: { createdAt: 'desc' },
    select: { createdAt: true }
  });

  return {
    totalConversations: count,
    lastGatheredAt: latest?.createdAt
  };
}

/**
 * Background trigger for auto-gathering (non-blocking)
 */
export function triggerAutoGatherInBackground(
  accessToken: string,
  organizationId: string
): void {
  logger.info(`🎯 Triggering background conversation index gathering for org ${organizationId}`);
  
  setImmediate(async () => {
    const result = await autoGatherConversationIndex(accessToken, organizationId);
    
    if (result.success) {
      logger.info(`🎉 Background gathering completed: ${result.message}`);
    } else {
      logger.error(`❌ Background gathering failed: ${result.message}`);
    }
  });
}