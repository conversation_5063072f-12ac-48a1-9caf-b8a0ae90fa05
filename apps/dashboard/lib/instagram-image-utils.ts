/**
 * Instagram image utilities for downloading and converting profile pictures
 */

export async function downloadImageAsBase64(imageUrl: string): Promise<string | null> {
  try {
    console.log(`📥 Downloading Instagram profile picture: ${imageUrl}`);
    
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (!response.ok) {
      console.error(`Failed to download image: ${response.status} ${response.statusText}`);
      return null;
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.startsWith('image/')) {
      console.error(`Invalid content type: ${contentType}`);
      return null;
    }

    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Convert to base64 with proper data URI format
    const base64Data = buffer.toString('base64');
    const dataUri = `data:${contentType};base64,${base64Data}`;
    
    console.log(`✅ Successfully downloaded and converted image (${buffer.length} bytes)`);
    return dataUri;
  } catch (error) {
    console.error('Error downloading Instagram profile picture:', error);
    return null;
  }
}