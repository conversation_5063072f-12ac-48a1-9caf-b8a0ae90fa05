// Bot Style Variables - Types and Utilities
// <PERSON>, I got you! Clean types for the dynamic variable system.

export type VariableType = 
  | 'text'
  | 'textarea'
  | 'dropdown'
  | 'array'
  | 'structured_list';

// Configuration for different variable types
export interface DropdownConfig {
  options: string[];
  allowCustom?: boolean;
  placeholder?: string;
}

export interface StructuredListColumn {
  name: string;
  displayName: string;
  type: 'text' | 'textarea' | 'number';
  required?: boolean;
  placeholder?: string;
}

export interface StructuredListConfig {
  columns: StructuredListColumn[];
  minItems?: number;
  maxItems?: number;
  addButtonText?: string;
}

export interface ArrayConfig {
  minItems?: number;
  maxItems?: number;
  placeholder?: string;
  addButtonText?: string;
}

export type VariableConfig = 
  | DropdownConfig
  | StructuredListConfig
  | ArrayConfig
  | Record<string, never>; // For simple types like text/textarea

// Variable definition interface
export interface BotStyleVariableDefinition {
  id: string;
  botStyleId: string;
  groupId?: string;
  name: string;
  displayName: string;
  type: VariableType;
  required: boolean;
  helpText?: string;
  defaultValue?: string;
  config?: VariableConfig;
  order: number;
}

// Variable group definition
export interface VariableGroup {
  id: string;
  name: string;
  description?: string | null;
  order: number;
  collapsible: boolean;
  variables: BotStyleVariableDefinition[];
}

// Variable values (what organizations fill in)
export type VariableValue = 
  | string                           // text, textarea, dropdown
  | string[]                         // array
  | Record<string, any>[]            // structured_list
  | Record<string, any>;             // complex types

export interface BotStyleVariableValue {
  variableId: string;
  variableName: string;
  value: VariableValue;
}

// Form data structures
export interface VariableFormData {
  [variableName: string]: VariableValue;
}

// Predefined variable templates
export const VARIABLE_TEMPLATES = {
  BASIC_INFO: {
    name: 'Basic Info',
    description: 'Core business information',
    variables: [
      {
        name: 'company_name',
        displayName: 'Company Name',
        type: 'text' as VariableType,
        required: true,
        helpText: 'Your business or organization name'
      },
      {
        name: 'about_us',
        displayName: 'About Us',
        type: 'textarea' as VariableType,
        required: false,
        helpText: 'Brief description of your business'
      }
    ]
  },
  PRODUCTS: {
    name: 'Products & Services',
    description: 'Your offerings and pricing',
    variables: [
      {
        name: 'products_list',
        displayName: 'Products',
        type: 'structured_list' as VariableType,
        required: false,
        helpText: 'List your products or services',
        config: {
          columns: [
            { name: 'name', displayName: 'Product Name', type: 'text', required: true },
            { name: 'price', displayName: 'Price', type: 'text', required: true },
            { name: 'link', displayName: 'Link', type: 'text', required: false },
            { name: 'description', displayName: 'Description', type: 'textarea', required: false }
          ],
          minItems: 1,
          maxItems: 10,
          addButtonText: 'Add Product'
        } as StructuredListConfig
      }
    ]
  },
  MESSAGING: {
    name: 'Messaging',
    description: 'Brand voice and key messages',
    variables: [
      {
        name: 'tone',
        displayName: 'Communication Tone',
        type: 'dropdown' as VariableType,
        required: true,
        helpText: 'How should the AI communicate?',
        config: {
          options: ['Professional', 'Friendly', 'Casual', 'Expert', 'Aggressive'],
          placeholder: 'Select tone...'
        } as DropdownConfig
      },
      {
        name: 'key_problems',
        displayName: 'Key Problems You Solve',
        type: 'array' as VariableType,
        required: false,
        helpText: 'Main pain points your business addresses',
        config: {
          maxItems: 10,
          placeholder: 'Enter a problem...',
          addButtonText: 'Add Problem'
        } as ArrayConfig
      }
    ]
  }
} as const;

// Utility functions
export function getVariablesByGroup(variables: BotStyleVariableDefinition[]): VariableGroup[] {
  const grouped = variables.reduce((acc, variable) => {
    const groupId = variable.groupId || 'ungrouped';
    if (!acc[groupId]) {
      acc[groupId] = {
        id: groupId,
        name: groupId === 'ungrouped' ? 'Other Variables' : 'Unknown Group',
        order: 999,
        collapsible: true,
        variables: []
      };
    }
    acc[groupId].variables.push(variable);
    return acc;
  }, {} as Record<string, VariableGroup>);

  return Object.values(grouped).sort((a, b) => a.order - b.order);
}

export function validateVariableValue(
  variable: BotStyleVariableDefinition,
  value: VariableValue
): { isValid: boolean; error?: string } {
  // Required field validation
  if (variable.required && (!value || (Array.isArray(value) && value.length === 0))) {
    return { isValid: false, error: `${variable.displayName} is required` };
  }

  // Type-specific validation
  switch (variable.type) {
    case 'text':
    case 'textarea':
      if (value && typeof value !== 'string') {
        return { isValid: false, error: `${variable.displayName} must be text` };
      }
      break;

    case 'dropdown':
      if (value && typeof value !== 'string') {
        return { isValid: false, error: `${variable.displayName} must be a single selection` };
      }
      // Validate against options if config exists
      const dropdownConfig = variable.config as DropdownConfig;
      if (dropdownConfig?.options && value && !dropdownConfig.options.includes(value as string)) {
        if (!dropdownConfig.allowCustom) {
          return { isValid: false, error: `${variable.displayName} must be one of the predefined options` };
        }
      }
      break;

    case 'array':
      if (value && !Array.isArray(value)) {
        return { isValid: false, error: `${variable.displayName} must be a list` };
      }
      const arrayConfig = variable.config as ArrayConfig;
      if (arrayConfig?.maxItems && Array.isArray(value) && value.length > arrayConfig.maxItems) {
        return { isValid: false, error: `${variable.displayName} cannot have more than ${arrayConfig.maxItems} items` };
      }
      break;

    case 'structured_list':
      if (value && !Array.isArray(value)) {
        return { isValid: false, error: `${variable.displayName} must be a list of items` };
      }
      // Additional validation for structured list could be added here
      break;
  }

  return { isValid: true };
}

export function getDefaultValueForVariable(variable: BotStyleVariableDefinition): VariableValue {
  if (variable.defaultValue) {
    try {
      return JSON.parse(variable.defaultValue);
    } catch {
      return variable.defaultValue;
    }
  }

  switch (variable.type) {
    case 'text':
    case 'textarea':
    case 'dropdown':
      return '';
    case 'array':
      return [];
    case 'structured_list':
      return [];
    default:
      return '';
  }
}