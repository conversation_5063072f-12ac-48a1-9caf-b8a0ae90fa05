import { NextResponse } from 'next/server';

/**
 * Add CORS headers for Chrome Extension API routes
 * Allows Chrome extensions to access the API from any extension ID
 */
export function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*', // Allow all origins including chrome-extension://
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400', // Cache preflight for 24 hours
  };
}

/**
 * Create a NextResponse with CORS headers
 */
export function createCorsResponse(data: any, status = 200) {
  return NextResponse.json(data, {
    status,
    headers: corsHeaders()
  });
}

/**
 * Handle OPTIONS preflight requests
 */
export function handleOptionsRequest() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders()
  });
}