/**
 * OpenAI Whisper transcription service for Instagram audio messages
 * Simple implementation following KISS principles
 */

const logger = {
  info: (message: string, ...args: unknown[]) => console.log(`[OPENAI-TRANSCRIPTION] ${message}`, ...args),
  warn: (message: string, ...args: unknown[]) => console.warn(`[OPENAI-TRANSCRIPTION] ${message}`, ...args),
  error: (message: string, ...args: unknown[]) => console.error(`[OPENAI-TRANSCRIPTION] ${message}`, ...args)
};

interface TranscriptionResult {
  success: boolean;
  text?: string;
  error?: string;
}

export class OpenAITranscriptionService {
  private readonly apiKey: string;
  private readonly model: string = 'gpt-4o-transcribe'; // Use the newer high-quality model

  constructor(apiKey: string) {
    if (!apiKey) {
      throw new Error('OpenAI API key is required for transcription service');
    }
    this.apiKey = apiKey;
  }

  /**
   * Download audio file from Instagram CDN URL with proper format detection
   */
  private async downloadAudioFile(url: string): Promise<{ data: ArrayBuffer; mimeType: string; extension: string } | null> {
    try {
      logger.info(`📥 Downloading audio file from: ${url.substring(0, 80)}...`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!response.ok) {
        logger.error(`❌ Failed to download audio: ${response.status} ${response.statusText}`);
        return null;
      }

      const contentLength = response.headers.get('content-length');
      const contentType = response.headers.get('content-type') || 'audio/mpeg';
      
      if (contentLength) {
        const fileSizeMB = parseInt(contentLength) / (1024 * 1024);
        logger.info(`📊 Audio file size: ${fileSizeMB.toFixed(2)}MB, Content-Type: ${contentType}`);
        
        // OpenAI limit is 25MB
        if (fileSizeMB > 25) {
          logger.error(`❌ Audio file too large: ${fileSizeMB.toFixed(2)}MB (max 25MB)`);
          return null;
        }
      }

      const arrayBuffer = await response.arrayBuffer();
      
      // Detect actual format from file header
      const { mimeType, extension } = this.detectAudioFormat(arrayBuffer, contentType);
      
      logger.info(`✅ Successfully downloaded audio file (${arrayBuffer.byteLength} bytes)`);
      logger.info(`🔍 Detected format: ${mimeType} (${extension})`);
      
      return { data: arrayBuffer, mimeType, extension };

    } catch (error) {
      logger.error('❌ Error downloading audio file:', error);
      return null;
    }
  }

  /**
   * Detect audio format from file header and content type
   */
  private detectAudioFormat(arrayBuffer: ArrayBuffer, contentType: string): { mimeType: string; extension: string } {
    const bytes = new Uint8Array(arrayBuffer);
    
    // Check file signatures (magic numbers)
    if (bytes.length >= 12) {
      // MP4/M4A - check for ftyp box
      if (bytes[4] === 0x66 && bytes[5] === 0x74 && bytes[6] === 0x79 && bytes[7] === 0x70) {
        // Check for M4A specifically
        const ftypContent = Array.from(bytes.slice(8, 12)).map(b => String.fromCharCode(b)).join('');
        if (ftypContent.includes('M4A') || contentType.includes('mp4')) {
          return { mimeType: 'audio/mp4', extension: 'm4a' };
        }
        return { mimeType: 'audio/mp4', extension: 'mp4' };
      }
    }
    
    if (bytes.length >= 3) {
      // MP3 - check for ID3 tag or MP3 frame sync
      if ((bytes[0] === 0x49 && bytes[1] === 0x44 && bytes[2] === 0x33) || // ID3v2
          (bytes[0] === 0xFF && (bytes[1] & 0xE0) === 0xE0)) { // MP3 frame sync
        return { mimeType: 'audio/mpeg', extension: 'mp3' };
      }
    }
    
    if (bytes.length >= 4) {
      // WAV
      if (bytes[0] === 0x52 && bytes[1] === 0x49 && bytes[2] === 0x46 && bytes[3] === 0x46) {
        return { mimeType: 'audio/wav', extension: 'wav' };
      }
      
      // WebM
      if (bytes[0] === 0x1A && bytes[1] === 0x45 && bytes[2] === 0xDF && bytes[3] === 0xA3) {
        return { mimeType: 'audio/webm', extension: 'webm' };
      }
    }
    
    // Fallback based on content type
    if (contentType.includes('mp4') || contentType.includes('m4a')) {
      return { mimeType: 'audio/mp4', extension: 'm4a' };
    } else if (contentType.includes('webm')) {
      return { mimeType: 'audio/webm', extension: 'webm' };
    } else if (contentType.includes('wav')) {
      return { mimeType: 'audio/wav', extension: 'wav' };
    }
    
    // Default to MP3
    return { mimeType: 'audio/mpeg', extension: 'mp3' };
  }

  /**
   * Transcribe audio using OpenAI Whisper API
   */
  public async transcribeAudio(audioUrl: string): Promise<TranscriptionResult> {
    try {
      logger.info(`🎵 Starting transcription for audio URL: ${audioUrl.substring(0, 80)}...`);

      // Download audio file first
      const audioResult = await this.downloadAudioFile(audioUrl);
      if (!audioResult) {
        return {
          success: false,
          error: 'Failed to download audio file'
        };
      }

      const { data: audioData, mimeType, extension } = audioResult;

      // Create form data for OpenAI API
      const formData = new FormData();
      
      // Convert ArrayBuffer to Blob with detected mime type
      const audioBlob = new Blob([audioData], { type: mimeType });
      const filename = `audio.${extension}`;
      formData.append('file', audioBlob, filename);
      formData.append('model', this.model);
      formData.append('response_format', 'text'); // Simple text response
      
      // Add prompt to improve transcription quality for voice messages
      formData.append('prompt', 'This is a voice message from Instagram. The speaker might be asking a question, sharing thoughts, or having a casual conversation.');

      logger.info(`🤖 Sending transcription request to OpenAI with model: ${this.model}, format: ${mimeType} (${filename})`);

      // Send to OpenAI Transcriptions API
      const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error(`❌ OpenAI API error: ${response.status} ${response.statusText} - ${errorText}`);
        return {
          success: false,
          error: `OpenAI API error: ${response.status}`
        };
      }

      const transcriptionText = await response.text();
      logger.info(`✅ Transcription successful: "${transcriptionText.substring(0, 100)}..."`);

      return {
        success: true,
        text: transcriptionText.trim()
      };

    } catch (error) {
      logger.error('❌ Error transcribing audio:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown transcription error'
      };
    }
  }
}

/**
 * Get OpenAI API key from environment
 */
export function getOpenAIApiKey(): string | null {
  return process.env.OPENAI_API_KEY || null;
}

/**
 * Create transcription service instance
 */
export function createTranscriptionService(): OpenAITranscriptionService | null {
  const apiKey = getOpenAIApiKey();
  if (!apiKey) {
    logger.warn('⚠️ OpenAI API key not configured - transcription service unavailable');
    return null;
  }
  
  return new OpenAITranscriptionService(apiKey);
}