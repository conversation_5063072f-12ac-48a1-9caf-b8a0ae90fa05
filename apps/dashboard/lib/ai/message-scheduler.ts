/**
 * Message scheduler for follow-up messages
 * Stores scheduled messages in database for processing by background jobs
 */

import { prisma } from '@workspace/database/client';
import { determineDeliveryMethod } from './delivery-method';

const logger = {
  info: (message: string, ...args: unknown[]) => console.log(`[MESSAGE-SCHEDULER] ${message}`, ...args),
  warn: (message: string, ...args: unknown[]) => console.warn(`[MESSAGE-SCHEDULER] ${message}`, ...args),
  error: (message: string, ...args: unknown[]) => console.error(`[MESSAGE-SCHEDULER] ${message}`, ...args)
};

export interface ScheduleMessageRequest {
  instagramContactId: string;
  messageText: string;
  scheduledAt: Date;
}

export class MessageScheduler {
  /**
   * Schedule a single message for future delivery
   */
  async scheduleMessage(request: ScheduleMessageRequest): Promise<string> {
    try {
      logger.info(`📅 Scheduling immediate message for contact: ${request.instagramContactId}`);
      
      // For immediate messages, we just return a fake ID since we send them right away
      // This maintains compatibility with existing code
      const messageId = `immediate-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      logger.info(`✅ Immediate message scheduled: ID=${messageId}, scheduledAt=${request.scheduledAt.toISOString()}`);
      
      return messageId;

    } catch (error) {
      logger.error('❌ Error scheduling message:', error);
      throw error;
    }
  }

  /**
   * Schedule multiple messages with random delays (15-40s between messages)
   * @param instagramContactId Instagram contact ID
   * @param messages Array of message texts to send
   * @param startDelayMs Optional delay before sending first message
   */
  async scheduleMessageSequence(
    instagramContactId: string,
    messages: string[],
    startDelayMs: number = 0
  ): Promise<Array<{id: string; messageText: string; scheduledAt: Date; sequencePosition: number; totalInSequence: number}>> {
    const scheduledMessages: Array<{id: string; messageText: string; scheduledAt: Date; sequencePosition: number; totalInSequence: number}> = [];
    let cumulativeDelay = startDelayMs;

    for (let i = 0; i < messages.length; i++) {
      // Add random delay between messages (15-40s)
      if (i > 0) {
        const randomDelay = this.calculateMessageDelay();
        cumulativeDelay += randomDelay;
      }

      const scheduledAt = new Date(Date.now() + cumulativeDelay);
      const messageId = `seq-${Date.now()}-${i}-${Math.random().toString(36).substr(2, 9)}`;
      
      scheduledMessages.push({
        id: messageId,
        messageText: messages[i],
        scheduledAt,
        sequencePosition: i + 1,
        totalInSequence: messages.length
      });
    }

    logger.info(`📅 Scheduled ${scheduledMessages.length}/${messages.length} messages for contact ${instagramContactId}`);
    
    return scheduledMessages;
  }

  /**
   * Clear existing attack list items for a specific user
   * Used when AI processes new responses to clean up old followups
   */
  private async clearAttackListForUser(instagramUserId: string, organizationId: string): Promise<void> {
    try {
      const result = await prisma.chromeExtensionAttackList.deleteMany({
        where: {
          organizationId,
          instagramUserId,
          messageSource: 'saas_followup'
        }
      });

      logger.info(`🗑️ Cleared ${result.count} old attack list items for user: ${instagramUserId}`);
    } catch (error) {
      logger.error('❌ Error clearing attack list for user:', error);
      throw error;
    }
  }

  /**
   * Schedule follow-up messages from AI response and save to InstagramContact fields
   */
  async scheduleFollowupMessages(instagramContactId: string, aiResponse: any): Promise<string[]> {
    try {
      logger.info(`📅 Scheduling follow-up messages for contact: ${instagramContactId}`);

      // Check if followups are enabled for this organization
      const contact = await prisma.instagramContact.findUnique({
        where: { id: instagramContactId },
        select: {
          instagramUserId: true,
          organizationId: true,
          organization: {
            select: {
              instagramFollowupsEnabled: true,
              slug: true,
              name: true
            }
          }
        }
      });

      if (!contact?.organization.instagramFollowupsEnabled) {
        logger.info(`🚫 Follow-ups disabled for organization: ${contact?.organization.name || contact?.organization.slug} - skipping followups`);
        return [];
      }

      // Clear existing attack list items for this user BEFORE processing new followups
      await this.clearAttackListForUser(contact.instagramUserId, contact.organizationId);

      // Extract followups from AI response
      const followups = [
        { slot: 1, text: aiResponse.followup1_text, scheduledAt: aiResponse.followup1_scheduledAt },
        { slot: 2, text: aiResponse.followup2_text, scheduledAt: aiResponse.followup2_scheduledAt },
        { slot: 3, text: aiResponse.followup3_text, scheduledAt: aiResponse.followup3_scheduledAt },
        { slot: 4, text: aiResponse.followup4_text, scheduledAt: aiResponse.followup4_scheduledAt },
        { slot: 5, text: aiResponse.followup5_text, scheduledAt: aiResponse.followup5_scheduledAt },
        { slot: 6, text: aiResponse.followup6_text, scheduledAt: aiResponse.followup6_scheduledAt }
      ];

      // Build update data for InstagramContact
      const updateData: any = {};
      const scheduledFollowupIds: string[] = [];

      for (const followup of followups) {
        if (followup.text?.trim() && followup.scheduledAt) {
          try {
            const scheduledAt = new Date(followup.scheduledAt);
            if (!isNaN(scheduledAt.getTime())) {
              // Determine delivery method based on 24h window at scheduled time
              const sendViaExtension = await this.shouldScheduledFollowupUseExtension(
                contact.instagramUserId,
                contact.organizationId,
                scheduledAt
              );
              
              updateData[`followup${followup.slot}_text`] = followup.text;
              updateData[`followup${followup.slot}_scheduledAt`] = scheduledAt;
              updateData[`followup${followup.slot}_status`] = 'PENDING';
              updateData[`followup${followup.slot}_sendViaExtension`] = sendViaExtension;
              
              const followupId = `followup${followup.slot}-${instagramContactId}`;
              scheduledFollowupIds.push(followupId);
              
              logger.info(`✅ Message scheduled successfully: ID=${followupId}, scheduledAt=${scheduledAt.toISOString()}, delivery=${sendViaExtension ? 'extension' : 'api'}`);
            }
          } catch (error) {
            logger.warn(`⚠️ Invalid followup ${followup.slot} date:`, followup.scheduledAt);
          }
        } else {
          // Clear blank/empty followups to prevent old scheduled messages from being sent
          updateData[`followup${followup.slot}_text`] = null;
          updateData[`followup${followup.slot}_scheduledAt`] = null;
          updateData[`followup${followup.slot}_status`] = null;
          updateData[`followup${followup.slot}_sendViaExtension`] = false;
          
          logger.info(`🗑️ Cleared blank followup ${followup.slot} to prevent old scheduled messages`);
        }
      }

      // Update InstagramContact with followup data
      if (Object.keys(updateData).length > 0) {
        await prisma.instagramContact.update({
          where: { id: instagramContactId },
          data: updateData
        });

        logger.info(`✅ Follow-up messages scheduled successfully for contact: ${instagramContactId}`);
      } else {
        logger.info(`📭 No followup messages to schedule for contact: ${instagramContactId}`);
      }

      return scheduledFollowupIds;

    } catch (error) {
      logger.error('❌ Error scheduling followup messages:', error);
      throw error;
    }
  }

  /**
   * Calculate random delay between messages (15-40 seconds)
   */
  private calculateMessageDelay(): number {
    const minDelayMs = 15 * 1000; // 15 seconds
    const maxDelayMs = 40 * 1000; // 40 seconds
    const delayMs = Math.random() * (maxDelayMs - minDelayMs) + minDelayMs;
    
    logger.info(`⏱️ Message delay: ${Math.round(delayMs / 1000)}s`);
    return delayMs;
  }

  /**
   * Get scheduled followups for a contact from InstagramContact fields
   */
  async getScheduledMessages(instagramContactId: string): Promise<any[]> {
    try {
      const contact = await prisma.instagramContact.findUnique({
        where: { id: instagramContactId },
        select: {
          followup1_text: true,
          followup1_scheduledAt: true,
          followup1_status: true,
          followup1_sendViaExtension: true,
          followup2_text: true,
          followup2_scheduledAt: true,
          followup2_status: true,
          followup2_sendViaExtension: true,
          followup3_text: true,
          followup3_scheduledAt: true,
          followup3_status: true,
          followup3_sendViaExtension: true,
          followup4_text: true,
          followup4_scheduledAt: true,
          followup4_status: true,
          followup4_sendViaExtension: true,
          followup5_text: true,
          followup5_scheduledAt: true,
          followup5_status: true,
          followup5_sendViaExtension: true,
          followup6_text: true,
          followup6_scheduledAt: true,
          followup6_status: true,
          followup6_sendViaExtension: true
        }
      });

      if (!contact) {
        return [];
      }

      const followups = [];
      for (let i = 1; i <= 6; i++) {
        const text = (contact as any)[`followup${i}_text`];
        const scheduledAt = (contact as any)[`followup${i}_scheduledAt`];
        const status = (contact as any)[`followup${i}_status`];
        const sendViaExtension = (contact as any)[`followup${i}_sendViaExtension`];

        if (text && scheduledAt && status === 'PENDING') {
          followups.push({
            id: `followup${i}-${instagramContactId}`,
            messageText: text,
            scheduledAt,
            status,
            sendViaExtension,
            processed: false,
            slot: i
          });
        }
      }

      return followups.sort((a, b) => a.scheduledAt.getTime() - b.scheduledAt.getTime());

    } catch (error) {
      logger.error('❌ Error getting scheduled messages:', error);
      return [];
    }
  }

  /**
   * Cancel all scheduled followups for a contact
   */
  async cancelScheduledMessages(instagramContactId: string): Promise<number> {
    try {
      const contact = await prisma.instagramContact.findUnique({
        where: { id: instagramContactId },
        select: {
          followup1_status: true,
          followup2_status: true,
          followup3_status: true,
          followup4_status: true,
          followup5_status: true,
          followup6_status: true
        }
      });

      if (!contact) {
        return 0;
      }

      // Count pending followups and clear them
      let cancelledCount = 0;
      const updateData: any = {};

      for (let i = 1; i <= 6; i++) {
        const status = (contact as any)[`followup${i}_status`];
        if (status === 'PENDING') {
          updateData[`followup${i}_text`] = null;
          updateData[`followup${i}_scheduledAt`] = null;
          updateData[`followup${i}_status`] = null;
          updateData[`followup${i}_sendViaExtension`] = false;
          cancelledCount++;
        }
      }

      if (cancelledCount > 0) {
        await prisma.instagramContact.update({
          where: { id: instagramContactId },
          data: updateData
        });
      }

      logger.info(`❌ Cancelled ${cancelledCount} scheduled messages for contact: ${instagramContactId}`);
      return cancelledCount;

    } catch (error) {
      logger.error('❌ Error cancelling scheduled messages:', error);
      return 0;
    }
  }

  /**
   * Determine if a followup scheduled for a future time will need extension delivery
   * Projects forward to when the message will be sent to check 24h window
   */
  async shouldScheduledFollowupUseExtension(
    instagramUserId: string,
    organizationId: string,
    scheduledAt: Date
  ): Promise<boolean> {
    try {
      // Get the latest customer message
      const latestCustomerMessage = await prisma.instagramMessage.findFirst({
        where: {
          organizationId,
          instagramUserId,
          isFromBusiness: false // Customer message, not business message
        },
        orderBy: { instagramCreatedTime: 'desc' },
        select: { instagramCreatedTime: true }
      });

      if (!latestCustomerMessage) {
        return true; // No customer messages, use extension
      }

      // Calculate hours between last customer message and when followup will be sent
      const hoursDiff = (scheduledAt.getTime() - latestCustomerMessage.instagramCreatedTime.getTime()) / (1000 * 60 * 60);
      
      logger.info(`⏰ Followup delivery check: ${hoursDiff.toFixed(2)}h between last customer message and scheduled followup`);
      
      return hoursDiff > 24; // Use extension if outside 24h window

    } catch (error) {
      logger.error('❌ Error checking scheduled followup delivery method:', error);
      return true; // Default to extension on error
    }
  }

  /**
   * Check if message should be sent via extension (24h window check)
   */
  shouldSendViaExtension(lastContactMessageAt: Date | null, forceExtension?: boolean): boolean {
    if (forceExtension) return true;
    if (!lastContactMessageAt) return true; // No previous contact, use extension
    
    const now = new Date();
    const hoursSinceLastContact = (now.getTime() - lastContactMessageAt.getTime()) / (1000 * 60 * 60);
    
    return hoursSinceLastContact > 24; // Use extension if outside 24h window
  }

  /**
   * Mark messages as sent (update InstagramContact followup status)
   */
  async markMessageSent(messages: any[], messageId: string): Promise<void> {
    try {
      // Extract slot number from messageId (e.g., "followup1-contactId")
      const slotMatch = messageId.match(/followup(\d+)-/);
      if (!slotMatch) return;
      
      const slot = parseInt(slotMatch[1]);
      const contactId = messageId.split('-').slice(1).join('-');
      
      await prisma.instagramContact.update({
        where: { id: contactId },
        data: {
          [`followup${slot}_status`]: 'SENT'
        }
      });
      
      logger.info(`✅ Marked followup ${slot} as sent for contact: ${contactId}`);
    } catch (error) {
      logger.error('❌ Error marking message as sent:', error);
    }
  }

  /**
   * Mark messages as failed (update InstagramContact followup status)
   */
  async markMessageFailed(messages: any[], messageId: string): Promise<void> {
    try {
      // Extract slot number from messageId (e.g., "followup1-contactId")
      const slotMatch = messageId.match(/followup(\d+)-/);
      if (!slotMatch) return;
      
      const slot = parseInt(slotMatch[1]);
      const contactId = messageId.split('-').slice(1).join('-');
      
      await prisma.instagramContact.update({
        where: { id: contactId },
        data: {
          [`followup${slot}_status`]: 'FAILED'
        }
      });
      
      logger.info(`❌ Marked followup ${slot} as failed for contact: ${contactId}`);
    } catch (error) {
      logger.error('❌ Error marking message as failed:', error);
    }
  }
}