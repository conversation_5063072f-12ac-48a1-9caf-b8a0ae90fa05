/**
 * OpenAI Vision service for Instagram image messages
 * Simple implementation following KISS principles - mirrors transcription service
 */

const logger = {
  info: (message: string, ...args: unknown[]) => console.log(`[OPENAI-VISION] ${message}`, ...args),
  warn: (message: string, ...args: unknown[]) => console.warn(`[OPENAI-VISION] ${message}`, ...args),
  error: (message: string, ...args: unknown[]) => console.error(`[OPENAI-VISION] ${message}`, ...args)
};

interface VisionResult {
  success: boolean;
  description?: string;
  error?: string;
}

export class OpenAIVisionService {
  private readonly apiKey: string;
  private readonly model: string = 'gpt-4o'; // Use vision-capable model

  constructor(apiKey: string) {
    if (!apiKey) {
      throw new Error('OpenAI API key is required for vision service');
    }
    this.apiKey = apiKey;
  }

  /**
   * Download image file from Instagram CDN URL
   */
  private async downloadImageFile(url: string): Promise<{ data: ArrayBuffer; mimeType: string; extension: string } | null> {
    try {
      logger.info(`📥 Downloading image file from: ${url.substring(0, 80)}...`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!response.ok) {
        logger.error(`❌ Failed to download image: ${response.status} ${response.statusText}`);
        return null;
      }

      const contentLength = response.headers.get('content-length');
      const contentType = response.headers.get('content-type') || 'image/jpeg';

      if (contentLength) {
        const fileSizeMB = parseInt(contentLength) / (1024 * 1024);
        logger.info(`📊 Image file size: ${fileSizeMB.toFixed(2)}MB, Content-Type: ${contentType}`);

        // OpenAI Vision limit is 20MB
        if (fileSizeMB > 20) {
          logger.error(`❌ Image file too large: ${fileSizeMB.toFixed(2)}MB (max 20MB)`);
          return null;
        }
      }

      const arrayBuffer = await response.arrayBuffer();

      // Detect format from content type
      const { mimeType, extension } = this.detectImageFormat(contentType);

      logger.info(`✅ Successfully downloaded image file (${arrayBuffer.byteLength} bytes)`);
      logger.info(`🔍 Detected format: ${mimeType} (${extension})`);

      return { data: arrayBuffer, mimeType, extension };

    } catch (error) {
      logger.error('❌ Error downloading image file:', error);
      return null;
    }
  }

  /**
   * Detect image format from content type
   */
  private detectImageFormat(contentType: string): { mimeType: string; extension: string } {
    if (contentType.includes('png')) {
      return { mimeType: 'image/png', extension: 'png' };
    } else if (contentType.includes('gif')) {
      return { mimeType: 'image/gif', extension: 'gif' };
    } else if (contentType.includes('webp')) {
      return { mimeType: 'image/webp', extension: 'webp' };
    }

    // Default to JPEG
    return { mimeType: 'image/jpeg', extension: 'jpg' };
  }

  /**
   * Analyze image using OpenAI Vision API
   */
  public async analyzeImage(imageUrl: string): Promise<VisionResult> {
    try {
      logger.info(`🖼️ Starting image analysis for URL: ${imageUrl.substring(0, 80)}...`);

      // For Instagram CDN URLs, we need to download first then send as base64
      // because OpenAI can't access Instagram's protected CDN
      const imageResult = await this.downloadImageFile(imageUrl);
      if (!imageResult) {
        return {
          success: false,
          error: 'Failed to download image from Instagram'
        };
      }

      // Convert ArrayBuffer to base64
      const base64Image = Buffer.from(imageResult.data).toString('base64');
      const dataUrl = `data:${imageResult.mimeType};base64,${base64Image}`;

      logger.info(`📤 Sending image to OpenAI Vision API (${imageResult.mimeType}, ${Math.round(imageResult.data.byteLength / 1024)}KB)`);

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: 'Analyze this image and provide a concise description of what you see. Focus on people, objects, activities, text, or anything relevant for understanding the context of this Instagram message. Keep it brief but descriptive.'
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: dataUrl,
                    detail: 'low' // Use low detail for faster/cheaper processing
                  }
                }
              ]
            }
          ],
          max_tokens: 200, // Keep descriptions concise
          temperature: 0.1 // Low temperature for consistent descriptions
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error(`❌ OpenAI Vision API error: ${response.status} ${response.statusText} - ${errorText}`);
        return {
          success: false,
          error: `OpenAI Vision API error: ${response.status}`
        };
      }

      const result = await response.json();
      const description = result.choices?.[0]?.message?.content?.trim();

      if (!description) {
        logger.error('❌ No description returned from OpenAI Vision API');
        return {
          success: false,
          error: 'No description returned from Vision API'
        };
      }

      logger.info(`✅ Image analysis successful: "${description.substring(0, 100)}..."`);

      return {
        success: true,
        description: description
      };

    } catch (error) {
      logger.error('❌ Error analyzing image:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown vision analysis error'
      };
    }
  }
}

/**
 * Get OpenAI API key from environment
 */
export function getOpenAIApiKey(): string | null {
  return process.env.OPENAI_API_KEY || null;
}

/**
 * Create vision service instance
 */
export function createVisionService(): OpenAIVisionService | null {
  const apiKey = getOpenAIApiKey();
  if (!apiKey) {
    logger.warn('⚠️ OpenAI API key not configured - vision service unavailable');
    return null;
  }

  return new OpenAIVisionService(apiKey);
}