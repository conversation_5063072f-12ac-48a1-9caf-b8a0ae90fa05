/**
 * 24-hour window delivery method detection
 * Determines if messages can be sent directly via Instagram API or need browser extension
 */

import { prisma } from '@workspace/database/client';

const logger = {
  info: (message: string, ...args: unknown[]) => console.log(`[DELIVERY-METHOD] ${message}`, ...args),
  warn: (message: string, ...args: unknown[]) => console.warn(`[DELIVERY-METHOD] ${message}`, ...args),
  error: (message: string, ...args: unknown[]) => console.error(`[DELIVERY-METHOD] ${message}`, ...args)
};

/**
 * Determine if messages can be sent directly via Instagram API
 * Returns true if within 24h window, false if browser extension needed
 */
export async function determineDeliveryMethod(
  instagramUserId: string, 
  organizationId: string
): Promise<boolean> {
  try {
    logger.info(`🔍 Checking delivery method for user: ${instagramUserId}`);

    // Get latest message from customer (not from business)
    const latestCustomerMessage = await prisma.instagramMessage.findFirst({
      where: {
        organizationId,
        instagramUserId,
        isFromBusiness: false // Customer message, not business message
      },
      orderBy: { instagramCreatedTime: 'desc' },
      select: { 
        instagramCreatedTime: true,
        content: true 
      }
    });

    if (!latestCustomerMessage) {
      logger.warn(`⚠️ No customer messages found for user: ${instagramUserId}`);
      return false; // No customer messages, use browser extension
    }

    const now = new Date();
    const messageTime = latestCustomerMessage.instagramCreatedTime;
    const hoursDiff = (now.getTime() - messageTime.getTime()) / (1000 * 60 * 60);

    logger.info(`⏰ Time analysis for user ${instagramUserId}:`);
    logger.info(`   Latest customer message: ${messageTime.toISOString()}`);
    logger.info(`   Current time: ${now.toISOString()}`);
    logger.info(`   Hours difference: ${hoursDiff.toFixed(2)}h`);

    const canSendDirectly = hoursDiff <= 24;

    if (canSendDirectly) {
      logger.info(`✅ Within 24h window - can send directly via Instagram API (${hoursDiff.toFixed(2)}h ago)`);
    } else {
      logger.info(`🔄 Outside 24h window - must use browser extension (${hoursDiff.toFixed(2)}h ago)`);
    }

    return canSendDirectly;

  } catch (error) {
    logger.error('❌ Error determining delivery method:', error);
    // Default to browser extension on error
    return false;
  }
}

/**
 * Get delivery method info for debugging/UI purposes
 */
export async function getDeliveryMethodInfo(
  instagramUserId: string,
  organizationId: string
): Promise<{
  canSendDirectly: boolean;
  latestCustomerMessageAt: Date | null;
  hoursSinceLastMessage: number | null;
  deliveryMethod: 'instagram_api' | 'browser_extension';
}> {
  try {
    const latestCustomerMessage = await prisma.instagramMessage.findFirst({
      where: {
        organizationId,
        instagramUserId,
        isFromBusiness: false
      },
      orderBy: { instagramCreatedTime: 'desc' },
      select: { instagramCreatedTime: true }
    });

    if (!latestCustomerMessage) {
      return {
        canSendDirectly: false,
        latestCustomerMessageAt: null,
        hoursSinceLastMessage: null,
        deliveryMethod: 'browser_extension'
      };
    }

    const now = new Date();
    const messageTime = latestCustomerMessage.instagramCreatedTime;
    const hoursDiff = (now.getTime() - messageTime.getTime()) / (1000 * 60 * 60);
    const canSendDirectly = hoursDiff <= 24;

    return {
      canSendDirectly,
      latestCustomerMessageAt: messageTime,
      hoursSinceLastMessage: hoursDiff,
      deliveryMethod: canSendDirectly ? 'instagram_api' : 'browser_extension'
    };

  } catch (error) {
    logger.error('❌ Error getting delivery method info:', error);
    return {
      canSendDirectly: false,
      latestCustomerMessageAt: null,
      hoursSinceLastMessage: null,
      deliveryMethod: 'browser_extension'
    };
  }
}

/**
 * Check if contact is eligible for immediate response
 * Considers both 24h window and other factors
 */
export async function canSendImmediateResponse(instagramContactId: string): Promise<{
  canSend: boolean;
  reason: string;
  deliveryMethod: 'instagram_api' | 'browser_extension' | 'blocked';
}> {
  try {
    // Get contact info
    const contact = await prisma.instagramContact.findUnique({
      where: { id: instagramContactId },
      select: {
        instagramUserId: true,
        organizationId: true,
        aiEnabled: true,
        stage: true,
        nickname: true,
        organization: {
          select: {
            instagramIsConnected: true,
            instagramToken: true
          }
        }
      }
    });

    if (!contact) {
      return {
        canSend: false,
        reason: 'Contact not found',
        deliveryMethod: 'blocked'
      };
    }

    if (!contact.aiEnabled) {
      return {
        canSend: false,
        reason: 'AI disabled for this contact',
        deliveryMethod: 'blocked'
      };
    }

    if (contact.stage === 'DISQUALIFIED') {
      return {
        canSend: false,
        reason: 'Contact is disqualified',
        deliveryMethod: 'blocked'
      };
    }

    if (!contact.organization.instagramIsConnected || !contact.organization.instagramToken) {
      return {
        canSend: false,
        reason: 'Instagram not connected for organization',
        deliveryMethod: 'blocked'
      };
    }

    // Check 24h window
    const canSendDirectly = await determineDeliveryMethod(
      contact.instagramUserId,
      contact.organizationId
    );

    return {
      canSend: true,
      reason: canSendDirectly ? 'Within 24h window' : 'Outside 24h window - queued for browser extension',
      deliveryMethod: canSendDirectly ? 'instagram_api' : 'browser_extension'
    };

  } catch (error) {
    logger.error('❌ Error checking immediate response eligibility:', error);
    return {
      canSend: false,
      reason: 'System error',
      deliveryMethod: 'blocked'
    };
  }
}