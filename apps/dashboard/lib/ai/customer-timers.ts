/**
 * Real-Time Event-Driven AI Processing System
 * Modern approach using in-memory timers + p-limit concurrency + database backup
 * No polling, no delays - instant real-time processing
 */

import { prisma } from '@workspace/database/client';
import pLimit from 'p-limit';

const logger = {
  info: (message: string, ...args: unknown[]) => console.log(`[REALTIME-AI] ${message}`, ...args),
  warn: (message: string, ...args: unknown[]) => console.warn(`[REALTIME-AI] ${message}`, ...args),
  error: (message: string, ...args: unknown[]) => console.error(`[REALTIME-AI] ${message}`, ...args)
};

// Global state for real-time processing
const activeTimers = new Map<string, NodeJS.Timeout>();
const processingStates = new Map<string, boolean>();

// Concurrency control with p-limit (battle-tested by npm, ESLint, etc.)
const MAX_CONCURRENT = parseInt(process.env.AI_MAX_WORKERS || '50');
const aiLimit = pLimit(MAX_CONCURRENT);

interface CustomerTimerConfig {
  customerId: string;
  organizationId: string;
  responseTimeMin: number; // seconds
  responseTimeMax: number; // seconds
}

/**
 * Initialize the real-time AI system
 * Call this once when the app starts
 */
export async function initializeRealTimeAI(): Promise<void> {
  try {
    logger.info(`🚀 Initializing Real-Time AI System with ${MAX_CONCURRENT} concurrent workers`);
    
    // Recover any pending timers from database (in case of restart)
    await recoverPendingTimers();
    
    logger.info('✅ Real-Time AI System initialized successfully');
    
  } catch (error) {
    logger.error('❌ Failed to initialize Real-Time AI System:', error);
    throw error;
  }
}

/**
 * Start real-time AI timer - INSTANT processing when timer expires
 * No polling delays, no cron jobs needed
 */
export async function startCustomerTimer(config: CustomerTimerConfig): Promise<void> {
  try {
    const { customerId, organizationId, responseTimeMin, responseTimeMax } = config;
    const customerKey = `${customerId}_${organizationId}`;
    
    // Prevent overlapping processing
    if (isProcessing(customerId, organizationId)) {
      logger.info(`🚫 AI processing in progress for customer: ${customerKey} - skipping timer start`);
      return;
    }

    // Check for existing timer and provide better feedback
    const hasExisting = hasActiveTimer(customerId, organizationId);
    if (hasExisting) {
      logger.info(`🔄 Cancelling existing timer for customer: ${customerKey} to start new one`);
    }

    // Cancel existing timer
    cancelExistingTimer(customerKey);
    
    // Calculate random delay
    const delaySeconds = Math.floor(Math.random() * (responseTimeMax - responseTimeMin + 1)) + responseTimeMin;
    const delayMs = delaySeconds * 1000;
    const triggersAt = new Date(Date.now() + delayMs);

    logger.info(`⏰ Starting REAL-TIME timer for customer: ${customerKey}`);
    logger.info(`📊 Timer details: ${delaySeconds}s delay (${responseTimeMin}-${responseTimeMax}s range), triggers at ${triggersAt.toISOString()}`);
    
    // Store timer in database for recovery (non-blocking)
    storeTimerInDatabase(customerId, organizationId, triggersAt, responseTimeMin, responseTimeMax)
      .catch(error => logger.warn('⚠️ Failed to store timer in database:', error));
    
    // Set real-time in-memory timer
    const timerId = setTimeout(() => {
      processCustomerWithConcurrency(customerId, organizationId, customerKey);
    }, delayMs);
    
    // Store timer reference
    activeTimers.set(customerKey, timerId);
    
    logger.info(`✅ REAL-TIME timer started: ${customerKey} fires in ${delaySeconds}s`);
    
  } catch (error) {
    logger.error('❌ Error starting real-time timer:', error);
  }
}

/**
 * Cancel timer for specific customer
 */
export function cancelCustomerTimer(customerId: string, organizationId: string): void {
  const customerKey = `${customerId}_${organizationId}`;
  cancelExistingTimer(customerKey);
  logger.info(`❌ Timer cancelled: ${customerKey}`);
}

/**
 * Process customer with p-limit concurrency control
 */
async function processCustomerWithConcurrency(customerId: string, organizationId: string, customerKey: string): Promise<void> {
  try {
    logger.info(`🔔 Timer FIRED for customer: ${customerKey} - processing with p-limit`);
    
    // Remove from active timers
    activeTimers.delete(customerKey);
    
    // Remove timer from database (cleanup)
    cleanupTimerFromDatabase(customerId, organizationId)
      .catch(error => logger.warn('⚠️ Failed to cleanup timer from database:', error));
    
    // Process with p-limit concurrency control
    await aiLimit(() => processAIResponseForCustomer(customerId, organizationId));
    
  } catch (error) {
    logger.error(`❌ Error processing customer ${customerKey}:`, error);
  }
}

/**
 * Main AI processing function with p-limit concurrency
 */
async function processAIResponseForCustomer(customerId: string, organizationId: string): Promise<void> {
  const customerKey = `${customerId}_${organizationId}`;
  
  try {
    logger.info(`🤖 Processing AI response for customer: ${customerKey} [${aiLimit.activeCount}/${MAX_CONCURRENT} active]`);
    
    // Set processing state
    setProcessingState(customerId, organizationId);
    
    // Get Instagram contact
    const contact = await prisma.instagramContact.findUnique({
      where: {
        instagramUserId_organizationId: {
          instagramUserId: customerId,
          organizationId: organizationId
        }
      },
      select: {
        id: true,
        aiEnabled: true,
        stage: true,
        nickname: true
      }
    });
    
    if (!contact) {
      logger.warn(`⚠️ Contact not found for customer: ${customerKey}`);
      return;
    }
    
    if (!contact.aiEnabled) {
      logger.info(`🚫 AI disabled for contact: ${contact.nickname || customerKey}`);
      return;
    }
    
    // Import auto-responder dynamically
    const { processAutoResponse } = await import('./auto-responder');
    await processAutoResponse(contact.id);
    
    logger.info(`✅ AI processing completed for customer: ${customerKey}`);
    
  } catch (error) {
    logger.error(`❌ Error processing AI response for customer ${customerKey}:`, error);
  } finally {
    // Always clear processing state
    clearProcessingState(customerId, organizationId);
  }
}

/**
 * Cancel existing timer helper
 */
function cancelExistingTimer(customerKey: string): void {
  const existingTimer = activeTimers.get(customerKey);
  if (existingTimer) {
    clearTimeout(existingTimer);
    activeTimers.delete(customerKey);
    logger.info(`🔄 Cancelled existing timer: ${customerKey}`);
  }
}

/**
 * Store timer in database for recovery (non-blocking)
 */
async function storeTimerInDatabase(
  customerId: string, 
  organizationId: string, 
  triggersAt: Date,
  responseTimeMin: number,
  responseTimeMax: number
): Promise<void> {
  try {
    await prisma.instagramContact.upsert({
      where: {
        instagramUserId_organizationId: {
          instagramUserId: customerId,
          organizationId
        }
      },
      update: {
        // Store timer data in existing fields for recovery
        lastActivity: triggersAt
      },
      create: {
        organizationId,
        instagramUserId: customerId,
        aiEnabled: true,
        stage: 'FIRSTCONTACT',
        lastActivity: triggersAt
      }
    });
  } catch (error) {
    logger.warn('⚠️ Failed to store timer in database:', error);
  }
}

/**
 * Cleanup timer from database
 */
async function cleanupTimerFromDatabase(customerId: string, organizationId: string): Promise<void> {
  try {
    // Just clear the lastActivity field (our timer recovery marker)
    await prisma.instagramContact.updateMany({
      where: {
        instagramUserId: customerId,
        organizationId
      },
      data: {
        lastActivity: null
      }
    });
  } catch (error) {
    logger.warn('⚠️ Failed to cleanup timer from database:', error);
  }
}

/**
 * Recover pending timers on startup
 */
async function recoverPendingTimers(): Promise<void> {
  try {
    logger.info('🔄 Recovering pending timers from database...');
    
    // Find contacts with recent lastActivity (our recovery marker)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const pendingContacts = await prisma.instagramContact.findMany({
      where: {
        lastActivity: {
          gte: fiveMinutesAgo // Only recent ones
        },
        aiEnabled: true
      },
      select: {
        instagramUserId: true,
        organizationId: true,
        lastActivity: true,
        nickname: true
      },
      take: 100 // Limit recovery to prevent startup overload
    });
    
    if (pendingContacts.length === 0) {
      logger.info('📭 No pending timers to recover');
      return;
    }
    
    logger.info(`🔄 Recovering ${pendingContacts.length} pending timers...`);
    
    for (const contact of pendingContacts) {
      if (!contact.lastActivity) continue;
      
      const now = Date.now();
      const scheduledTime = contact.lastActivity.getTime();
      const delay = scheduledTime - now;
      
      if (delay > 0) {
        // Timer still pending - reschedule
        const customerKey = `${contact.instagramUserId}_${contact.organizationId}`;
        const remainingSeconds = Math.ceil(delay/1000);

        const timerId = setTimeout(() => {
          processCustomerWithConcurrency(contact.instagramUserId, contact.organizationId, customerKey);
        }, delay);

        activeTimers.set(customerKey, timerId);
        logger.info(`⏰ Recovered timer: ${contact.nickname || contact.instagramUserId} (${remainingSeconds}s remaining)`);
        logger.info(`📊 Recovery details: scheduled for ${contact.lastActivity?.toISOString()}, now ${new Date().toISOString()}`);
      } else {
        // Timer expired during downtime - DISCARD instead of processing immediately
        const expiredSeconds = Math.abs(Math.ceil(delay/1000));
        logger.info(`🗑️ Discarded expired timer: ${contact.nickname || contact.instagramUserId} (expired ${expiredSeconds}s ago)`);
        logger.info(`📊 Expiry details: was scheduled for ${contact.lastActivity?.toISOString()}, now ${new Date().toISOString()}`);

        // Clean up the expired timer from database
        await cleanupTimerFromDatabase(contact.instagramUserId, contact.organizationId);
      }
    }
    
    logger.info(`✅ Timer recovery completed: ${pendingContacts.length} timers processed`);
    
  } catch (error) {
    logger.error('❌ Error recovering pending timers:', error);
  }
}

// Processing state management
export function isProcessing(customerId: string, organizationId: string): boolean {
  const customerKey = `${customerId}_${organizationId}`;
  return processingStates.get(customerKey) === true;
}

export function setProcessingState(customerId: string, organizationId: string): void {
  const customerKey = `${customerId}_${organizationId}`;
  processingStates.set(customerKey, true);
}

export function clearProcessingState(customerId: string, organizationId: string): void {
  const customerKey = `${customerId}_${organizationId}`;
  processingStates.delete(customerKey);
}

// Utility functions
export function hasActiveTimer(customerId: string, organizationId: string): boolean {
  const customerKey = `${customerId}_${organizationId}`;
  return activeTimers.has(customerKey);
}

export function getActiveTimerCount(): number {
  return activeTimers.size;
}

export function getProcessingCount(): number {
  return processingStates.size;
}

export function getConcurrencyStats(): { active: number; pending: number; max: number } {
  return {
    active: aiLimit.activeCount,
    pending: aiLimit.pendingCount,
    max: MAX_CONCURRENT
  };
}

export function clearAllTimers(): void {
  activeTimers.forEach((timer, customerKey) => {
    clearTimeout(timer);
    logger.info(`🧹 Cleared timer: ${customerKey}`);
  });
  activeTimers.clear();
  processingStates.clear();
  logger.info('🧹 All timers and processing states cleared');
}

// Auto-initialize when imported (for production)
if (typeof process !== 'undefined' && process.env.NODE_ENV !== 'test') {
  // Initialize on next tick to avoid blocking imports
  process.nextTick(() => {
    initializeRealTimeAI().catch(error => {
      logger.error('❌ Auto-initialization failed:', error);
    });
  });
}