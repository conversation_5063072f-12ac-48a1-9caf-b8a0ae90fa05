/**
 * Background job system for processing scheduled follow-up messages
 * KISS implementation - can be run as cron job or scheduled task
 */

import { prisma } from '@workspace/database/client';
import { sendInstagramTextMessage } from '~/lib/instagram-api';
import { determineDeliveryMethod } from './delivery-method';

const logger = {
  info: (message: string, ...args: unknown[]) => console.log(`[BACKGROUND-JOBS] ${message}`, ...args),
  warn: (message: string, ...args: unknown[]) => console.warn(`[BACKGROUND-JOBS] ${message}`, ...args),
  error: (message: string, ...args: unknown[]) => console.error(`[BACKGROUND-JOBS] ${message}`, ...args)
};

/**
 * Process followups marked for extension delivery by adding them to attack list
 */
export async function processExtensionFollowups(): Promise<void> {
  try {
    logger.info('🔄 Processing extension followups...');

    // Find contacts with followups marked for extension delivery
    const contactsWithExtensionFollowups = await prisma.instagramContact.findMany({
      where: {
        OR: [
          {
            followup1_status: 'PENDING',
            followup1_sendViaExtension: true,
            followup1_text: { not: null }
          },
          {
            followup2_status: 'PENDING', 
            followup2_sendViaExtension: true,
            followup2_text: { not: null }
          },
          {
            followup3_status: 'PENDING',
            followup3_sendViaExtension: true,
            followup3_text: { not: null }
          },
          {
            followup4_status: 'PENDING',
            followup4_sendViaExtension: true,
            followup4_text: { not: null }
          },
          {
            followup5_status: 'PENDING',
            followup5_sendViaExtension: true,
            followup5_text: { not: null }
          },
          {
            followup6_status: 'PENDING',
            followup6_sendViaExtension: true,
            followup6_text: { not: null }
          }
        ]
      },
      select: {
        id: true,
        organizationId: true,
        instagramUserId: true,
        nickname: true,
        profilePhoto: true,
        followup1_text: true,
        followup1_scheduledAt: true,
        followup1_status: true,
        followup1_sendViaExtension: true,
        followup2_text: true,
        followup2_scheduledAt: true,
        followup2_status: true,
        followup2_sendViaExtension: true,
        followup3_text: true,
        followup3_scheduledAt: true,
        followup3_status: true,
        followup3_sendViaExtension: true,
        followup4_text: true,
        followup4_scheduledAt: true,
        followup4_status: true,
        followup4_sendViaExtension: true,
        followup5_text: true,
        followup5_scheduledAt: true,
        followup5_status: true,
        followup5_sendViaExtension: true,
        followup6_text: true,
        followup6_scheduledAt: true,
        followup6_status: true,
        followup6_sendViaExtension: true
      },
      take: 50 // Process max 50 contacts per run
    });

    if (contactsWithExtensionFollowups.length === 0) {
      logger.info('📭 No extension followups to process');
      return;
    }

    logger.info(`📤 Processing ${contactsWithExtensionFollowups.length} contacts with extension followups`);

    let processed = 0;
    let failed = 0;

    for (const contact of contactsWithExtensionFollowups) {
      try {
        await processContactExtensionFollowups(contact);
        processed++;
      } catch (error) {
        logger.error(`❌ Failed to process extension followups for contact ${contact.id}:`, error);
        failed++;
      }
    }

    logger.info(`✅ Extension followup processing completed: ${processed} contacts processed, ${failed} failed`);

  } catch (error) {
    logger.error('❌ Error in extension followup processing:', error);
  }
}

/**
 * Process extension followups for a single contact by adding them to attack list
 */
async function processContactExtensionFollowups(contact: any): Promise<void> {
  try {
    logger.info(`📤 Processing extension followups for contact: ${contact.nickname || contact.instagramUserId}`);

    // Process each followup slot
    for (let slot = 1; slot <= 6; slot++) {
      const text = (contact as any)[`followup${slot}_text`];
      const scheduledAt = (contact as any)[`followup${slot}_scheduledAt`];
      const status = (contact as any)[`followup${slot}_status`];
      const sendViaExtension = (contact as any)[`followup${slot}_sendViaExtension`];

      // Skip if not eligible for extension processing
      if (!text || status !== 'PENDING' || !sendViaExtension) {
        continue;
      }

      try {
        // Create attack list entry
        await prisma.chromeExtensionAttackList.create({
          data: {
            organizationId: contact.organizationId,
            instagramUserId: contact.instagramUserId,
            nickname: contact.nickname,
            profilePhoto: contact.profilePhoto,
            priority: 2, // SaaS followups
            messageContent: [text], // Store followup text directly (no batch for followups)
            messageSource: 'saas_followup',
            messageBatchId: null, // No batch for individual followups
            scheduledAt: scheduledAt || new Date(),
            status: 'pending'
          }
        });

        // Mark followup as queued in InstagramContact
        await prisma.instagramContact.update({
          where: { id: contact.id },
          data: {
            [`followup${slot}_status`]: 'QUEUED'
          }
        });

        logger.info(`✅ Followup ${slot} queued for extension delivery: ${contact.nickname || contact.instagramUserId}`);

      } catch (error) {
        logger.error(`❌ Failed to queue followup ${slot} for contact ${contact.id}:`, error);
        
        // Mark followup as failed
        await prisma.instagramContact.update({
          where: { id: contact.id },
          data: {
            [`followup${slot}_status`]: 'FAILED'
          }
        });
      }
    }

  } catch (error) {
    logger.error('❌ Error processing contact extension followups:', error);
    throw error;
  }
}

/**
 * Process all due follow-up messages from InstagramContact fields
 * This should be called by a cron job every few minutes
 */
export async function processInstagramContactFollowups(): Promise<void> {
  try {
    logger.info('🔄 Starting Instagram contact follow-up processing...');

    const now = new Date();

    // Get all contacts with due followups (any followup slot with scheduledAt <= now and status = PENDING)
    const contactsWithDueFollowups = await prisma.instagramContact.findMany({
      where: {
        OR: [
          {
            followup1_scheduledAt: { lte: now },
            followup1_status: 'PENDING',
            followup1_text: { not: null }
          },
          {
            followup2_scheduledAt: { lte: now },
            followup2_status: 'PENDING',
            followup2_text: { not: null }
          },
          {
            followup3_scheduledAt: { lte: now },
            followup3_status: 'PENDING',
            followup3_text: { not: null }
          },
          {
            followup4_scheduledAt: { lte: now },
            followup4_status: 'PENDING',
            followup4_text: { not: null }
          },
          {
            followup5_scheduledAt: { lte: now },
            followup5_status: 'PENDING',
            followup5_text: { not: null }
          },
          {
            followup6_scheduledAt: { lte: now },
            followup6_status: 'PENDING',
            followup6_text: { not: null }
          }
        ]
      },
      include: {
        organization: {
          select: {
            id: true,
            slug: true,
            instagramToken: true,
            instagramIsConnected: true,
            instagramFollowupsEnabled: true
          }
        }
      },
      take: 25 // Process max 25 contacts per run to avoid overwhelming the system
    });

    if (contactsWithDueFollowups.length === 0) {
      logger.info('📭 No due follow-up messages found');
      return;
    }

    logger.info(`📤 Processing ${contactsWithDueFollowups.length} contacts with due follow-ups`);

    let processed = 0;
    let failed = 0;

    for (const contact of contactsWithDueFollowups) {
      try {
        await processContactFollowups(contact, now);
        processed++;
      } catch (error) {
        logger.error(`❌ Failed to process followups for contact ${contact.id}:`, error);
        failed++;
      }

      // Small delay between contacts to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second
    }

    logger.info(`✅ Follow-up processing completed: ${processed} contacts processed, ${failed} failed`);

  } catch (error) {
    logger.error('❌ Error in Instagram contact follow-up processing:', error);
  }
}

/**
 * Process followups for a single contact
 */
async function processContactFollowups(contact: any, now: Date): Promise<void> {
  try {
    const organization = contact.organization;

    logger.info(`📤 Processing followups for contact: ${contact.nickname || contact.instagramUserId}`);

    // Check if organization is still connected to Instagram
    if (!organization.instagramIsConnected || !organization.instagramToken) {
      logger.error(`❌ Instagram not connected for organization: ${organization.slug}`);
      await markContactFollowupsFailed(contact.id, 'Instagram not connected');
      return;
    }

    // Check if followups are enabled for this organization
    if (!organization.instagramFollowupsEnabled) {
      logger.info(`🚫 Follow-ups disabled for organization: ${organization.slug} - skipping followups`);
      await markContactFollowupsSkipped(contact.id, 'Follow-ups disabled for organization');
      return;
    }

    // Check if contact still has AI enabled
    if (!contact.aiEnabled) {
      logger.info(`🚫 AI disabled for contact: ${contact.nickname || contact.instagramUserId} - skipping followups`);
      await markContactFollowupsSkipped(contact.id, 'AI disabled for contact');
      return;
    }

    // Check if contact is disqualified
    if (contact.stage === 'DISQUALIFIED') {
      logger.info(`🚫 Contact is disqualified: ${contact.nickname || contact.instagramUserId} - skipping followups`);
      await markContactFollowupsSkipped(contact.id, 'Contact is disqualified');
      return;
    }

    // Process each followup slot
    for (let slot = 1; slot <= 6; slot++) {
      const text = (contact as any)[`followup${slot}_text`];
      const scheduledAt = (contact as any)[`followup${slot}_scheduledAt`];
      const status = (contact as any)[`followup${slot}_status`];
      const sendViaExtension = (contact as any)[`followup${slot}_sendViaExtension`];

      // Skip if not due or not pending
      if (!text || !scheduledAt || status !== 'PENDING' || scheduledAt > now) {
        continue;
      }

      try {
        await processFollowupSlot(contact, organization, slot, text, sendViaExtension);
      } catch (error) {
        logger.error(`❌ Failed to process followup ${slot} for contact ${contact.id}:`, error);
        await markFollowupSlotFailed(contact.id, slot, error instanceof Error ? error.message : 'Unknown error');
      }
    }

  } catch (error) {
    logger.error('❌ Error processing contact followups:', error);
    await markContactFollowupsFailed(contact.id, error instanceof Error ? error.message : 'Unknown error');
  }
}

/**
 * Process a single followup slot
 */
async function processFollowupSlot(
  contact: any,
  organization: any,
  slot: number,
  text: string,
  sendViaExtension: boolean
): Promise<void> {
  
  // Check delivery method (24h window) unless forced to use extension
  if (!sendViaExtension) {
    const canSendDirectly = await determineDeliveryMethod(contact.instagramUserId, organization.id);
    
    if (!canSendDirectly) {
      logger.info(`🔄 Followup ${slot} queued for browser extension (outside 24h window): ${contact.nickname || contact.instagramUserId}`);
      await markFollowupForExtension(contact.id, slot, 'Outside 24h window');
      return;
    }
  } else {
    logger.info(`🔄 Followup ${slot} marked for browser extension delivery: ${contact.nickname || contact.instagramUserId}`);
    await markFollowupForExtension(contact.id, slot, 'Extension delivery requested');
    return;
  }

  // Send the message via Instagram API
  logger.info(`📨 Sending followup ${slot}: "${text.substring(0, 50)}..."`);
  
  const result = await sendInstagramTextMessage(
    contact.instagramUserId,
    text,
    organization.instagramToken
  );

  if (result.success) {
    logger.info(`✅ Followup ${slot} sent successfully: ${result.messageId}`);
    await markFollowupSlotSent(contact.id, slot, result.messageId);
  } else {
    logger.error(`❌ Failed to send followup ${slot}:`, result.error);
    await markFollowupSlotFailed(contact.id, slot, result.error || 'Unknown error');
  }
}

/**
 * Mark followup slot as successfully sent
 */
async function markFollowupSlotSent(contactId: string, slot: number, instagramMessageId?: string): Promise<void> {
  await prisma.instagramContact.update({
    where: { id: contactId },
    data: {
      [`followup${slot}_status`]: 'SENT'
    }
  });
}

/**
 * Mark followup slot as failed
 */
async function markFollowupSlotFailed(contactId: string, slot: number, errorMessage: string): Promise<void> {
  await prisma.instagramContact.update({
    where: { id: contactId },
    data: {
      [`followup${slot}_status`]: 'FAILED'
    }
  });
}

/**
 * Mark followup slot for browser extension delivery by creating attack list entry
 */
async function markFollowupForExtension(contactId: string, slot: number, reason: string): Promise<void> {
  try {
    // Get contact details for attack list entry
    const contact = await prisma.instagramContact.findUnique({
      where: { id: contactId },
      select: {
        organizationId: true,
        instagramUserId: true,
        nickname: true,
        profilePhoto: true,
        [`followup${slot}_text`]: true,
        [`followup${slot}_scheduledAt`]: true
      }
    });

    if (!contact) {
      logger.error(`❌ Contact not found for followup extension: ${contactId}`);
      return;
    }

    const text = (contact as any)[`followup${slot}_text`];
    const scheduledAt = (contact as any)[`followup${slot}_scheduledAt`];

    if (!text) {
      logger.warn(`⚠️ No text for followup ${slot}, skipping extension queue: ${contactId}`);
      return;
    }

    // Create attack list entry
    await prisma.chromeExtensionAttackList.create({
      data: {
        organizationId: contact.organizationId,
        instagramUserId: contact.instagramUserId,
        nickname: contact.nickname,
        profilePhoto: contact.profilePhoto,
        priority: 2, // SaaS followups
        messageContent: [text], // Store followup text directly (no batch for followups)
        messageSource: 'saas_followup',
        messageBatchId: null, // No batch for individual followups
        scheduledAt: scheduledAt || new Date(),
        status: 'pending'
      }
    });

    // Mark followup as queued
    await prisma.instagramContact.update({
      where: { id: contactId },
      data: {
        [`followup${slot}_status`]: 'QUEUED'
      }
    });

    logger.info(`✅ Followup ${slot} queued for extension delivery (${reason}): ${contact.nickname || contact.instagramUserId}`);

  } catch (error) {
    logger.error(`❌ Failed to mark followup ${slot} for extension:`, error);
    
    // Fallback: just mark as pending for extension
    await prisma.instagramContact.update({
      where: { id: contactId },
      data: {
        [`followup${slot}_sendViaExtension`]: true,
        [`followup${slot}_status`]: 'PENDING'
      }
    });
  }
}

/**
 * Mark all pending followups for a contact as failed
 */
async function markContactFollowupsFailed(contactId: string, errorMessage: string): Promise<void> {
  const updateData: any = {};
  
  for (let slot = 1; slot <= 6; slot++) {
    updateData[`followup${slot}_status`] = 'FAILED';
  }

  await prisma.instagramContact.updateMany({
    where: {
      id: contactId,
      OR: [
        { followup1_status: 'PENDING' },
        { followup2_status: 'PENDING' },
        { followup3_status: 'PENDING' },
        { followup4_status: 'PENDING' },
        { followup5_status: 'PENDING' },
        { followup6_status: 'PENDING' }
      ]
    },
    data: updateData
  });
}

/**
 * Mark all pending followups for a contact as skipped
 */
async function markContactFollowupsSkipped(contactId: string, reason: string): Promise<void> {
  await markContactFollowupsFailed(contactId, reason); // Same implementation for now
}

/**
 * Get statistics about scheduled followup messages from InstagramContact fields
 */
export async function getFollowupStats(): Promise<{
  total: number;
  pending: number;
  sent: number;
  failed: number;
  dueNow: number;
}> {
  try {
    const now = new Date();

    // Count all followup slots that have content
    const contacts = await prisma.instagramContact.findMany({
      select: {
        followup1_text: true,
        followup1_status: true,
        followup1_scheduledAt: true,
        followup2_text: true,
        followup2_status: true,
        followup2_scheduledAt: true,
        followup3_text: true,
        followup3_status: true,
        followup3_scheduledAt: true,
        followup4_text: true,
        followup4_status: true,
        followup4_scheduledAt: true,
        followup5_text: true,
        followup5_status: true,
        followup5_scheduledAt: true,
        followup6_text: true,
        followup6_status: true,
        followup6_scheduledAt: true
      }
    });

    let total = 0;
    let pending = 0;
    let sent = 0;
    let failed = 0;
    let dueNow = 0;

    for (const contact of contacts) {
      for (let slot = 1; slot <= 6; slot++) {
        const text = (contact as any)[`followup${slot}_text`];
        const status = (contact as any)[`followup${slot}_status`];
        const scheduledAt = (contact as any)[`followup${slot}_scheduledAt`];

        if (text) {
          total++;
          
          if (status === 'PENDING') {
            pending++;
            if (scheduledAt && scheduledAt <= now) {
              dueNow++;
            }
          } else if (status === 'SENT') {
            sent++;
          } else if (status === 'FAILED') {
            failed++;
          }
        }
      }
    }

    return {
      total,
      pending,
      sent,
      failed,
      dueNow
    };

  } catch (error) {
    logger.error('❌ Error getting follow-up stats:', error);
    return {
      total: 0,
      pending: 0,
      sent: 0,
      failed: 0,
      dueNow: 0
    };
  }
}

/**
 * Clean up old sent/failed followups (older than 30 days)
 */
export async function cleanupOldFollowups(): Promise<number> {
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Find contacts with old sent/failed followups and clear them
    const contacts = await prisma.instagramContact.findMany({
      where: {
        OR: [
          {
            followup1_scheduledAt: { lt: thirtyDaysAgo },
            followup1_status: { in: ['SENT', 'FAILED'] }
          },
          {
            followup2_scheduledAt: { lt: thirtyDaysAgo },
            followup2_status: { in: ['SENT', 'FAILED'] }
          },
          {
            followup3_scheduledAt: { lt: thirtyDaysAgo },
            followup3_status: { in: ['SENT', 'FAILED'] }
          },
          {
            followup4_scheduledAt: { lt: thirtyDaysAgo },
            followup4_status: { in: ['SENT', 'FAILED'] }
          },
          {
            followup5_scheduledAt: { lt: thirtyDaysAgo },
            followup5_status: { in: ['SENT', 'FAILED'] }
          },
          {
            followup6_scheduledAt: { lt: thirtyDaysAgo },
            followup6_status: { in: ['SENT', 'FAILED'] }
          }
        ]
      },
      select: {
        id: true,
        followup1_scheduledAt: true,
        followup1_status: true,
        followup2_scheduledAt: true,
        followup2_status: true,
        followup3_scheduledAt: true,
        followup3_status: true,
        followup4_scheduledAt: true,
        followup4_status: true,
        followup5_scheduledAt: true,
        followup5_status: true,
        followup6_scheduledAt: true,
        followup6_status: true
      }
    });

    let cleanedCount = 0;

    for (const contact of contacts) {
      const updateData: any = {};
      
      for (let slot = 1; slot <= 6; slot++) {
        const scheduledAt = (contact as any)[`followup${slot}_scheduledAt`];
        const status = (contact as any)[`followup${slot}_status`];
        
        if (scheduledAt && scheduledAt < thirtyDaysAgo && ['SENT', 'FAILED'].includes(status)) {
          updateData[`followup${slot}_text`] = null;
          updateData[`followup${slot}_scheduledAt`] = null;
          updateData[`followup${slot}_status`] = null;
          updateData[`followup${slot}_sendViaExtension`] = false;
          cleanedCount++;
        }
      }

      if (Object.keys(updateData).length > 0) {
        await prisma.instagramContact.update({
          where: { id: contact.id },
          data: updateData
        });
      }
    }

    logger.info(`🧹 Cleaned up ${cleanedCount} old followup slots`);
    return cleanedCount;

  } catch (error) {
    logger.error('❌ Error cleaning up old followups:', error);
    return 0;
  }
}

/**
 * Retry failed followups that are eligible for retry
 */
export async function retryFailedFollowups(): Promise<void> {
  try {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    // Find contacts with recently failed followups
    const contactsWithFailedFollowups = await prisma.instagramContact.findMany({
      where: {
        OR: [
          {
            followup1_scheduledAt: { gte: twentyFourHoursAgo },
            followup1_status: 'FAILED'
          },
          {
            followup2_scheduledAt: { gte: twentyFourHoursAgo },
            followup2_status: 'FAILED'
          },
          {
            followup3_scheduledAt: { gte: twentyFourHoursAgo },
            followup3_status: 'FAILED'
          },
          {
            followup4_scheduledAt: { gte: twentyFourHoursAgo },
            followup4_status: 'FAILED'
          },
          {
            followup5_scheduledAt: { gte: twentyFourHoursAgo },
            followup5_status: 'FAILED'
          },
          {
            followup6_scheduledAt: { gte: twentyFourHoursAgo },
            followup6_status: 'FAILED'
          }
        ]
      },
      include: {
        organization: {
          select: {
            id: true,
            slug: true,
            instagramToken: true,
            instagramIsConnected: true
          }
        }
      },
      take: 10 // Limit to 10 contacts per run
    });

    if (contactsWithFailedFollowups.length === 0) {
      logger.info('🔄 No failed followups to retry');
      return;
    }

    logger.info(`🔄 Retrying failed followups for ${contactsWithFailedFollowups.length} contacts`);

    let retriedCount = 0;

    for (const contact of contactsWithFailedFollowups) {
      const updateData: any = {};
      
      for (let slot = 1; slot <= 6; slot++) {
        const scheduledAt = (contact as any)[`followup${slot}_scheduledAt`];
        const status = (contact as any)[`followup${slot}_status`];
        
        if (scheduledAt && scheduledAt >= twentyFourHoursAgo && status === 'FAILED') {
          // Reset to pending for retry
          updateData[`followup${slot}_status`] = 'PENDING';
          updateData[`followup${slot}_scheduledAt`] = new Date(); // Retry now
          retriedCount++;
        }
      }

      if (Object.keys(updateData).length > 0) {
        await prisma.instagramContact.update({
          where: { id: contact.id },
          data: updateData
        });
      }
    }

    logger.info(`✅ ${retriedCount} followup slots queued for retry`);

  } catch (error) {
    logger.error('❌ Error retrying failed followups:', error);
  }
}

/**
 * Sync attack list status back to original followup fields
 * Called when attack list items are sent or failed by the extension
 */
export async function syncAttackListStatusToFollowups(): Promise<void> {
  try {
    logger.info('🔄 Syncing attack list status to followup fields...');

    // Get attack list items from followups that have been sent or failed
    const processedFollowups = await prisma.chromeExtensionAttackList.findMany({
      where: {
        messageSource: 'saas_followup',
        status: { in: ['sent', 'failed'] },
        // Add a field to track if we've synced this back (optional optimization)
      },
      take: 100 // Process max 100 per run
    });

    if (processedFollowups.length === 0) {
      logger.info('📭 No processed followups to sync');
      return;
    }

    logger.info(`🔄 Syncing ${processedFollowups.length} processed followups back to contacts`);

    let synced = 0;
    let failed = 0;

    for (const attackItem of processedFollowups) {
      try {
        await syncSingleAttackListItemToFollowup(attackItem);
        synced++;
      } catch (error) {
        logger.error(`❌ Failed to sync attack list item ${attackItem.id}:`, error);
        failed++;
      }
    }

    logger.info(`✅ Followup sync completed: ${synced} synced, ${failed} failed`);

  } catch (error) {
    logger.error('❌ Error syncing attack list status to followups:', error);
  }
}

/**
 * Sync a single attack list item back to its original followup field
 */
async function syncSingleAttackListItemToFollowup(attackItem: any): Promise<void> {
  try {
    // Find the original contact and determine which followup slot this came from
    const contact = await prisma.instagramContact.findUnique({
      where: {
        instagramUserId_organizationId: {
          instagramUserId: attackItem.instagramUserId,
          organizationId: attackItem.organizationId
        }
      },
      select: {
        id: true,
        followup1_text: true,
        followup1_status: true,
        followup2_text: true,
        followup2_status: true,
        followup3_text: true,
        followup3_status: true,
        followup4_text: true,
        followup4_status: true,
        followup5_text: true,
        followup5_status: true,
        followup6_text: true,
        followup6_status: true
      }
    });

    if (!contact) {
      logger.warn(`⚠️ Contact not found for attack list sync: ${attackItem.instagramUserId}`);
      return;
    }

    // Find which followup slot matches this attack list message
    const attackMessage = Array.isArray(attackItem.messageContent) 
      ? attackItem.messageContent[0] 
      : attackItem.messageContent;

    let matchingSlot = 0;
    for (let slot = 1; slot <= 6; slot++) {
      const followupText = (contact as any)[`followup${slot}_text`];
      const followupStatus = (contact as any)[`followup${slot}_status`];
      
      if (followupText === attackMessage && followupStatus === 'QUEUED') {
        matchingSlot = slot;
        break;
      }
    }

    if (matchingSlot === 0) {
      logger.warn(`⚠️ No matching followup slot found for attack list item: ${attackItem.id}`);
      return;
    }

    // Update the followup status based on attack list status
    const newStatus = attackItem.status === 'sent' ? 'SENT' : 'FAILED';
    
    await prisma.instagramContact.update({
      where: { id: contact.id },
      data: {
        [`followup${matchingSlot}_status`]: newStatus
      }
    });

    logger.info(`✅ Synced followup ${matchingSlot} status to ${newStatus}: ${contact.id}`);

  } catch (error) {
    logger.error('❌ Error syncing single attack list item:', error);
    throw error;
  }
}

// Legacy function name aliases for backward compatibility
export const processScheduledFollowups = processInstagramContactFollowups;
export const cleanupOldMessages = cleanupOldFollowups;
export const retryFailedMessages = retryFailedFollowups;