/**
 * Auto-responder logic for AI-powered Instagram responses
 * Handles the complete flow from AI analysis to message delivery
 */

import { prisma } from '@workspace/database/client';
import { InstagramAIService } from '@workspace/ai';
import type { 
  ConversationMessage, 
  InstagramContactContext, 
  PromptConfig,
  OpenRouterConfig,
  InstagramResponse,
  UsageTrackingContext,
  BotStyleVariableValue
} from '@workspace/ai';
import { sendInstagramTextMessage } from '~/lib/instagram-api';
import { MessageScheduler } from './message-scheduler';
import { determineDeliveryMethod } from './delivery-method';
import { getConversationIdForContact } from '~/lib/instagram-conversation-utils';
import { slackOAuthService } from '@workspace/ai';

const logger = {
  info: (message: string, ...args: unknown[]) => console.log(`[AUTO-RESPONDER] ${message}`, ...args),
  warn: (message: string, ...args: unknown[]) => console.warn(`[AUTO-RESPONDER] ${message}`, ...args),
  error: (message: string, ...args: unknown[]) => console.error(`[AUTO-RESPONDER] ${message}`, ...args)
};

/**
 * Main auto-response processing function
 * Called when customer timer expires
 */
export async function processAutoResponse(instagramContactId: string): Promise<void> {
  try {
    logger.info(`🤖 Starting auto-response processing for contact: ${instagramContactId}`);

    // Get platform settings
    const platformSettings = await prisma.platformSettings.findFirst();
    if (!platformSettings?.openRouterApiKey && !platformSettings?.zaiApiKey && !platformSettings?.anthropicApiKey) {
      logger.error('❌ No AI provider API key configured (OpenRouter, Z.AI, or Anthropic)');
      return;
    }


    // Get contact with organization data and Calendly integration
    const contact = await prisma.instagramContact.findUnique({
      where: { id: instagramContactId },
      include: {
        organization: {
          include: {
            calendlyIntegration: true
          }
        }
      }
    });

    if (!contact) {
      logger.warn(`⚠️ Contact not found: ${instagramContactId}`);
      return;
    }

    if (!contact.aiEnabled) {
      logger.info(`🚫 AI disabled for contact: ${contact.nickname || contact.instagramUserId}`);
      return;
    }

    if (!contact.organization.instagramToken || !contact.organization.instagramIsConnected) {
      logger.error('❌ Instagram not connected for organization');
      return;
    }

    logger.info(`📋 Organization followups enabled: ${contact.organization.instagramFollowupsEnabled}`);

    // Get conversation messages using conversation-based approach (like UI)
    const messages = await getConversationMessages(contact.instagramUserId, contact.organizationId, platformSettings.maxConversationMessages || 25);
    if (messages.length === 0) {
      logger.warn(`⚠️ No messages found for contact: ${instagramContactId}`);
      return;
    }

    // Determine default model based on available API keys
    let defaultModel = 'openai/gpt-4o-mini'; // OpenRouter fallback
    if (platformSettings.anthropicApiKey) {
      defaultModel = 'claude-sonnet-4-20250514'; // Anthropic fallback
    } else if (platformSettings.zaiApiKey) {
      defaultModel = 'glm-4.5'; // Z.AI fallback
    }

    // Build AI configuration (supports OpenRouter, Z.AI, and Anthropic)
    const aiConfig: OpenRouterConfig & { zaiApiKey?: string; anthropicApiKey?: string; enableThinking?: boolean; cacheDuration?: '5m' | '1h' } = {
      apiKey: platformSettings.openRouterApiKey || '',
      zaiApiKey: platformSettings.zaiApiKey || undefined,
      anthropicApiKey: platformSettings.anthropicApiKey || undefined,
      model: platformSettings.aiModel || defaultModel,
      enableReasoning: platformSettings.enableReasoning || false,
      reasoningMaxTokens: platformSettings.reasoningMaxTokens || undefined,
      enableThinking: platformSettings.enableThinking || false,
      cacheDuration: platformSettings.promptCacheDuration as '5m' | '1h' | undefined,
    };

    // Get bot style and build prompt configuration
    let botStylePrompt = 'Default prompt template not available';
    let botStyleVariables: BotStyleVariableValue | undefined;
    
    if (contact.organization.botStyleId) {
      // Get the bot style to get the prompt template
      const botStyle = await prisma.botStyle.findUnique({
        where: { id: contact.organization.botStyleId }
      });
      
      if (botStyle) {
        botStylePrompt = botStyle.prompt;
        logger.info(`🎨 Using bot style: ${botStyle.title}`);
        
        // Get bot style variables for template processing
        const { getOrganizationBotStyleVariableValues } = await import('~/data/saas/get-bot-style-variables');
        botStyleVariables = await getOrganizationBotStyleVariableValues(
          contact.organizationId,
          contact.organization.botStyleId
        );
        logger.info('📊 Bot style variables loaded:', Object.keys(botStyleVariables || {}));
      } else {
        logger.warn('⚠️ Bot style not found for organization');
      }
    } else {
      // Try to get the default bot style
      const defaultBotStyle = await prisma.botStyle.findFirst({
        where: { isDefault: true },
        orderBy: { createdAt: 'desc' }
      });
      
      if (defaultBotStyle) {
        botStylePrompt = defaultBotStyle.prompt;
        logger.info(`🎨 Using default bot style: ${defaultBotStyle.title}`);
      } else {
        logger.warn('⚠️ No bot style ID found for organization and no default bot style available');
      }
    }

    const promptConfig: PromptConfig = {
      botStylePrompt,
      followupsEnabled: contact.organization.instagramFollowupsEnabled,
      maxConversationMessages: platformSettings.maxConversationMessages || 25
    };

    // Build contact context
    const contactContext: InstagramContactContext = {
      id: contact.id,
      username: contact.nickname || contact.instagramUserId,
      fullName: contact.nickname || undefined,
      engagementPriority: contact.engagementPriority,
      lastContactMessageAt: contact.lastContactMessageAt || undefined
    };

    // Get conversation ID for usage tracking
    const conversationId = await getConversationIdForContact(contact.instagramUserId, contact.organizationId);

    // Build usage tracking context
    const usageContext: UsageTrackingContext = {
      organizationId: contact.organizationId,
      instagramContactId: instagramContactId,
      conversationId: conversationId || undefined,
      requestType: 'auto_response'
    };

    // Generate AI response
    logger.info(`🧠 Generating AI response for contact: ${contact.nickname || contact.instagramUserId}`);
    logger.info(`🔧 AI Config: model=${aiConfig.model}, hasOpenRouter=${!!platformSettings.openRouterApiKey}, hasZAI=${!!platformSettings.zaiApiKey}, hasAnthropic=${!!platformSettings.anthropicApiKey}`);
    const aiService = new InstagramAIService(aiConfig);
    
    const aiResponse = await aiService.generateResponse({
      messages,
      contact: contactContext,
      promptConfig,
      aiConfig,
      botStyleVariables,
      calendlyIntegration: contact.organization.calendlyIntegration || undefined
    }, usageContext);

    // Check if contact is disqualified
    if (aiResponse.isDisqualified) {
      logger.info(`🚫 Contact disqualified by AI: ${contact.nickname || contact.instagramUserId}`);
      
      // Update contact stage, disable AI, and save notes
      await prisma.instagramContact.update({
        where: { id: instagramContactId },
        data: {
          stage: 'DISQUALIFIED',
          aiEnabled: false,
          notes: aiResponse.notes
        }
      });
      
      return;
    }

    // Store previous stage for comparison
    const previousStage = contact.stage;

    // Update contact with AI analysis results
    await updateContactFromAIResponse(instagramContactId, aiResponse);

    // Check if contact converted and send Slack notification
    if (aiResponse.instagramStage === 'CONVERTED' && previousStage !== 'CONVERTED') {
      logger.info(`🎯 Contact converted! Sending Slack notification for: ${contact.nickname || contact.instagramUserId}`);
      await handleConvertedStageNotification(contact, aiResponse, contact.organization);
    }

    // Process immediate messages
    await processImmediateMessages(contact, aiResponse);

    // Schedule follow-up messages
    await scheduleFollowupMessages(contact, aiResponse);

    logger.info(`✅ Auto-response processing completed for contact: ${contact.nickname || contact.instagramUserId}`);

  } catch (error) {
    logger.error('❌ Error in auto-response processing:', error);
  }
}

/**
 * Get conversation messages for AI analysis using conversation-based approach (like UI)
 */
async function getConversationMessages(instagramUserId: string, organizationId: string, maxMessages: number): Promise<ConversationMessage[]> {
  try {
    // Step 1: Get conversationId for this contact (same as UI)
    const conversationId = await getConversationIdForContact(instagramUserId, organizationId);
    
    if (!conversationId) {
      logger.warn(`⚠️ No conversationId found for user ${instagramUserId}`);
      return [];
    }

    logger.info(`🔍 Using conversationId: ${conversationId} for user ${instagramUserId}`);

    // Step 2: Get messages by conversationId (same as UI), respecting maxConversationMessages
    const messages = await prisma.instagramMessage.findMany({
      where: {
        organizationId,
        conversationId
      },
      orderBy: { instagramCreatedTime: 'desc' }, // Get newest messages first
      take: maxMessages // Respect SaaS admin setting
    });

    logger.info(`💬 Fetched ${messages.length} total messages for conversationId ${conversationId} (limit: ${maxMessages})`);
    
    const userMessages = messages.filter(msg => !msg.isFromBusiness);
    const businessMessages = messages.filter(msg => msg.isFromBusiness);
    
    logger.info(`📊 Message breakdown: ${userMessages.length} user messages, ${businessMessages.length} business messages`);
    
    if (businessMessages.length > 0) {
      logger.info(`🤖 Business messages preview:`);
      businessMessages.slice(-3).forEach((msg, index) => {
        logger.info(`  ${index + 1}. [${msg.instagramCreatedTime.toISOString()}] ${msg.content?.substring(0, 50)}...`);
      });
    }

    const conversationMessages = messages
      .reverse() // Reverse to chronological order (oldest first) since we query desc
      .map(msg => ({
        id: msg.id,
        content: msg.content || '[No content]',
        isFromUser: !msg.isFromBusiness,
        createdAt: msg.instagramCreatedTime
      }));

    logger.info(`🔄 Returning ${conversationMessages.length} conversation messages (${conversationMessages.filter(m => m.isFromUser).length} user, ${conversationMessages.filter(m => !m.isFromUser).length} business)`);

    return conversationMessages;

  } catch (error) {
    logger.error('❌ Error fetching conversation messages:', error);
    return [];
  }
}



/**
 * Update contact with AI response data
 */
async function updateContactFromAIResponse(contactId: string, aiResponse: InstagramResponse): Promise<void> {
  try {
    await prisma.instagramContact.update({
      where: { id: contactId },
      data: {
        stage: aiResponse.instagramStage,
        engagementPriority: aiResponse.engagementPriority,
        notes: aiResponse.notes
      }
    });

    logger.info(`📝 Updated contact with AI analysis: stage=${aiResponse.instagramStage}, priority=${aiResponse.engagementPriority}`);

  } catch (error) {
    logger.error('❌ Error updating contact from AI response:', error);
  }
}

/**
 * Handle Slack notification when contact converts
 */
async function handleConvertedStageNotification(
  contact: any,
  aiResponse: InstagramResponse,
  organization: any
): Promise<void> {
  try {
    // Check if Slack is configured with OAuth
    const slackIntegration = await slackOAuthService.getIntegration(organization.id);
    
    if (!slackIntegration?.enabled || !slackIntegration.accessToken) {
      logger.info('🔕 Slack integration not configured or disabled for organization');
      return;
    }

    // Build conversation link
    const conversationUrl = `https://app.setorai.com/organizations/${organization.slug}/instagram-contacts/${contact.id}`;
    
    // Send to Slack using OAuth service
    const slackMessage = {
      text: `🎉 New Booking: ${contact.nickname || contact.instagramUserId}`,
      blocks: [
        {
          type: "header",
          text: {
            type: "plain_text",
            text: "🎉 New Call Booked!"
          }
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*Contact:* ${contact.nickname || contact.instagramUserId}`
          }
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*Notes from AI:*\n${aiResponse.notes || 'Booking confirmed. No additional notes.'}`
          }
        },
        {
          type: "actions",
          elements: [
            {
              type: "button",
              text: {
                type: "plain_text",
                text: "View Conversation 💬"
              },
              url: conversationUrl,
              style: "primary"
            }
          ]
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `Stage changed to CONVERTED • ${new Date().toLocaleString()}`
            }
          ]
        }
      ]
    };

    const success = await slackOAuthService.sendNotification(organization.id, slackMessage);

    if (success) {
      logger.info(`✅ Slack notification sent successfully for contact: ${contact.nickname || contact.instagramUserId}`);
    } else {
      logger.error(`❌ Failed to send Slack notification for contact: ${contact.nickname || contact.instagramUserId}`);
      logger.error(`💡 Troubleshooting: Ensure bot is invited to channel with '/invite @BotName' or check OAuth scopes include 'chat:write.public'`);
    }

  } catch (error) {
    logger.error('❌ Error sending Slack notification:', error);
  }
}

/**
 * Process immediate messages (1-4 with delays)
 */
async function processImmediateMessages(contact: any, aiResponse: InstagramResponse): Promise<void> {
  try {
    const immediateMessages = [
      aiResponse.message1,
      aiResponse.message2,
      aiResponse.message3,
      aiResponse.message4
    ].filter(Boolean);

    if (immediateMessages.length === 0) {
      logger.info(`📭 No immediate messages to send for contact: ${contact.nickname || contact.instagramUserId}`);
      return;
    }

    logger.info(`📤 Sending ${immediateMessages.length} immediate messages for contact: ${contact.nickname || contact.instagramUserId}`);


    // Send messages with delays between each
    for (let i = 0; i < immediateMessages.length; i++) {
      const message = immediateMessages[i];
      
      if (!message) {
        logger.warn(`⚠️ Skipping empty message ${i + 1}`);
        continue;
      }
      
      if (i > 0) {
        // Wait 10-15 seconds between messages
        const delayMs = Math.floor(Math.random() * 5000) + 10000; // 10-15s
        logger.info(`⏳ Waiting ${Math.floor(delayMs / 1000)}s before sending next message...`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }

      const result = await sendInstagramTextMessage(
        contact.instagramUserId,
        message,
        contact.organization.instagramToken!
      );

      if (result.success) {
        logger.info(`✅ Immediate message ${i + 1} sent successfully: ${result.messageId}`);
        
        // Store message in database with AI reasoning (for first message)
        if (i === 0 && result.messageId && aiResponse.aiReasoning) {
          try {
            await prisma.instagramMessage.upsert({
              where: {
                messageId_organizationId: {
                  messageId: result.messageId,
                  organizationId: contact.organizationId
                }
              },
              update: {
                aiReasoning: aiResponse.aiReasoning
              },
              create: {
                organizationId: contact.organizationId,
                conversationId: await getConversationIdForContact(contact.instagramUserId, contact.organizationId) || `${contact.instagramUserId}_${Date.now()}`,
                instagramUserId: contact.instagramUserId,
                messageId: result.messageId,
                isFromBusiness: true,
                messageType: 'text',
                content: message,
                aiReasoning: aiResponse.aiReasoning,
                instagramCreatedTime: new Date(),
              }
            });
            logger.info(`💾 Stored message with AI reasoning (length: ${aiResponse.aiReasoning?.length})`);
          } catch (error) {
            logger.warn(`⚠️ Failed to store reasoning for message ${result.messageId}:`, error);
          }
        }
      } else {
        logger.error(`❌ Failed to send immediate message ${i + 1}:`, result.error);
        break; // Stop sending if one fails
      }
    }

  } catch (error) {
    logger.error('❌ Error processing immediate messages:', error);
  }
}

/**
 * Schedule follow-up messages for later delivery
 */
async function scheduleFollowupMessages(contact: any, aiResponse: InstagramResponse): Promise<void> {
  try {
    // Check if followups are enabled for this organization
    if (!contact.organization.instagramFollowupsEnabled) {
      logger.info(`🚫 Follow-ups disabled for organization: ${contact.organization.name || contact.organization.slug}`);
      return;
    }

    logger.info(`📅 Scheduling follow-up messages for contact: ${contact.nickname || contact.instagramUserId}`);

    const messageScheduler = new MessageScheduler();
    const followupIds = await messageScheduler.scheduleFollowupMessages(contact.id, aiResponse);

    if (followupIds.length > 0) {
      logger.info(`✅ ${followupIds.length} follow-up messages scheduled successfully for contact: ${contact.nickname || contact.instagramUserId}`);
    } else {
      logger.info(`📭 No follow-up messages to schedule for contact: ${contact.nickname || contact.instagramUserId}`);
    }

  } catch (error) {
    logger.error('❌ Error scheduling follow-up messages:', error);
  }
}