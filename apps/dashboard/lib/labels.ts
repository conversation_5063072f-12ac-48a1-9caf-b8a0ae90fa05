import { AuthErrorCode } from '@workspace/auth/errors';
import { Provider } from '@workspace/auth/providers.types';
import {
  FeedbackCategory,
  InstagramStage,
  Role
} from '@workspace/database';

export const instagramStageLabel: Record<InstagramStage, string> = {
  [InstagramStage.FIRSTCONTACT]: 'First Contact',
  [InstagramStage.RAPPORT]: 'Rapport',
  [InstagramStage.DISCOVERY]: 'Discovery',
  [InstagramStage.DISSONANCE]: 'Dissonance',
  [InstagramStage.VISION]: 'Vision',
  [InstagramStage.PROPOSAL]: 'Proposal',
  [InstagramStage.QUALIFIED]: 'Qualified',
  [InstagramStage.FORMSENT]: 'Form Sent',
  [InstagramStage.CONVERTED]: 'Converted',
  [InstagramStage.DISQUALIFIED]: 'Disqualified',
  [InstagramStage.SUSPICIOUS]: 'Suspicious'
};

export const instagramStageColor: Record<InstagramStage, string> = {
  [InstagramStage.FIRSTCONTACT]: 'bg-blue-800',
  [InstagramStage.RAPPORT]: 'bg-purple-800',
  [InstagramStage.DISCOVERY]: 'bg-orange-800',
  [InstagramStage.DISSONANCE]: 'bg-amber-800',
  [InstagramStage.VISION]: 'bg-emerald-800',
  [InstagramStage.PROPOSAL]: 'bg-indigo-800',
  [InstagramStage.QUALIFIED]: 'bg-cyan-800',
  [InstagramStage.FORMSENT]: 'bg-pink-800',
  [InstagramStage.CONVERTED]: 'bg-green-800',
  [InstagramStage.DISQUALIFIED]: 'bg-red-800',
  [InstagramStage.SUSPICIOUS]: 'bg-yellow-800'
};

export const roleLabels: Record<Role, string> = {
  [Role.MEMBER]: 'Member',
  [Role.ADMIN]: 'Admin'
};

export const feedbackCategoryLabels: Record<FeedbackCategory, string> = {
  [FeedbackCategory.SUGGESTION]: 'Suggestion',
  [FeedbackCategory.PROBLEM]: 'Problem',
  [FeedbackCategory.QUESTION]: 'Question'
};

export const identityProviderLabels: Record<Provider, string> = {
  [Provider.Credentials]: 'Credentials',
  [Provider.TotpCode]: 'TOTP code',
  [Provider.RecoveryCode]: 'Recovery code',
  [Provider.Google]: 'Google',
  [Provider.MicrosoftEntraId]: 'Microsoft'
};

export const authErrorLabels: Record<AuthErrorCode, string> = {
  [AuthErrorCode.NewEmailConflict]: 'Email already exists.',
  [AuthErrorCode.UnverifiedEmail]: 'Email is not verified.',
  [AuthErrorCode.IncorrectEmailOrPassword]: 'Email or password is not correct.',
  [AuthErrorCode.TotpCodeRequired]: 'TOTP code is required.',
  [AuthErrorCode.IncorrectTotpCode]: 'The TOTP code is not correct.',
  [AuthErrorCode.MissingRecoveryCodes]: 'Missing recovery codes.',
  [AuthErrorCode.IncorrectRecoveryCode]: 'The recovery code is not correct.',
  [AuthErrorCode.RequestExpired]: 'Request has expired.',
  [AuthErrorCode.RateLimitExceeded]: 'Rate limit exceeded.',
  [AuthErrorCode.IllegalOAuthProvider]: 'Illegal OAuth provider.',
  [AuthErrorCode.InternalServerError]:
    'Something went wrong. Please try again later.',
  [AuthErrorCode.MissingOAuthEmail]: 'Missing OAuth email.',
  [AuthErrorCode.AlreadyLinked]: 'OAuth account has been already linked.',
  [AuthErrorCode.RequiresExplicitLinking]:
    'Please sign in first to link this account',
  [AuthErrorCode.UnknownError]: 'Unknown error.'
};
