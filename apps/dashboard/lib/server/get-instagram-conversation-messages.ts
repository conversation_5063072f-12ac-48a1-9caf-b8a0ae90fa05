import 'server-only';

import { getInstagramMessages } from '~/data/instagram-messages/get-instagram-messages';
import { getConversationIdForContact } from '~/lib/instagram-conversation-utils';

export type ConversationMessage = {
  id: string;
  content: string;
  from: 'user' | 'assistant';
  timestamp: Date;
  messageType: string;
  attachmentUrl?: string;
};

export async function getInstagramConversationMessages(
  instagramUserId: string,
  organizationId: string
): Promise<ConversationMessage[]> {
  try {
    // Get conversation ID for this contact
    const conversationId = await getConversationIdForContact(instagramUserId, organizationId);
    
    if (!conversationId) {
      return [];
    }

    // Get messages for this conversation
    const result = await getInstagramMessages({
      conversationId,
      pageIndex: 0,
      pageSize: 20 // Get latest messages for initial load
    });

    // Transform messages for UI
    // Business messages (us) should appear on right, customer messages on left
    const transformedMessages: ConversationMessage[] = result.messages.map(msg => ({
      id: msg.id,
      content: msg.content || (msg.attachmentUrl ? `[${msg.messageType} attachment]` : '[no content]'),
      from: msg.isFromBusiness ? 'user' : 'assistant', // Swapped: business=user (right), customer=assistant (left)
      timestamp: msg.instagramCreatedTime,
      messageType: msg.messageType,
      attachmentUrl: msg.attachmentUrl || undefined
    }));

    return transformedMessages;

  } catch (error) {
    console.error('❌ Error loading Instagram conversation messages:', error);
    return [];
  }
}