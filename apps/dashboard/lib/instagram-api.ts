/**
 * Instagram API service for sending messages
 * Simple KISS implementation for message sending only
 * Webhook handles all message storage (incoming + outgoing)
 */

import { env } from '~/env';

const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INSTAGRAM-API] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[INSTAGRAM-API] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[INSTAGRAM-API] ${message}`, ...args)
};

/**
 * Send a text message to an Instagram user
 */
export async function sendInstagramTextMessage(
  recipientId: string,
  text: string,
  accessToken: string
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    logger.info(`📤 Sending text message to Instagram user ${recipientId}`);

    if (!text || text.trim().length === 0) {
      return { success: false, error: 'Message text is required' };
    }

    if (text.length > 1000) {
      return { success: false, error: 'Message text must be 1000 characters or less' };
    }

    const response = await fetch('https://graph.instagram.com/v23.0/me/messages', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        recipient: {
          id: recipientId
        },
        message: {
          text: text.trim()
        }
      })
    });

    const data = await response.json();

    if (!response.ok) {
      logger.error(`❌ Failed to send text message:`, data);
      return { 
        success: false, 
        error: data.error?.message || 'Failed to send message' 
      };
    }

    logger.info(`✅ Text message sent successfully, message ID: ${data.message_id}`);
    
    return { 
      success: true, 
      messageId: data.message_id 
    };

  } catch (error) {
    logger.error(`❌ Error sending text message:`, error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Send an image message to an Instagram user
 */
export async function sendInstagramImageMessage(
  recipientId: string,
  imageUrl: string,
  accessToken: string
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    logger.info(`📤 Sending image message to Instagram user ${recipientId}`);

    if (!imageUrl || !isValidUrl(imageUrl)) {
      return { success: false, error: 'Valid image URL is required' };
    }

    const response = await fetch('https://graph.instagram.com/v23.0/me/messages', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        recipient: {
          id: recipientId
        },
        message: {
          attachment: {
            type: 'image',
            payload: {
              url: imageUrl
            }
          }
        }
      })
    });

    const data = await response.json();

    if (!response.ok) {
      logger.error(`❌ Failed to send image message:`, data);
      return { 
        success: false, 
        error: data.error?.message || 'Failed to send image message' 
      };
    }

    logger.info(`✅ Image message sent successfully, message ID: ${data.message_id}`);
    
    return { 
      success: true, 
      messageId: data.message_id 
    };

  } catch (error) {
    logger.error(`❌ Error sending image message:`, error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Send a heart sticker to an Instagram user
 */
export async function sendInstagramHeartSticker(
  recipientId: string,
  accessToken: string
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    logger.info(`📤 Sending heart sticker to Instagram user ${recipientId}`);

    const response = await fetch('https://graph.instagram.com/v23.0/me/messages', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        recipient: {
          id: recipientId
        },
        message: {
          attachment: {
            type: 'like_heart'
          }
        }
      })
    });

    const data = await response.json();

    if (!response.ok) {
      logger.error(`❌ Failed to send heart sticker:`, data);
      return { 
        success: false, 
        error: data.error?.message || 'Failed to send heart sticker' 
      };
    }

    logger.info(`✅ Heart sticker sent successfully, message ID: ${data.message_id}`);
    
    return { 
      success: true, 
      messageId: data.message_id 
    };

  } catch (error) {
    logger.error(`❌ Error sending heart sticker:`, error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Get conversation messages from Instagram API (for initial sync)
 */
export async function getInstagramConversationMessages(
  conversationId: string,
  accessToken: string,
  nextUrl?: string
): Promise<{
  success: boolean;
  messages: Array<{
    id: string;
    created_time: string;
    from?: {
      id: string;
      username?: string;
    };
    message?: {
      text?: string;
    };
    attachments?: Array<{
      type: string;
      payload: {
        url?: string;
      };
    }>;
  }>;
  nextUrl?: string;
  error?: string;
}> {
  try {
    logger.info(`📥 Fetching messages for conversation ${conversationId}`);

    const url = nextUrl || `https://graph.instagram.com/v23.0/${conversationId}?fields=messages&access_token=${accessToken}`;
    
    const response = await fetch(url);
    const data = await response.json();

    if (!response.ok) {
      logger.error(`❌ Failed to fetch conversation messages:`, data);
      return { 
        success: false, 
        messages: [],
        error: data.error?.message || 'Failed to fetch messages' 
      };
    }

    const messages = data.messages?.data || [];
    logger.info(`📥 Fetched ${messages.length} messages from conversation`);
    
    return { 
      success: true, 
      messages,
      nextUrl: data.messages?.paging?.next
    };

  } catch (error) {
    logger.error(`❌ Error fetching conversation messages:`, error);
    return { 
      success: false, 
      messages: [],
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Utility function to validate URLs
 */
function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}