{"extends": "@workspace/typescript-config/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"~/*": ["./*"], "content-collections": ["./.content-collections/generated"], "@workspace/ui/*": ["../../packages/ui/src/*"]}}, "include": ["next-env.d.ts", "next-page.d.ts", "next-params.d.ts", "**/*.ts", "**/*.tsx", "*.mjs", ".next/types/**/*.ts"], "exclude": [".cache", ".content-collections", ".next", ".turbo", "build", "dist", "node_modules"]}