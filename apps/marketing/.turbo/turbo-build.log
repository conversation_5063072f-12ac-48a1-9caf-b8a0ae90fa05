
> marketing@0.0.0 build /Users/<USER>/Documents/GitHub/AIChromaPRO/apps/marketing
> content-collections build && next build --turbopack

build started ...
... finished build of 3 collections and 7 documents in 24ms
Starting content-collections content-collections.ts
build started ...
... finished build of 3 collections and 7 documents in 49ms
   ▲ Next.js 15.3.3 (Turbopack)

   Creating an optimized production build ...
 ✓ Compiled successfully in 18.9s
   Linting and checking validity of types ...
   Collecting page data ...
❌ Invalid environment variables: [
  {
    code: 'invalid_type',
    expected: 'string',
    received: 'undefined',
    path: [ 'NEXT_PUBLIC_DASHBOARD_URL' ],
    message: 'Required'
  },
  {
    code: 'invalid_type',
    expected: 'string',
    received: 'undefined',
    path: [ 'NEXT_PUBLIC_MARKETING_URL' ],
    message: 'Required'
  }
]
Error: Invalid environment variables
    at j (.next/server/chunks/ssr/_6de77f32._.js:1:958)
    at e (.next/server/chunks/ssr/_6de77f32._.js:1:1133)
    at e (.next/server/chunks/ssr/_6de77f32._.js:1:1902)
    at b (.next/server/chunks/ssr/_6de77f32._.js:1:74027)
    at 65038 (.next/server/chunks/ssr/[root-of-the-server]__bdfd9c83._.js:1:410)
    at instantiateModule (.next/server/chunks/ssr/[turbopack]_runtime.js:594:23)
    at getOrInstantiateModuleFromParent (.next/server/chunks/ssr/[turbopack]_runtime.js:653:12)
    at esmImport (.next/server/chunks/ssr/[turbopack]_runtime.js:132:20)
    at 76188 (.next/server/chunks/ssr/[root-of-the-server]__bdfd9c83._.js:1:10723)
    at instantiateModule (.next/server/chunks/ssr/[turbopack]_runtime.js:594:23)
 ⚠ Support for Turbopack builds is experimental. We don't recommend deploying mission-critical applications to production.

- Turbopack currently always builds production sourcemaps for the browser. This will include project sourcecode if deployed to production.
- It is expected that your bundle size might be different from `next build` with webpack. This will be improved as we work towards stability.
- This build is without disk caching; subsequent builds will become faster when disk caching becomes available.
- When comparing output to webpack builds, make sure to first clear the Next.js cache by deleting the `.next` directory.

Provide feedback for Turbopack builds at https://github.com/vercel/next.js/discussions/77721

> Build error occurred
[Error: Failed to collect page data for /_not-found] { type: 'Error' }
 ELIFECYCLE  Command failed with exit code 1.
