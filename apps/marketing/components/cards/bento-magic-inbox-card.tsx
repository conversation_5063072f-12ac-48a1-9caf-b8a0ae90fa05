'use client';

import * as React from 'react';
import { ArrowLeftRightIcon } from 'lucide-react';
import { motion } from 'motion/react';

import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle
} from '@workspace/ui/components/card';
import { cn } from '@workspace/ui/lib/utils';

function AppleCalendar(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="267"
      height="268"
      viewBox="0 0 267 268"
      fill="none"
      {...props}
    >
      <path
        d="M208.2 266.5H58.4C26.6 266.5 0.899994 240.7 0.899994 209V59.3C0.899994 27.5 26.7 1.8 58.4 1.8H208.1C239.9 1.8 265.6 27.6 265.6 59.3V209C265.7 240.8 239.9 266.5 208.2 266.5Z"
        fill="#FCFCFC"
      />
      <path
        d="M198 111V119.8C175.7 154 161.9 189.5 156.5 226.3H146.6C152.3 189.2 166.3 153.7 188.8 119.8H130.7V111H198Z"
        fill="#5A5A5A"
      />
      <path
        d="M91.8 111C90.9 114.9 89.3 119.8 85.6 122.8C82 125.8 75 127.3 68.2 127.7V133.9H91.7V226.2H101.4V111H91.8Z"
        fill="#5A5A5A"
      />
      <path
        d="M206.5 267.4H60.2C27 267.4 0.100006 240.4 0.100006 207.3V61C0.100006 27.8 27.1 0.900002 60.2 0.900002H206.5C239.7 0.900002 266.6 27.9 266.6 61V207.3C266.7 240.4 239.7 267.4 206.5 267.4ZM60.2 2.6C28 2.6 1.8 28.8 1.8 61V207.3C1.8 239.5 28 265.7 60.2 265.7H206.5C238.7 265.7 264.9 239.5 264.9 207.3V61C264.9 28.8 238.7 2.6 206.5 2.6H60.2Z"
        fill="#D8D8D8"
      />
      <path
        d="M265.8 80.8V60.7C265.8 28 239.3 1.4 206.5 1.4H60.2C27.5 1.4 0.899994 27.9 0.899994 60.7V80.8H265.8Z"
        fill="#E9574E"
      />
      <path
        d="M266.7 81.7H0.100006V60.7C0.100006 27.5 27.1 0.599998 60.2 0.599998H206.5C239.7 0.599998 266.6 27.6 266.6 60.7V81.7H266.7ZM1.89999 79.9H264.9V60.7C264.9 28.5 238.7 2.3 206.5 2.3H60.2C28 2.3 1.8 28.5 1.8 60.7V79.9H1.89999Z"
        fill="#E9574E"
      />
      <path
        d="M83.4 52.1C83.4 52.8 83.5 53.7 83.6 54.5C83.7 55.3 83.9 56.1 84.4 56.8C84.8 57.4 85.4 57.9 86.3 58.5C87.2 59.1 88.4 59.4 89.9 59.4C91.1 59.4 92.3 59.2 93.3 58.7C94.3 58.2 95.2 57.4 95.8 56.4C96 56 96.2 55.5 96.4 55C96.5 54.5 96.6 53.9 96.7 53.4C96.8 52.9 96.8 52.4 96.8 51.9C96.8 51.4 96.8 51 96.8 50.7V21.1H102.7V52.4C102.7 54.4 102.3 56.1 101.6 57.6C100.9 59.1 99.9 60.4 98.7 61.4C97.5 62.4 96.1 63.2 94.5 63.7C92.9 64.2 91.2 64.5 89.4 64.5C88.8 64.5 88.1 64.4 87.3 64.3C86.4 64.2 85.5 64 84.6 63.7C83.7 63.4 82.7 62.9 81.8 62.3C80.9 61.7 80 60.9 79.3 59.9C78.8 59.2 78.4 58.5 78.1 57.8C77.8 57 77.6 56.3 77.5 55.6C77.4 54.9 77.3 54.3 77.2 53.6C77.2 53 77.1 52.5 77.1 52L83.4 52.1Z"
        fill="white"
      />
      <path
        d="M120.3 21.1V47.6C120.3 48.7 120.4 49.7 120.5 50.6C120.6 51.4 120.8 52.1 120.9 52.7C121.1 53.3 121.3 53.8 121.5 54.2C121.7 54.6 121.9 55 122.2 55.3C122.9 56.2 123.6 56.9 124.5 57.5C125.4 58.1 126.2 58.5 127 58.8C127.9 59.1 128.7 59.3 129.6 59.4C130.4 59.5 131.2 59.5 132 59.5C134.9 59.5 137.1 59 138.7 58C140.3 57 141.4 55.9 142.1 54.5C142.8 53.2 143.3 51.8 143.4 50.5C143.5 49.2 143.6 48.1 143.6 47.3V21.1H149.3V47.5C149.3 48.1 149.3 48.8 149.2 49.7C149.2 50.5 149 51.5 148.8 52.5C148.6 53.5 148.2 54.6 147.8 55.7C147.3 56.8 146.7 57.9 145.8 59C144.7 60.3 143.6 61.4 142.3 62.1C141 62.9 139.8 63.5 138.5 63.9C137.2 64.3 136 64.6 134.9 64.7C133.8 64.8 132.8 64.9 132.1 64.9C131.1 64.9 129.9 64.8 128.6 64.6C127.3 64.4 125.9 64.1 124.5 63.6C123.1 63.1 121.8 62.4 120.4 61.5C119.1 60.6 117.9 59.5 116.9 58C116.6 57.5 116.3 57 116 56.4C115.7 55.8 115.4 55.1 115.2 54.3C115 53.5 114.8 52.5 114.6 51.3C114.5 50.2 114.4 48.8 114.4 47.2V21.1H120.3Z"
        fill="white"
      />
      <path
        d="M160.4 21.1H166.3V58.6H186.2V63.9H160.4V21.1V21.1Z"
        fill="white"
      />
    </svg>
  );
}

function GoogleCalendar(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="76"
      height="76"
      viewBox="186 38 76 76"
      {...props}
    >
      <path
        fill="#fff"
        d="M244 56h-40v40h40V56z"
      />
      <path
        fill="#EA4335"
        d="M244 114l18-18h-18v18z"
      />
      <path
        fill="#FBBC04"
        d="M262 56h-18v40h18V56z"
      />
      <path
        fill="#34A853"
        d="M244 96h-40v18h40V96z"
      />
      <path
        fill="#188038"
        d="M186 96v12c0 3.315 2.685 6 6 6h12V96h-18z"
      />
      <path
        fill="#1967D2"
        d="M262 56V44c0-3.315-2.685-6-6-6h-12v18h18z"
      />
      <path
        fill="#4285F4"
        d="M244 38h-52c-3.315 0-6 2.685-6 6v52h18V56h40V38z"
      />
      <path
        fill="#4285F4"
        d="M212.205 87.03c-1.495-1.01-2.53-2.485-3.095-4.435l3.47-1.43c.315 1.2.865 2.13 1.65 2.79.78.66 1.73.985 2.84.985 1.135 0 2.11-.345 2.925-1.035s1.225-1.57 1.225-2.635c0-1.09-.43-1.98-1.29-2.67-.86-.69-1.94-1.035-3.23-1.035h-2.005V74.13h1.8c1.11 0 2.045-.3 2.805-.9.76-.6 1.14-1.42 1.14-2.465 0-.93-.34-1.67-1.02-2.225-.68-.555-1.54-.835-2.585-.835-1.02 0-1.83.27-2.43.815a4.784 4.784 0 00-1.31 2.005l-3.435-1.43c.455-1.29 1.29-2.43 2.515-3.415 1.225-.985 2.79-1.48 4.69-1.48 1.405 0 2.67.27 3.79.815 1.12.545 2 1.3 2.635 2.26.635.965.95 2.045.95 3.245 0 1.225-.295 2.26-.885 3.11-.59.85-1.315 1.5-2.175 1.955v.205a6.605 6.605 0 012.79 2.175c.725.975 1.09 2.14 1.09 3.5 0 1.36-.345 2.575-1.035 3.64s-1.645 1.905-2.855 2.515c-1.215.61-2.58.92-4.095.92-1.755.005-3.375-.5-4.87-1.51zm21.315-17.22l-3.81 2.755-1.905-2.89 6.835-4.93h2.62V88h-3.74V69.81z"
      />
    </svg>
  );
}

function OutlookCalendar(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="127"
      height="113"
      className="rounded-full"
      viewBox="17 34 127 113"
      {...props}
    >
      <defs>
        <linearGradient
          id="a"
          x1="28.286"
          x2="70.714"
          y1="53.757"
          y2="127.243"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset="0"
            stopColor="#1784d9"
          ></stop>
          <stop
            offset="0.5"
            stopColor="#107ad5"
          ></stop>
          <stop
            offset="1"
            stopColor="#0a63c9"
          ></stop>
        </linearGradient>
      </defs>
      <path
        fill="#123b6d"
        d="M143.984 93.02a1.8 1.8 0 0 1-.07.52 1 1 0 0 1-.06.24c.01.01 0 .02-.01.03a1 1 0 0 1-.079.21 2 2 0 0 1-.15.3 2.5 2.5 0 0 1-1.059 1.05l-4.636 2.62-.4.23-4.936 2.78-1.139.64-21.5 12.16-.419.24-8.193 4.63-1.019.57-1.009.57-.51.29c-.17.09-.349.19-.529.29-.02.01-.03.02-.05.03a4.7 4.7 0 0 1-1.169.43h-.01a5.8 5.8 0 0 1-1.509.19 6 6 0 0 1-2.7-.62c-.14-.08-.28-.16-.42-.23-.05-.03-.1-.06-.16-.09l-6.414-3.63-.869-.49-.62-.35-.13-.07-.469-.27-.07-.04-.21-.12-.15-.09-.16-.09-.2-.11-.239-.13-.12-.07-.03-.02-.32-.18-.28-.16-.08-.04-.359-.2-18.112-10.24-3.816-2.16-1.14-.64-4.936-2.78-.524-.3-4.506-2.55a2.55 2.55 0 0 1-1.059-1.05 2 2 0 0 1-.15-.31l-.21-.99a2.65 2.65 0 0 1 1.329-2.3l.03-.03c.02 0 .04-.02.06-.02L53 88.13l.529-.3 21.316-12.05 1.379-.78 5.306-2.99.359-.21.08-.04.28-.16.32-.18.03-.02.12-.07.239-.13.2-.11.16-.09.15-.08.21-.12.07-.04.469-.27.13-.07.62-.35.869-.5 6.414-3.62c.2-.11.39-.22.58-.32a6.07 6.07 0 0 1 5.385 0c.2.1.389.21.579.32l10.731 6.06 6.674 3.77 21.321 12.05.4.23 4.636 2.61c.03 0 .05.02.07.02l.02.03a2.63 2.63 0 0 1 1.338 2.3"
      />
      <path
        fill="#28a8ea"
        d="M110 49H82v26l28 26h28V75z"
      />
      <path
        fill="#0364b8"
        d="M110 101v26H82l-.5-.42-.07-.06v-.01l-.09-.08-.18-.15-.34-.29-.03-.02-.44-.38-.01-.01-.11-.09-.05-.04-.05-.05-.59-.5h-.01l-.12-.11-.08-.07-.05-.04-.12-.1-.3-.25-.6-.49L54 103.99l-1-.82V75h29l.6.56.15.14.44.41.31.28.28.26.47.44.13.12.62.58Z"
      />
      <path
        fill="#14447d"
        d="M54 101h28v26H54z"
      />
      <path
        fill="#0078d4"
        d="M110 101h28v26h-28z"
      />
      <path
        fill="#50d9ff"
        d="M110 49h28v26h-28z"
      />
      <path
        fill="#0078d4"
        d="M53 49h29v26H53zm29 26h28v26H82z"
      />
      <path
        fill="#0358a7"
        d="M58.38 34h74.24a5.38 5.38 0 0 1 5.38 5.38V49H53v-9.62A5.38 5.38 0 0 1 58.38 34"
      />
      <path
        fill="#0a2767"
        d="m138 93.829-41.058 22.279-.192.111a2 2 0 0 1-.2.107 3 3 0 0 1-.227.093l-1.34-.751-.167-.089a3 3 0 0 1-.237-.13l-.1-.062-8.132-4.52-.664-.375-.545-.3-1.2-.675L53 92.318v32.259h85Z"
        opacity="0.1"
      />
      <path
        fill="#0a2767"
        d="m138 94.829-41.058 22.279-.192.111a2 2 0 0 1-.2.107 3 3 0 0 1-.227.093l-1.34-.751-.167-.089a3 3 0 0 1-.237-.13l-.1-.062-8.132-4.52-.664-.375-.545-.3-1.2-.675L53 93.318v32.259h85Z"
        opacity="0.1"
      />
      <path
        fill="#0a2767"
        d="m138 95.829-41.058 22.279-.192.111a2 2 0 0 1-.2.107 3 3 0 0 1-.227.093l-1.34-.751-.167-.089a3 3 0 0 1-.237-.13l-.1-.062-8.132-4.52-.664-.375-.545-.3-1.2-.675L53 94.318v32.259h85Z"
        opacity="0.1"
      />
      <path
        fill="#0a2767"
        d="m138 96.829-41.058 22.279-.192.111a2 2 0 0 1-.2.107 3 3 0 0 1-.227.093l-1.34-.751-.167-.089a3 3 0 0 1-.237-.13l-.1-.062-8.132-4.52-.664-.375-.545-.3-1.2-.675L53 95.318v32.259h85Z"
        opacity="0.1"
      />
      <path
        fill="#1490df"
        d="m142.641 95.242-.054.029-.013.008-43.8 23.765a6 6 0 0 1-.587.319 6.33 6.33 0 0 1-5.389 0 6 6 0 0 1-.587-.319l2.061 14.818 43.658 13.047a6.24 6.24 0 0 0 3.564-1.1 5.69 5.69 0 0 0 2.5-4.671V93.022a2.54 2.54 0 0 1-1.353 2.22"
      />
      <path
        d="m100.479 118.163-1.629.883a6 6 0 0 1-.587.319 6.33 6.33 0 0 1-5.388 0 6 6 0 0 1-.587-.319l2.061 14.818 43.658 13.047a6.24 6.24 0 0 0 3.565-1.1 5.77 5.77 0 0 0 2.339-3.356Z"
        opacity="0.05"
      />
      <path
        d="m143.578 143.412-44.139-24.685-.589.319a6 6 0 0 1-.587.319 6.33 6.33 0 0 1-5.388 0 6 6 0 0 1-.587-.319l2.061 14.818 43.658 13.047a6.24 6.24 0 0 0 3.565-1.1 5.85 5.85 0 0 0 2.006-2.399"
        opacity="0.05"
      />
      <path
        d="M143.088 144.284 98.406 119.3l-.143.07a6.33 6.33 0 0 1-5.388 0 6 6 0 0 1-.587-.319l2.061 14.818 43.658 13.047a6.24 6.24 0 0 0 3.565-1.1 6 6 0 0 0 1.516-1.532"
        opacity="0.05"
      />
      <path
        d="m142.444 145.069-44.866-25.092-.223-.119-.176-.1a6.33 6.33 0 0 1-4.305-.394 6 6 0 0 1-.587-.319l2.061 14.818 43.658 13.047a6.24 6.24 0 0 0 3.565-1.1 6 6 0 0 0 .873-.741"
        opacity="0.05"
      />
      <path
        fill="#28a8ea"
        d="M141.63 145.76c-.13.09-.26.18-.39.26s-.27.15-.41.22c-.16.08-.32.15-.48.22-.01 0-.02.01-.03.01q-.27.1-.54.18a3.3 3.3 0 0 1-.62.15.7.7 0 0 1-.14.03c-.11.02-.22.03-.34.04a5 5 0 0 1-.65.04H53.15a6.01 6.01 0 0 1-6.08-5.94V93.02l1.38.77.02.01c.01.01.01.01.02.01L53 96.32l8.42 4.68L82 112.44l.6.34.15.08.44.25.31.17.28.15.47.27.13.07.62.34 7.44 4.14c.19.12.39.23.59.33l2.48 1.39 1.57.88h.01l6.67 3.73 1.79 1 1.79 1 .75.42 1.04.58Z"
      />
      <path
        d="M85 63v59.62a5.3 5.3 0 0 1-.37 1.96 5.3 5.3 0 0 1-.52 1 4.8 4.8 0 0 1-.86 1 5 5 0 0 1-.51.42 5.5 5.5 0 0 1-1.03.58 5.4 5.4 0 0 1-2.09.42H47v-4h.07V93.02a2.64 2.64 0 0 1 1.33-2.3l.03-.03c.02 0 .04-.02.06-.02L53 88.13V57h26.62a4.55 4.55 0 0 1 2.38.69A6.37 6.37 0 0 1 85 63"
        opacity="0.05"
      />
      <path
        d="M84.25 63.1v58.52a5.43 5.43 0 0 1-.86 2.96 4.8 4.8 0 0 1-.84 1 3.4 3.4 0 0 1-.55.45 4 4 0 0 1-.66.4c-.1.06-.2.1-.3.15a5.3 5.3 0 0 1-2.08.42H47v-3h.07V93.02a2.64 2.64 0 0 1 1.33-2.3l.03-.03c.02 0 .04-.02.06-.02L53 88.13V57.25h25.87A4.94 4.94 0 0 1 82 58.42a6.15 6.15 0 0 1 2.25 4.68"
        opacity="0.075"
      />
      <path
        d="M83.5 63.19v57.43a5.45 5.45 0 0 1-1.5 3.82 2 2 0 0 1-.15.14 4.9 4.9 0 0 1-1.47 1c-.01 0-.02.01-.03.01a5.3 5.3 0 0 1-2.04.41H47v-2h.07V93.02a2.64 2.64 0 0 1 1.33-2.3l.03-.03c.02 0 .04-.02.06-.02L53 88.13V57.5h25.12A5.2 5.2 0 0 1 82 59.32a5.85 5.85 0 0 1 1.5 3.87"
        opacity="0.1"
      />
      <path
        d="M82.75 63.28v56.34a5.74 5.74 0 0 1-.75 2.87 5 5 0 0 1-2.29 2.09c-.12.05-.25.1-.38.14a5.1 5.1 0 0 1-1.67.28H47v-1h.07V93.02a2.64 2.64 0 0 1 1.33-2.3l.03-.03c.02 0 .04-.02.06-.02L53 88.13V57.75h24.37A5.37 5.37 0 0 1 82 60.51a5.5 5.5 0 0 1 .75 2.77"
        opacity="0.125"
      />
      <path
        d="M82 63.38v55.24a5.16 5.16 0 0 1-3.74 5.22A4.7 4.7 0 0 1 77 124H47.07V93.02a2.64 2.64 0 0 1 1.33-2.3l.03-.03c.02 0 .04-.02.06-.02L53 88.13V58h23.62A5.38 5.38 0 0 1 82 63.38"
        opacity="0.2"
      />
      <rect
        width="65"
        height="65"
        x="17"
        y="58"
        fill="url(#a)"
        rx="5.38"
      ></rect>
      <path
        fill="#fff"
        d="M35.041 81.643a14.64 14.64 0 0 1 5.744-6.343 17.37 17.37 0 0 1 9.128-2.287 16.15 16.15 0 0 1 8.444 2.169 14.5 14.5 0 0 1 5.59 6.062 19.6 19.6 0 0 1 1.958 8.916 20.65 20.65 0 0 1-2.017 9.329 14.84 14.84 0 0 1-5.755 6.274 16.8 16.8 0 0 1-8.763 2.228 16.54 16.54 0 0 1-8.632-2.193 14.7 14.7 0 0 1-5.661-6.074 19.1 19.1 0 0 1-1.977-8.811 21.2 21.2 0 0 1 1.941-9.27m6.121 14.895a9.5 9.5 0 0 0 3.231 4.175 8.44 8.44 0 0 0 5.048 1.521 8.86 8.86 0 0 0 5.39-1.568 9.1 9.1 0 0 0 3.137-4.187 16.2 16.2 0 0 0 1-5.826 17.7 17.7 0 0 0-.943-5.9A9.35 9.35 0 0 0 55 80.417a8.35 8.35 0 0 0-5.343-1.651 8.7 8.7 0 0 0-5.169 1.534 9.6 9.6 0 0 0-3.3 4.21 16.7 16.7 0 0 0-.024 12.029Z"
      />
      <path
        fill="none"
        d="M0 0h180v180H0z"
      />
    </svg>
  );
}

const DATA_MAGIC_INBOX = [
  { icon: AppleCalendar },
  { icon: GoogleCalendar },
  { icon: OutlookCalendar }
];

const MotionCard = motion.create(Card);

export function BentoMagicInboxCard({
  className,
  ...other
}: React.ComponentPropsWithoutRef<typeof MotionCard>): React.JSX.Element {
  const [active, setActive] = React.useState<number>(0);
  React.useEffect(() => {
    const interval = setInterval(() => {
      setActive((prev) => (prev + 1) % 3);
    }, 1000);

    return () => clearInterval(interval);
  }, []);
  return (
    <MotionCard
      className={cn(
        'relative h-[300px] max-h-[300px] overflow-hidden',
        className
      )}
      {...other}
    >
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Magic Inbox</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="line-clamp-2 text-sm text-muted-foreground">
          Centralize all customer communications in one shared inbox.
        </p>
        <div
          aria-hidden="true"
          className="pointer-events-none relative h-[142px] flex-auto select-none overflow-hidden"
        >
          <div className="relative flex h-full flex-col items-center justify-center">
            {/* Rings */}
            <div className="absolute blur-[1px]">
              <div className="absolute left-1/2 top-1/2 ml-[calc(-216/2/16*1rem)] mt-[calc(-216/2/16*1rem)] size-[calc(216/16*1rem)] rounded-full border opacity-60 dark:opacity-100" />
              <div className="opacity-12.5 absolute left-1/2 top-1/2 ml-[calc(-280/2/16*1rem)] mt-[calc(-280/2/16*1rem)] size-[calc(280/16*1rem)] rounded-full border opacity-50 dark:opacity-90" />
              <div className="absolute left-1/2 top-1/2 ml-[calc(-344/2/16*1rem)] mt-[calc(-344/2/16*1rem)] size-[calc(344/16*1rem)] rounded-full border opacity-40 dark:opacity-80" />
              <div className="opacity-7.5 absolute left-1/2 top-1/2 ml-[calc(-408/2/16*1rem)] mt-[calc(-408/2/16*1rem)] size-[calc(408/16*1rem)] rounded-full border opacity-30 dark:opacity-70" />
            </div>
            {/* Icons */}
            <div className="flex flex-row gap-4">
              {DATA_MAGIC_INBOX.map(({ icon: Icon }, index) => (
                <div
                  key={index}
                  className={cn(
                    'transition duration-1000',
                    active === index ? 'opacity-100' : 'opacity-25'
                  )}
                >
                  <div className="size-10 rounded-full border-2 border-background ring-1 ring-border/80">
                    <Icon
                      width={36}
                      height={36}
                      className="rounded-full"
                    />
                  </div>
                </div>
              ))}
            </div>
            <div className="relative aspect-128/55 w-32">
              {/* Connector */}
              <svg
                viewBox="0 0 128 55"
                fill="none"
                aria-hidden="true"
                className="absolute inset-0 size-full stroke-neutral-200 opacity-80 dark:stroke-neutral-800"
              >
                <path d="M64 0v25M8 0v8c0 8.837 7.163 16 16 16h24c8.837 0 16 7.163 16 16v15M120 0v8c0 8.837-7.163 16-16 16H80c-5.922 0-11.093 3.218-13.86 8" />
              </svg>
            </div>
            {/* Text */}
            <div className="mt-px flex flex-row items-center gap-2 whitespace-nowrap rounded-lg bg-secondary px-3 py-1.5 text-sm text-foreground">
              Chat
              <ArrowLeftRightIcon className="size-3 shrink-0" />
              Email
            </div>
          </div>
        </div>
      </CardContent>
    </MotionCard>
  );
}
