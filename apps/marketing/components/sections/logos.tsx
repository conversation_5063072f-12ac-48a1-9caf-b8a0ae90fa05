import * as React from 'react';

import { BlurFade } from '~/components/fragments/blur-fade';
import { GridSection } from '~/components/fragments/grid-section';

function Vercel(props: React.SVGAttributes<SVGSVGElement>): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="95.484"
      height="21.606"
      fill="none"
      viewBox="0 0 95.484 21.606"
      {...props}
    >
      <path
        fill="currentColor"
        d="M47.85 5.402c-3.712 0-6.389 2.43-6.389 6.076s3.01 6.077 6.725 6.077c2.242 0 4.22-.891 5.443-2.393l-2.572-1.493c-.68.747-1.711 1.182-2.871 1.182-1.61 0-2.979-.844-3.487-2.194h9.42c.075-.379.119-.77.119-1.182 0-3.643-2.673-6.073-6.388-6.073m-3.178 4.895c.42-1.347 1.57-2.195 3.178-2.195 1.61 0 2.76.848 3.177 2.195zm-.76-8.609-9.316 16.205-9.32-16.205h3.494l5.823 10.128 5.823-10.128ZM12.424 0l12.423 21.606H0Zm55.33 11.478c0 2.026 1.318 3.376 3.362 3.376 1.386 0 2.425-.63 2.959-1.66l2.582 1.495c-1.07 1.79-3.073 2.866-5.54 2.866-3.716 0-6.389-2.43-6.389-6.077s2.677-6.076 6.388-6.076c2.468 0 4.469 1.077 5.541 2.866l-2.582 1.495c-.534-1.03-1.573-1.66-2.959-1.66-2.04 0-3.362 1.35-3.362 3.375m27.73-9.79v15.53h-3.025V1.688Zm-11.43 3.714c-3.712 0-6.388 2.43-6.388 6.076s3.012 6.077 6.724 6.077c2.242 0 4.22-.891 5.443-2.393l-2.572-1.493c-.679.747-1.711 1.182-2.871 1.182-1.61 0-2.979-.844-3.487-2.194h9.421c.074-.379.118-.77.118-1.182 0-3.643-2.673-6.073-6.388-6.073m-3.178 4.895c.42-1.347 1.567-2.195 3.178-2.195s2.76.848 3.177 2.195zM63.72 5.739v3.272a3.8 3.8 0 0 0-1.076-.166c-1.954 0-3.362 1.35-3.362 3.376v4.997h-3.026V5.739h3.026v3.106c0-1.715 1.987-3.106 4.438-3.106"
      />
    </svg>
  );
}

function Deel(props: React.SVGAttributes<SVGSVGElement>): React.JSX.Element {
  return (
    <svg
      width="58.371498"
      height="20.424255"
      viewBox="0 0 58.371498 20.424255"
      fill="none"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g
        clipPath="url(#clip0_2108_28027)"
        transform="translate(0,-0.74072244)"
      >
        <path
          d="m 47.8545,20.7669 c -0.0857,0 -0.1553,-0.0696 -0.1553,-0.1553 V 1.90739 c 0,-0.07364 0.0518,-0.13714 0.1239,-0.15205 l 3.2667,-0.67541 c 0.0964,-0.01992 0.1868,0.05366 0.1868,0.15205 V 20.6116 c 0,0.0857 -0.0696,0.1553 -0.1554,0.1553 z"
          fill="currentColor"
        />
        <path
          d="m 6.81919,21.163 c -1.30422,0 -2.4687,-0.3168 -3.49344,-0.9502 C 2.30101,19.5793 1.49053,18.7129 0.894321,17.6136 0.298106,16.5144 0,15.2661 0,13.8686 0,12.4713 0.298106,11.2323 0.894321,10.1517 1.49053,9.05238 2.30101,8.19532 3.32575,7.58047 4.35049,6.947 5.51497,6.63026 6.81919,6.63026 c 1.04337,0 1.95634,0.19563 2.73884,0.58689 0.62857,0.31427 1.16097,0.7307 1.59707,1.24929 0.0983,0.11683 0.3034,0.05039 0.3034,-0.10227 V 1.57145 c 0,-0.07363 0.0517,-0.13714 0.1238,-0.15205 l 3.2668,-0.675406 c 0.0963,-0.019921 0.1867,0.053659 0.1867,0.15205 V 20.6723 c 0,0.0858 -0.0696,0.1553 -0.1553,0.1553 h -2.9032 c -0.0741,0 -0.1379,-0.0525 -0.1523,-0.1253 L 11.5272,19.1921 C 11.501,19.0592 11.3267,19.0196 11.2399,19.1237 10.8231,19.6238 10.2996,20.0706 9.6698,20.4642 8.94321,20.93 7.99298,21.163 6.81919,21.163 Z m 0.75458,-3.1302 c 1.15512,0 2.09603,-0.3819 2.82273,-1.1458 0.7452,-0.7825 1.1178,-1.7793 1.1178,-2.9904 0,-1.2111 -0.3726,-2.1985 -1.1178,-2.9624 C 9.6698,10.1517 8.72889,9.76037 7.57377,9.76037 c -1.13653,0 -2.07744,0.38193 -2.8227,1.14583 -0.74527,0.7639 -1.1179,1.7514 -1.1179,2.9624 0,1.2111 0.37263,2.2079 1.1179,2.9904 0.74526,0.7825 1.68617,1.1738 2.8227,1.1738 z"
          fill="currentColor"
        />
        <path
          d="m 23.7567,21.165 c -1.3974,0 -2.6364,-0.2981 -3.717,-0.8943 -1.0807,-0.5962 -1.9284,-1.4347 -2.5433,-2.5153 -0.6148,-1.0806 -0.9222,-2.3289 -0.9222,-3.7449 0,-1.4347 0.2981,-2.711 0.8943,-3.8288 0.6148,-1.11795 1.4532,-1.98432 2.5153,-2.59917 1.0806,-0.63347 2.3476,-0.95021 3.8008,-0.95021 1.3601,0 2.5619,0.29811 3.6053,0.89433 1.0433,0.59621 1.8538,1.41599 2.4314,2.45937 0.5962,1.02468 0.8943,2.17058 0.8943,3.43748 0,0.205 -0.0093,0.4193 -0.0279,0.6428 0,0.1791 -0.006,0.3642 -0.0179,0.5553 -0.0052,0.0809 -0.0728,0.1434 -0.1538,0.1434 H 20.2898 c -0.0899,0 -0.1613,0.0764 -0.1523,0.1659 0.0999,1.0019 0.4678,1.7944 1.1039,2.3774 0.6894,0.6148 1.5185,0.9222 2.4873,0.9222 0.7267,0 1.3322,-0.1584 1.8166,-0.4751 0.4741,-0.316 0.8324,-0.7148 1.0748,-1.1964 0.0271,-0.0539 0.0818,-0.0891 0.1422,-0.0891 h 3.3258 c 0.1039,0 0.1785,0.1 0.1457,0.1986 -0.2659,0.7985 -0.6829,1.5335 -1.251,2.2048 -0.5961,0.708 -1.3415,1.267 -2.2357,1.6769 -0.8757,0.4098 -1.8725,0.6148 -2.9904,0.6148 z M 23.7846,9.53888 c -0.8756,0 -1.6489,0.25149 -2.3196,0.75452 -0.6295,0.4547 -1.0456,1.1389 -1.2484,2.0529 -0.0211,0.0948 0.0523,0.183 0.1494,0.183 h 6.5514 c 0.0894,0 0.1607,-0.0756 0.1517,-0.1646 C 26.9847,11.5286 26.6537,10.8568 26.0763,10.3494 25.4615,9.80904 24.6976,9.53888 23.7846,9.53888 Z"
          fill="currentColor"
        />
        <path
          d="m 39.3192,21.165 c -1.3974,0 -2.6364,-0.2981 -3.717,-0.8943 -1.0807,-0.5962 -1.9284,-1.4347 -2.5432,-2.5153 -0.6149,-1.0806 -0.9223,-2.3289 -0.9223,-3.7449 0,-1.4347 0.2981,-2.711 0.8943,-3.8288 0.6148,-1.11795 1.4532,-1.98432 2.5153,-2.59917 1.0806,-0.63347 2.3476,-0.95021 3.8008,-0.95021 1.3601,0 2.5619,0.29811 3.6053,0.89433 1.0433,0.59621 1.8538,1.41599 2.4314,2.45937 0.5962,1.02468 0.8943,2.17058 0.8943,3.43748 0,0.205 -0.0093,0.4193 -0.0279,0.6428 0,0.1791 -0.006,0.3642 -0.0179,0.5553 -0.0052,0.0809 -0.0728,0.1434 -0.1538,0.1434 H 35.8523 c -0.0899,0 -0.1613,0.0764 -0.1523,0.1659 0.0999,1.0019 0.4678,1.7944 1.1039,2.3774 0.6894,0.6148 1.5185,0.9222 2.4873,0.9222 0.7267,0 1.3322,-0.1584 1.8166,-0.4751 0.4741,-0.316 0.8324,-0.7148 1.0748,-1.1964 0.0271,-0.0539 0.0818,-0.0891 0.1422,-0.0891 h 3.3258 c 0.1039,0 0.1785,0.1 0.1457,0.1986 -0.2659,0.7985 -0.6829,1.5335 -1.251,2.2048 -0.5961,0.708 -1.3415,1.267 -2.2357,1.6769 -0.8757,0.4098 -1.8725,0.6148 -2.9904,0.6148 z M 39.3471,9.53888 c -0.8756,0 -1.6489,0.25149 -2.3196,0.75452 -0.6294,0.4547 -1.0456,1.1389 -1.2484,2.0529 -0.0211,0.0948 0.0523,0.183 0.1494,0.183 h 6.5514 c 0.0894,0 0.1607,-0.0756 0.1517,-0.1646 C 42.5472,11.5286 42.2162,10.8568 41.6388,10.3494 41.024,9.80904 40.2601,9.53888 39.3471,9.53888 Z"
          fill="currentColor"
        />
        <path
          d="m 55.9631,20.7714 c 1.3301,0 2.4084,-1.0783 2.4084,-2.4084 0,-1.3301 -1.0783,-2.4084 -2.4084,-2.4084 -1.3301,0 -2.4084,1.0783 -2.4084,2.4084 0,1.3301 1.0783,2.4084 2.4084,2.4084 z"
          fill="currentColor"
        />
      </g>
    </svg>
  );
}

function Resend(props: React.SVGAttributes<SVGSVGElement>): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="65"
      height="16"
      fill="none"
      viewBox="0 0 65 16"
      {...props}
    >
      <path
        fill="currentColor"
        d="M.82 15V1h6.2q1.3 0 2.36.6 1.06.58 1.66 1.58.62 1 .62 2.28 0 1.26-.62 2.3-.6 1.02-1.66 1.62t-2.36.6h-3.3V15zm7.94 0L5.2 8.68l3.08-.5 3.96 6.84zM3.72 7.54h3.16q.54 0 .94-.24.42-.26.64-.7.22-.46.22-1.02 0-.6-.26-1.04a1.7 1.7 0 0 0-.76-.68A2.5 2.5 0 0 0 6.5 3.6H3.72zM18.053 15.2q-1.72 0-3.02-.7a5.13 5.13 0 0 1-2-1.92q-.7-1.22-.7-2.8 0-1.24.4-2.26a5.2 5.2 0 0 1 1.12-1.76 4.8 4.8 0 0 1 1.7-1.16 5.5 5.5 0 0 1 2.16-.42q1.08 0 2 .4a4.6 4.6 0 0 1 1.58 1.12q.68.7 1.04 1.68.36.96.32 2.08l-.02.88h-8.5l-.46-1.74h6.62l-.32.36v-.44a1.77 1.77 0 0 0-.36-.96q-.3-.44-.78-.68-.48-.26-1.08-.26-.879 0-1.5.34-.6.34-.92 1t-.32 1.6q0 .96.4 1.66.42.7 1.16 1.1.76.38 1.78.38.7 0 1.28-.22t1.24-.76l1.36 1.9q-.58.52-1.28.88-.699.34-1.44.52-.74.18-1.46.18M27.312 15.2q-1.48 0-2.64-.48-1.14-.5-1.84-1.36l1.78-1.52q.6.66 1.36.96a4.4 4.4 0 0 0 1.52.28q.3 0 .54-.06.26-.08.44-.2.18-.14.26-.32a.9.9 0 0 0 .1-.42.77.77 0 0 0-.36-.68 3.3 3.3 0 0 0-.62-.26q-.42-.16-1.08-.34a9.7 9.7 0 0 1-1.74-.6q-.7-.36-1.12-.8-.36-.42-.56-.9-.18-.5-.18-1.1 0-.72.32-1.3.32-.6.88-1.02.58-.42 1.32-.64a5 5 0 0 1 1.56-.24q.82 0 1.6.2t1.44.58q.68.36 1.18.86l-1.52 1.68a5 5 0 0 0-.82-.62 4.2 4.2 0 0 0-.92-.44 2.8 2.8 0 0 0-.86-.16q-.34 0-.62.06-.26.06-.44.2a.86.86 0 0 0-.28.3.98.98 0 0 0 .02.82q.12.2.32.34.22.12.64.28.44.16 1.18.36.96.26 1.62.58.68.32 1.08.74.34.36.5.82t.16 1.02q0 .98-.56 1.74-.54.76-1.5 1.2t-2.16.44M37.577 15.2q-1.72 0-3.02-.7a5.13 5.13 0 0 1-2-1.92q-.7-1.22-.7-2.8 0-1.24.4-2.26a5.2 5.2 0 0 1 1.12-1.76 4.8 4.8 0 0 1 1.7-1.16 5.5 5.5 0 0 1 2.16-.42q1.08 0 2 .4a4.6 4.6 0 0 1 1.58 1.12q.68.7 1.04 1.68.36.96.32 2.08l-.02.88h-8.5l-.46-1.74h6.62l-.32.36v-.44a1.77 1.77 0 0 0-.36-.96q-.3-.44-.78-.68a2.2 2.2 0 0 0-1.08-.26q-.88 0-1.5.34-.6.34-.92 1t-.32 1.6q0 .96.4 1.66.42.7 1.16 1.1.76.38 1.78.38.7 0 1.28-.22t1.24-.76l1.36 1.9a6 6 0 0 1-1.28.88q-.7.34-1.44.52t-1.46.18M43.276 15V4.42h2.72l.08 2.16-.56.24q.219-.72.78-1.3.579-.6 1.38-.96a4.05 4.05 0 0 1 1.68-.36q1.2 0 2 .48.82.48 1.22 1.46.42.96.42 2.38V15h-2.84V8.74q0-.72-.2-1.2a1.34 1.34 0 0 0-.62-.7q-.4-.22-1-.2-.48 0-.9.16-.4.14-.7.42-.28.28-.46.64a1.9 1.9 0 0 0-.16.78V15h-2.84M58.857 15.2q-1.4 0-2.5-.7a5.17 5.17 0 0 1-1.72-1.94q-.62-1.24-.62-2.86 0-1.58.62-2.82.64-1.26 1.72-1.96 1.1-.72 2.5-.72.74 0 1.42.24.7.22 1.24.62.56.4.9.9.34.48.38 1l-.72.14V.2h2.86V15h-2.7l-.12-2.44.56.06a1.94 1.94 0 0 1-.36.96q-.32.46-.86.84-.52.36-1.2.58-.66.2-1.4.2m.64-2.36q.8 0 1.4-.4t.94-1.1.34-1.64q0-.92-.34-1.62a2.7 2.7 0 0 0-.94-1.1q-.6-.4-1.4-.4t-1.4.4a2.8 2.8 0 0 0-.92 1.1q-.32.7-.32 1.62 0 .94.32 1.64.34.7.92 1.1.6.4 1.4.4"
      />
    </svg>
  );
}

function Notion(props: React.SVGAttributes<SVGSVGElement>): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="128.546"
      height="34.45"
      fill="none"
      viewBox="0 0 128.546 34.45"
      {...props}
    >
      <path
        fill="currentColor"
        d="M44.936 28.776V14.22h.259L55.79 28.776h3.334V7.4h-3.705v14.518h-.26L44.566 7.402h-3.334v21.374zm24.671.33c4.89 0 7.89-3.153 7.89-8.432 0-5.243-3-8.433-7.89-8.433-4.853 0-7.89 3.19-7.89 8.433.037 5.279 3 8.432 7.89 8.432m0-3.08c-2.593 0-4.075-1.943-4.075-5.352 0-3.373 1.482-5.353 4.075-5.353s4.075 1.98 4.075 5.352c0 3.41-1.482 5.353-4.075 5.353M80.87 8.612v4.07h-2.594v2.932h2.594v8.836c0 3.153 1.481 4.4 5.26 4.4.704 0 1.408-.074 1.963-.184v-2.86c-.444.037-.74.074-1.26.074-1.555 0-2.259-.697-2.259-2.31v-7.956h3.52V12.68h-3.52V8.61H80.87Zm9.446 20.164h3.704V12.57h-3.704Zm1.852-18.881c1.223 0 2.223-.99 2.223-2.2 0-1.247-1-2.237-2.223-2.237s-2.223.99-2.223 2.237c0 1.21 1 2.2 2.223 2.2m12.002 19.21c4.89 0 7.891-3.152 7.891-8.431 0-5.243-3.001-8.433-7.891-8.433-4.852 0-7.89 3.19-7.89 8.433 0 5.279 2.964 8.432 7.89 8.432zm0-3.08c-2.593 0-4.074-1.942-4.074-5.351 0-3.373 1.481-5.353 4.074-5.353 2.557 0 4.075 1.98 4.075 5.352-.037 3.41-1.518 5.353-4.075 5.353m10.076 2.75h3.705v-9.421c0-2.383 1.408-3.887 3.593-3.887 2.26 0 3.297 1.247 3.297 3.703v9.606h3.705V18.29c0-3.886-2.001-6.05-5.631-6.05-2.445 0-4.075 1.1-4.853 2.934h-.259V12.57h-3.594c.037 0 .037 16.205.037 16.205"
      />
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M5.712 6.034c1.078.87 1.468.8 3.487.663l19.017-1.144c.413 0 .069-.412-.069-.458l-3.165-2.266c-.597-.458-1.423-1.007-2.96-.87L3.625 3.309c-.666.07-.803.413-.528.665zM6.86 10.45v19.96c0 1.076.528 1.465 1.743 1.396l20.899-1.213c1.216-.07 1.353-.801 1.353-1.671V9.1c0-.87-.344-1.35-1.078-1.282L7.937 9.1c-.803.068-1.078.48-1.078 1.35zm20.623 1.076c.138.595 0 1.213-.596 1.282l-1.01.206v14.74c-.871.459-1.674.733-2.363.733-1.078 0-1.353-.343-2.156-1.35l-6.584-10.323v9.98l2.088.457s0 1.213-1.675 1.213l-4.634.275c-.137-.275 0-.938.46-1.076l1.215-.343V14.136l-1.675-.137c-.137-.595.207-1.465 1.147-1.534l4.978-.343 6.86 10.438v-9.225l-1.744-.206c-.138-.732.413-1.282 1.078-1.35zM2.088 1.479 21.242.08c2.34-.206 2.96-.068 4.428 1.007l6.102 4.28c1.01.733 1.353.94 1.353 1.74v23.508c0 1.465-.527 2.335-2.408 2.472l-22.23 1.35c-1.421.07-2.087-.137-2.82-1.075l-4.52-5.837C.344 26.45 0 25.65 0 24.71V3.813C0 2.6.55 1.616 2.088 1.479"
        clipRule="evenodd"
      />
    </svg>
  );
}

const DATA = [
  { icon: Vercel },
  { icon: Deel },
  { icon: Resend },
  { icon: Notion }
];

export function Logos(): React.JSX.Element {
  return (
    <GridSection className="bg-diagonal-lines">
      <div className="flex flex-col items-center justify-between gap-2 bg-background p-8 sm:flex-row sm:py-4">
        <BlurFade className="mb-6 sm:mb-0">
          <p className="max-w-[220px] text-center text-sm text-muted-foreground sm:text-left">
            Trusted by fast-growing companies around the world
          </p>
        </BlurFade>
        <div className="grid grid-cols-2 gap-6 md:grid-cols-4 lg:max-w-4xl lg:gap-10">
          {DATA.map(({ icon: Icon }, index) => (
            <BlurFade
              key={index}
              delay={0.2 + index * 0.2}
              className="flex items-center justify-center text-neutral-700 dark:text-neutral-300"
            >
              <Icon className="h-6 w-auto" />
            </BlurFade>
          ))}
        </div>
      </div>
    </GridSection>
  );
}
