{"name": "marketing", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "next dev --port 3001 --turbopack", "build": "content-collections build && next build --turbopack", "build:content": "content-collections build", "start": "next start --port 3001", "analyze": "BUNDLE_ANALYZE=both next build --turbopack", "clean": "git clean -xdf .cache .content-collections .next .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "content-collections build && tsc --noEmit", "postinstall": "content-collections build"}, "dependencies": {"@hookform/resolvers": "5.1.0", "@t3-oss/env-nextjs": "0.13.6", "@tanstack/react-table": "8.21.3", "@workspace/analytics": "workspace:*", "@workspace/billing": "workspace:*", "@workspace/common": "workspace:*", "@workspace/routes": "workspace:*", "@workspace/ui": "workspace:*", "date-fns": "4.1.0", "exceljs": "4.4.0", "lucide-react": "0.513.0", "mdast-util-toc": "7.1.0", "motion": "12.18.1", "next": "15.3.3", "next-secure-headers": "2.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "7.57.0", "react-is": "19.0.0", "recharts": "2.15.3", "server-only": "0.0.1", "sharp": "0.34.2", "unist-util-visit": "5.0.0", "vaul": "1.1.2", "vfile": "6.0.3", "zod": "3.25.56"}, "devDependencies": {"@content-collections/cli": "0.1.6", "@content-collections/core": "0.9.0", "@content-collections/mdx": "0.2.2", "@content-collections/next": "0.2.6", "@next/bundle-analyzer": "15.3.3", "@types/mdast": "4.0.4", "@types/node": "22.15.30", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@types/unist": "3.0.3", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "mdx-bundler": "10.1.1", "postcss": "8.5.4", "rehype": "13.0.2", "rehype-autolink-headings": "7.1.0", "rehype-pretty-code": "0.14.1", "rehype-slug": "6.0.0", "remark": "15.0.1", "remark-code-import": "1.2.0", "remark-gfm": "4.0.1", "shiki": "3.6.0"}, "prettier": "@workspace/prettier-config"}