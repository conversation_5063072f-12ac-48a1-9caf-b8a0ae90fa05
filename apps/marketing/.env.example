# -------------------------- ANALYTICS --------------------------

# Select an analytics provider: <PERSON>sol<PERSON> (default), Google Analytics, PostHog or Umami.
# Set environment variables based on your choice.
# Configuration file: packages/analytics/provider/index.ts

# Provider: Google Analytics
NEXT_PUBLIC_ANALYTICS_GA_MEASUREMENT_ID=
NEXT_PUBLIC_ANALYTICS_GA_DISABLE_LOCALHOST_TRACKING=false
NEXT_PUBLIC_ANALYTICS_GA_DISABLE_PAGE_VIEWS_TRACKING=false

# Provider: PostHog
NEXT_PUBLIC_ANALYTICS_POSTHOG_KEY=phc_1234
NEXT_PUBLIC_ANALYTICS_POSTHOG_HOST=https://us.i.posthog.com

# Provider: Umami
NEXT_PUBLIC_ANALYTICS_UMAMI_HOST=https://cloud.umami.is/script.js
NEXT_PUBLIC_ANALYTICS_UMAMI_WEBSITE_ID=
NEXT_PUBLIC_ANALYTICS_UMAMI_DISABLE_LOCALHOST_TRACKING=false

# -------------------------- ROUTES --------------------------

NEXT_PUBLIC_DASHBOARD_URL=http://localhost:3000
NEXT_PUBLIC_MARKETING_URL=http://localhost:3001
NEXT_PUBLIC_PUBLIC_API_URL=http://localhost:3002
