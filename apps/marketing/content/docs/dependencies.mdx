---
title: Dependencies
description: Learn how to install dependencies for your project with various package managers.
---

Package managers simplify dependency management in software projects. They automate the process of installing, updating, and maintaining libraries and tools required for your project.

## Installation

To install dependencies using **PNPM**, first, ensure PNPM is installed globally by running:

```bash
npm install -g pnpm
```

Once installed, navigate to your project root and execute:

```bash
pnpm install
```

This will install all the dependencies defined in the `package.json` file efficiently, leveraging PNPM's unique caching system.
