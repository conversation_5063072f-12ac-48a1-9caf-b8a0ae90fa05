{"name": "@workspace/billing", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "5.1.0", "@stripe/stripe-js": "7.3.1", "@t3-oss/env-nextjs": "0.13.6", "@workspace/database": "workspace:*", "@workspace/routes": "workspace:*", "@workspace/ui": "workspace:*", "date-fns": "4.1.0", "lucide-react": "0.513.0", "next": "15.3.3", "react": "19.0.0", "react-dom": "19.0.0", "server-only": "0.0.1", "stripe": "18.2.1", "zod": "3.25.56"}, "devDependencies": {"@types/node": "22.15.30", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "prettier": "@workspace/prettier-config", "exports": {"./keys": "./keys.ts", "./components/price-interval-selector": "./src/components/price-interval-selector.tsx", "./components/pricing-card": "./src/components/pricing-card.tsx", "./components/pricing-table": "./src/components/pricing-table.tsx", "./hooks/use-purchases": "./src/hooks/use-purchases.tsx", "./provider": "./src/provider/index.ts", "./config": "./src/config.ts", "./helpers": "./src/helpers.ts", "./schema": "./src/schema.ts", "./seats": "./src/seats.ts", "./synchronize": "./src/synchronize.ts", "./webhook": "./src/webhook.ts"}}