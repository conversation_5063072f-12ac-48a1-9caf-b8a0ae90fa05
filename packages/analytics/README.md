# `@workspace/analytics`

This package contains the analytics logic which is often a requirement from a product manager or the marketing department. It contains following features:

- User Identification: Associate actions with specific users.
- Event Tracking: Record important user interactions.
- Automatic Page Views: Built-in tracking for Next.js route changes with no code required.

Following analytics provider are available:

# Console (default)

Tracks events and page views to the console.

# Google Analytics

Tracks events and page views to Google Analytics which is the industry standard.

# PostHog

Tracks events and page views to PostHog which is a cheap, developer-focused powerhouse.

# Umami

Tracks events and page views to Umami which is Aa simple, fast, privacy-focused alternative to GA.
