{"name": "@workspace/auth", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "2.9.1", "@otplib/core": "^12.0.1", "@otplib/plugin-crypto": "^12.0.1", "@otplib/plugin-thirty-two": "^12.0.1", "@t3-oss/env-nextjs": "0.13.6", "@workspace/common": "workspace:*", "@workspace/database": "workspace:*", "@workspace/email": "workspace:*", "@workspace/rate-limit": "workspace:*", "@workspace/routes": "workspace:*", "bcryptjs": "3.0.2", "date-fns": "4.1.0", "next": "15.3.3", "next-auth": "5.0.0-beta.28", "react": "19.0.0", "react-dom": "19.0.0", "uuid": "11.1.0", "zod": "3.25.56"}, "devDependencies": {"@types/node": "22.15.30", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@types/uuid": "10.0.0", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "prettier": "@workspace/prettier-config", "exports": {"./keys": "./keys.ts", "./adapter": "./src/adapter.ts", "./callbacks": "./src/callbacks.ts", "./constants": "./src/constants.ts", "./context": "./src/context.ts", "./cookies": "./src/cookies.ts", "./encryption": "./src/encryption.ts", "./errors": "./src/errors.ts", "./events": "./src/events.ts", ".": "./src/index.ts", "./invitations": "./src/invitations.ts", "./password": "./src/password.ts", "./permissions": "./src/permissions.ts", "./providers": "./src/providers.ts", "./providers.types": "./src/providers.types.ts", "./redirect": "./src/redirect.ts", "./schemas": "./src/schemas.ts", "./session": "./src/session.ts", "./verification": "./src/verification.ts"}}