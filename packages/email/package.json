{"name": "@workspace/email", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit", "preview": "email dev -d ./src/templates/previews --port 3004", "export": "email export -d ./src/templates/previews"}, "dependencies": {"@react-email/components": "0.0.41", "@react-email/render": "1.1.2", "@react-email/tailwind": "1.0.5", "@sendgrid/mail": "8.1.5", "@t3-oss/env-nextjs": "0.13.6", "@workspace/common": "workspace:*", "nodemailer": "7.0.3", "postmark": "4.0.5", "react": "19.0.0", "react-dom": "19.0.0", "react-email": "4.0.16", "resend": "4.5.2", "zod": "3.25.56"}, "devDependencies": {"@types/node": "22.15.30", "@types/nodemailer": "6.4.17", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "prettier": "@workspace/prettier-config", "exports": {"./keys": "./keys.ts", "./provider": "./src/provider", "./send-confirm-email-address-change-email": "./src/send-confirm-email-address-change-email.ts", "./send-connected-account-security-alert-email": "./src/send-connected-account-security-alert-email.ts", "./send-feedback-email": "./src/send-feedback-email.ts", "./send-invitation-email": "./src/send-invitation-email.ts", "./send-password-reset-email": "./src/send-password-reset-email.ts", "./send-revoked-invitation-email": "./src/send-revoked-invitation-email.ts", "./send-verify-email-address-email": "./src/send-verify-email-address-email.ts", "./send-welcome-email": "./src/send-welcome-email.ts", "./templates/confirm-email-address-change-email": "./src/templates/confirm-email-address-change-email.tsx", "./templates/connected-account-security-alert-email": "./src/templates/connected-account-security-alert-email.tsx", "./templates/feedback-email": "./src/templates/feedback-email.tsx", "./templates/invitation-email": "./src/templates/invitation-email.tsx", "./templates/password-reset-email": "./src/templates/password-reset-email.tsx", "./templates/revoked-invitation-email": "./src/templates/revoked-invitation-email.tsx", "./templates/verify-email-address-email": "./src/templates/verify-email-address-email.tsx", "./templates/welcome-email": "./src/templates/welcome-email.tsx"}}