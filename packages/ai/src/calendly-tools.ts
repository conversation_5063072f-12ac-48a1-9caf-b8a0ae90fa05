/**
 * Calendly tools integration for AI system
 * Provides tools for checking bookings and rescheduling
 */

export interface CalendlyIntegration {
  enabled: boolean;
  apiToken: string;
  eventTypeUri: string | null;
}

/**
 * Get Calendly tools prompt when integration is enabled
 */
export function getCalendlyToolsPrompt(): string {
  return `
You have access to Calendly scheduling tools. Use them when:
- You need to verify someone actually booked after sending them the calendar link
- A user wants to reschedule their booking

Available tools:
- check_calendly_booking: Verify if someone has a booking
- get_available_slots: Get available time slots for rescheduling
- reschedule_booking: Change an existing booking to a new time`;
}

/**
 * Get Calendly tools definitions for function calling
 */
export function getCalendlyToolDefinitions() {
  return [
    {
      name: 'check_calendly_booking',
      description: 'Check if a person has scheduled a meeting by their email address',
      parameters: {
        type: 'object',
        properties: {
          email: {
            type: 'string',
            description: 'The email address to check for bookings'
          }
        },
        required: ['email']
      }
    },
    {
      name: 'get_available_slots',
      description: 'Get available time slots for scheduling or rescheduling',
      parameters: {
        type: 'object',
        properties: {
          startDate: {
            type: 'string',
            description: 'Start date for availability search (optional, YYYY-MM-DD format)'
          },
          endDate: {
            type: 'string',
            description: 'End date for availability search (optional, YYYY-MM-DD format)'
          }
        },
        required: []
      }
    },
    {
      name: 'reschedule_booking',
      description: 'Help a user reschedule their existing booking',
      parameters: {
        type: 'object',
        properties: {
          email: {
            type: 'string',
            description: 'The email address of the person who wants to reschedule'
          }
        },
        required: ['email']
      }
    }
  ];
}