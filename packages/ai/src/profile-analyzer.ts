import type { OpenRouterConfig, ZA<PERSON>onfig, AnthropicConfig } from './types';
import { OpenRouterClient } from './openrouter-client';
import { ZAIClient } from './zai-client';
import { AnthropicClient } from './anthropic-client';

interface ProfileData {
  username: string;
  bio: string | null;
  profilePhoto?: string | null;
  mediaData: Array<{
    id: string;
    media_type: string;
    media_url: string;
    caption?: string;
    thumbnail_url?: string;
    alt_text?: string;
  }>;
}

export class ProfileAnalyzer {
  private readonly config: OpenRouterConfig & { zaiApiKey?: string; anthropicApiKey?: string };

  constructor(config: OpenRouterConfig & { zaiApiKey?: string; anthropicApiKey?: string }) {
    this.config = config;
  }

  /**
   * Analyze Instagram profile and generate insights
   */
  async analyzeProfile(
    profileData: ProfileData,
    customPrompt: string
  ): Promise<string> {
    console.log(`🔍 Analyzing profile for @${profileData.username}`);

    try {
      // Get profile photo analysis if available
      let profilePhotoAnalysis = '';
      if (profileData.profilePhoto) {
        try {
          profilePhotoAnalysis = await this.analyzeProfilePhoto(profileData.profilePhoto);
        } catch (photoError) {
          console.warn('⚠️ Profile photo analysis failed, continuing without it:', photoError);
        }
      }

      // Build analysis prompt with photo analysis
      const analysisPrompt = this.buildAnalysisPrompt(profileData, customPrompt, profilePhotoAnalysis);

      // Use appropriate AI provider
      const response = await this.makeApiRequest(analysisPrompt);

      console.log(`✅ Profile analysis completed for @${profileData.username}`);
      return response;

    } catch (error) {
      console.error('❌ Failed to analyze profile:', error);
      throw error;
    }
  }

  /**
   * Make direct API call to the appropriate provider
   */
  private async makeApiRequest(prompt: string): Promise<string> {
    const model = this.config.model;

    // Check if it's a Claude model
    if (model.includes('claude') && this.config.anthropicApiKey) {
      return this.makeAnthropicRequest(prompt);
    } else {
      // Fallback to OpenRouter for other models
      return this.makeOpenRouterRequest(prompt);
    }
  }

  /**
   * Make direct Anthropic API call
   */
  private async makeAnthropicRequest(prompt: string): Promise<string> {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.anthropicApiKey}`,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: this.config.model,
        max_tokens: 4000,
        messages: [
          { role: 'user', content: prompt }
        ],
        temperature: this.config.temperature || 1.0
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Anthropic API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();

    if (!data.content?.[0]?.text) {
      throw new Error('Invalid response from Anthropic API');
    }

    return data.content[0].text;
  }

  /**
   * Make direct OpenRouter API call (fallback)
   */
  private async makeOpenRouterRequest(prompt: string): Promise<string> {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
        'X-Title': 'Profile Analysis'
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: [
          { role: 'user', content: prompt }
        ],
        temperature: this.config.temperature || 1.0,
        top_p: this.config.topP || 1.0
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.choices?.[0]?.message?.content) {
      throw new Error('Invalid response from OpenRouter API');
    }

    return data.choices[0].message.content;
  }

  /**
   * Analyze profile photo using vision API
   */
  private async analyzeProfilePhoto(profilePhotoUrl: string): Promise<string> {
    try {
      // Download and convert profile photo to base64
      const response = await fetch(profilePhotoUrl);
      if (!response.ok) {
        throw new Error(`Failed to download profile photo: ${response.status}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const base64 = Buffer.from(arrayBuffer).toString('base64');
      const mimeType = response.headers.get('content-type') || 'image/jpeg';
      const dataUrl = `data:${mimeType};base64,${base64}`;

      // Use Claude for vision analysis if available, otherwise OpenRouter
      const model = this.config.model;

      if (model.includes('claude') && this.config.anthropicApiKey) {
        return await this.analyzePhotoWithAnthropic(dataUrl);
      } else {
        return await this.analyzePhotoWithOpenRouter(dataUrl);
      }
    } catch (error) {
      console.error('❌ Profile photo analysis error:', error);
      throw error;
    }
  }

  /**
   * Analyze photo using Anthropic Claude Vision API
   */
  private async analyzePhotoWithAnthropic(dataUrl: string): Promise<string> {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.anthropicApiKey}`,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: this.config.model,
        max_tokens: 300,
        messages: [{
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'Analyze this Instagram profile photo. Describe the person\'s appearance, estimated age range, gender, style, and any other relevant characteristics for understanding this person\'s profile. Be concise but detailed.'
            },
            {
              type: 'image',
              source: {
                type: 'base64',
                media_type: dataUrl.split(';')[0].split(':')[1],
                data: dataUrl.split(',')[1]
              }
            }
          ]
        }],
        temperature: 0.3
      })
    });

    if (!response.ok) {
      throw new Error(`Anthropic Vision API error: ${response.status}`);
    }

    const data = await response.json();
    return data.content[0]?.text || 'No analysis available';
  }

  /**
   * Analyze photo using OpenRouter (fallback)
   */
  private async analyzePhotoWithOpenRouter(dataUrl: string): Promise<string> {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
        'X-Title': 'Profile Photo Analysis'
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: [{
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'Analyze this Instagram profile photo. Describe the person\'s appearance, estimated age range, gender, style, and any other relevant characteristics for understanding this person\'s profile. Be concise but detailed.'
            },
            {
              type: 'image_url',
              image_url: { url: dataUrl }
            }
          ]
        }],
        temperature: 0.3,
        max_tokens: 300
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter Vision API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || 'No analysis available';
  }

  /**
   * Build analysis prompt with profile data
   */
  private buildAnalysisPrompt(profileData: ProfileData, customPrompt: string, profilePhotoAnalysis?: string): string {
    const mediaSummary = profileData.mediaData.map((media, index) => {
      const altText = media.alt_text ? ` (Alt: ${media.alt_text})` : '';
      return `Post ${index + 1}: ${media.media_type} - ${media.caption || 'No caption'}${altText}`;
    }).join('\n');

    let prompt = `${customPrompt}

PROFILE DATA:
Username: @${profileData.username}
Bio: ${profileData.bio || 'No bio available'}`;

    if (profilePhotoAnalysis) {
      prompt += `

PROFILE PHOTO ANALYSIS:
${profilePhotoAnalysis}`;
    }

    prompt += `

RECENT POSTS (Last ${profileData.mediaData.length}):
${mediaSummary}`;

    return prompt;
  }

}