import { z } from 'zod';

// Instagram Stage enum - needs to match database
export const InstagramStageSchema = z.enum([
  'FIRSTCONTACT',
  'RAPPORT',
  'DISCOVERY',
  'DISSONANCE',
  'VISION',
  'PROPOSAL',
  'QUALIFIED',
  'FORMSENT',
  'CONVERTED',
  'DISQUALIFIED',
  'SUSPICIOUS'
]);

export type InstagramStage = z.infer<typeof InstagramStageSchema>;

// Engagement Priority enum (excluding NEW_FOLLOWER as per requirements)
export const EngagementPrioritySchema = z.enum([
  'VERY_HIGH',
  'HIGH',
  'MEDIUM', 
  'LOW',
  'NON_RESPONDED'
]);

export type EngagementPriority = z.infer<typeof EngagementPrioritySchema>;

// Main Instagram Response Schema for OpenRouter structured outputs
export const InstagramResponseSchema = z.object({
  instagramStage: InstagramStageSchema,
  engagementPriority: EngagementPrioritySchema,
  message1: z.string().nullable().optional(), // Optional - null if disqualified
  message2: z.string().nullable().optional(),
  message3: z.string().nullable().optional(),
  message4: z.string().nullable().optional(),
  notes: z.string(),
  followup1_text: z.string().nullable().optional(),
  followup1_scheduledAt: z.string().nullable().optional(), // ISO date string
  followup2_text: z.string().nullable().optional(),
  followup2_scheduledAt: z.string().nullable().optional(),
  followup3_text: z.string().nullable().optional(),
  followup3_scheduledAt: z.string().nullable().optional(),
  followup4_text: z.string().nullable().optional(),
  followup4_scheduledAt: z.string().nullable().optional(),
  followup5_text: z.string().nullable().optional(),
  followup5_scheduledAt: z.string().nullable().optional(),
  followup6_text: z.string().nullable().optional(),
  followup6_scheduledAt: z.string().nullable().optional(),
  followup7_text: z.string().nullable().optional(),
  followup7_scheduledAt: z.string().nullable().optional(),
  followup8_text: z.string().nullable().optional(),
  followup8_scheduledAt: z.string().nullable().optional(),
  isDisqualified: z.boolean(), // Determines if AI responds
  aiReasoning: z.string().nullable().optional() // AI reasoning content for debugging
});

export type InstagramResponse = z.infer<typeof InstagramResponseSchema>;

// OpenRouter API Configuration
export interface OpenRouterConfig {
  apiKey: string;
  model: string;
  enableReasoning?: boolean;
  reasoningMaxTokens?: number;
  temperature?: number;
  topP?: number;
}

// Z.AI API Configuration
export interface ZAIConfig {
  apiKey: string;
  model: string;
  enableReasoning?: boolean;
  reasoningMaxTokens?: number;
}

// Anthropic API Configuration
export interface AnthropicConfig {
  apiKey: string;
  model: string;
  enableThinking?: boolean;
  cacheDuration?: '5m' | '1h'; // Prompt caching duration
  temperature?: number;
  topP?: number;
}

// Prompt Building Configuration  
export interface PromptConfig {
  botStylePrompt: string; // Required - BotStyle template with variables
  followupsEnabled: boolean; // Simple boolean - AI determines everything when enabled
  maxConversationMessages: number;
}

// Message for conversation context
export interface ConversationMessage {
  id: string;
  content: string;
  isFromUser: boolean;
  createdAt: Date;
}

// Instagram Contact context
export interface InstagramContactContext {
  id: string;
  username: string;
  fullName?: string;
  engagementPriority: string;
  lastContactMessageAt?: Date;
  profileInsights?: string; // Raw AI analysis text
}

// Organization settings for AI
export interface OrganizationAISettings {
  responseTimeMin: number; // seconds
  responseTimeMax: number; // seconds
  aiModel: string;
  enableReasoning: boolean;
  reasoningMaxTokens?: number;
}

// Request to generate response
export interface GenerateResponseRequest {
  messages: ConversationMessage[];
  contact: InstagramContactContext;
  promptConfig: PromptConfig;
  aiConfig: OpenRouterConfig;
}

// Universal Message Content Type
export interface MessageContent {
  type: 'text';
  text: string;
}

// OpenRouter API Response types
export interface OpenRouterMessage {
  role: 'system' | 'user' | 'assistant';
  content: string | MessageContent[] | Array<{ type: string; text: string; cache_control?: { type: 'ephemeral'; ttl?: '5m' | '1h' } }>;
}

export interface OpenRouterRequest {
  model: string;
  messages: OpenRouterMessage[];
  reasoning?: {
    enabled?: boolean;        // Enable/disable reasoning
    effort?: "high" | "medium" | "low";  // OpenAI-style effort level
    max_tokens?: number;      // Token limit for reasoning (Anthropic-style)
    exclude?: boolean;        // Don't return reasoning in response
  };
  usage?: {
    include: boolean;         // Include detailed usage information
  };
  temperature?: number;
  top_p?: number;
  stream?: boolean;
}

export interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string;
      reasoning?: string;
    };
    finish_reason?: string;
  }>;
  model?: string;
  usage?: {
    total_tokens: number;
    prompt_tokens: number;
    completion_tokens: number;
    completion_tokens_details?: {
      reasoning_tokens: number;
    };
    prompt_tokens_details?: {
      cached_tokens: number;
      audio_tokens: number;
    };
    cost: number;
    cost_details?: {
      upstream_inference_cost: number;
    };
  };
}

// Z.AI API Message types
export interface ZAIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string | MessageContent[];
}

export interface ZAIRequest {
  model: string;
  messages: ZAIMessage[];
  thinking?: {
    type: 'enabled' | 'disabled';
  };
  stream?: boolean;
}

export interface ZAIResponse {
  choices: Array<{
    message: {
      content: string;
      reasoning_content?: string;
    };
    finish_reason?: string;
  }>;
  model?: string;
  usage?: {
    total_tokens: number;
    prompt_tokens: number;
    completion_tokens: number;
    cost?: number;
  };
}

// Anthropic API Message types
export interface AnthropicMessage {
  role: 'system' | 'user' | 'assistant';
  content: string | Array<{ 
    type: string; 
    text?: string; 
    cache_control?: { type: 'ephemeral'; ttl?: '5m' | '1h' };
    // Tool use content
    id?: string;
    name?: string;
    input?: any;
    // Tool result content
    tool_use_id?: string;
  }>;
}

export interface AnthropicRequest {
  model: string;
  messages: AnthropicMessage[];
  thinking?: {
    enabled: boolean;
  };
  max_tokens: number;
  temperature?: number;
  top_p?: number;
  tools?: Array<{
    name: string;
    description: string;
    input_schema: {
      type: string;
      properties: Record<string, any>;
      required: string[];
    };
  }>;
}

export interface AnthropicResponse {
  id?: string;
  model?: string;
  stop_reason?: string;
  usage?: {
    input_tokens: number;
    output_tokens: number;
    cache_creation_input_tokens?: number;
    cache_read_input_tokens?: number;
  };
  content: Array<{
    type: 'text' | 'tool_use';
    text?: string;
    // Tool use fields
    id?: string;
    name?: string;
    input?: any;
  }>;
}

// GLM-4.5 Model types
export const GLM_MODELS = [
  'glm-4.5',
  'glm-4.5-air',
  'glm-4.5-x',
  'glm-4.5-airx',
  'glm-4.5-flash',
  'glm-4.5v'
] as const;

export type GLMModel = typeof GLM_MODELS[number];

// Claude Model types
export const CLAUDE_MODELS = [
  'claude-sonnet-4-20250514',
  'claude-3-5-sonnet-20241022',
  'claude-3-5-haiku-20241022', 
  'claude-3-opus-20240229',
  'claude-3-sonnet-20240229',
  'claude-3-haiku-20240307'
] as const;

export type ClaudeModel = typeof CLAUDE_MODELS[number];


// Bot Style Variables Types
export interface BotStyleVariableValue {
  [variableName: string]: any; // Can be string, array, object, etc.
}

// Request interface for generating AI responses
export interface GenerateResponseRequest {
  messages: ConversationMessage[];
  contact: InstagramContactContext;
  promptConfig: PromptConfig;
  aiConfig: OpenRouterConfig;
  botStyleVariables?: BotStyleVariableValue; // Dynamic variables for template processing
  calendlyIntegration?: {
    enabled: boolean;
    apiToken: string;
    eventTypeUri: string | null;
  }; // Calendly integration for AI tools
}