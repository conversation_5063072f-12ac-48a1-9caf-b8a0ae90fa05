export interface OpenRouterModel {
  id: string;
  name: string;
  created: number;
  description: string;
  architecture: {
    modality: string;
    tokenizer: string;
    instruct_type?: string;
  };
  pricing: {
    prompt: string;
    completion: string;
    image?: string;
    request?: string;
  };
  context_length: number;
  top_provider: {
    context_length: number;
    max_completion_tokens: number;
    is_moderated: boolean;
  };
  per_request_limits?: {
    prompt_tokens: string;
    completion_tokens: string;
  };
}

export interface OpenRouterModelsResponse {
  data: OpenRouterModel[];
}

export class OpenRouterModelsService {
  private readonly baseUrl = 'https://openrouter.ai/api/v1';
  
  constructor(private apiKey?: string) {}

  /**
   * Fetch all available models from OpenRouter
   */
  async getModels(category?: string): Promise<OpenRouterModel[]> {
    try {
      const url = new URL(`${this.baseUrl}/models`);
      
      if (category) {
        url.searchParams.set('category', category);
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: Object.fromEntries(
          Object.entries({
            'Authorization': this.apiKey ? `Bearer ${this.apiKey}` : undefined,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'https://localhost:3000',
            'X-Title': 'AIChromaPRO Instagram AI'
          }).filter(([_, value]) => value !== undefined)
        ) as Record<string, string>
      });

      if (!response.ok) {
        console.error('❌ Failed to fetch OpenRouter models:', {
          status: response.status,
          statusText: response.statusText
        });
        throw new Error(`Failed to fetch models: ${response.status} ${response.statusText}`);
      }

      const data: OpenRouterModelsResponse = await response.json();
      console.log(`✅ Fetched ${data.data.length} models from OpenRouter`);
      
      return data.data;
      
    } catch (error) {
      console.error('❌ Error fetching OpenRouter models:', error);
      throw error;
    }
  }

  /**
   * Get curated list of recommended models for Instagram AI
   */
  async getRecommendedModels(): Promise<OpenRouterModel[]> {
    const allModels = await this.getModels();
    
    // List of recommended model IDs for Instagram AI use case
    const recommendedIds = [
      'openai/gpt-4o',
      'openai/gpt-4o-mini', 
      'anthropic/claude-3.5-sonnet',
      'anthropic/claude-3.5-haiku',
      'google/gemini-pro-1.5',
      'meta-llama/llama-3.1-70b-instruct',
      'meta-llama/llama-3.1-8b-instruct',
      'anthropic/claude-3-opus',
      'anthropic/claude-3-sonnet',
      'openai/gpt-4-turbo',
      'google/gemini-flash-1.5',
      'mistralai/mistral-large',
      'cohere/command-r-plus'
    ];

    // Filter and sort recommended models
    const recommendedModels = allModels
      .filter(model => recommendedIds.includes(model.id))
      .sort((a, b) => {
        // Sort by recommendation order
        const aIndex = recommendedIds.indexOf(a.id);
        const bIndex = recommendedIds.indexOf(b.id);
        return aIndex - bIndex;
      });

    console.log(`📋 Found ${recommendedModels.length} recommended models`);
    return recommendedModels;
  }

  /**
   * Format model for UI display
   */
  formatModelForUI(model: OpenRouterModel): {
    id: string;
    name: string;
    description: string;
    pricing: string;
    contextLength: string;
    provider: string;
  } {
    // Extract provider from model ID (e.g., "openai/gpt-4o" -> "OpenAI")
    const provider = this.getProviderName(model.id);
    
    // Format pricing (prompt tokens per million)
    const promptPrice = parseFloat(model.pricing.prompt) * 1000000;
    const completionPrice = parseFloat(model.pricing.completion) * 1000000;
    const pricing = `$${promptPrice.toFixed(2)}/$${completionPrice.toFixed(2)} per 1M tokens`;
    
    // Format context length
    const contextLength = `${(model.context_length / 1000).toFixed(0)}K tokens`;

    return {
      id: model.id,
      name: model.name,
      description: model.description || 'No description available',
      pricing,
      contextLength,
      provider
    };
  }

  /**
   * Get provider name from model ID
   */
  private getProviderName(modelId: string): string {
    const providerMap: Record<string, string> = {
      'openai': 'OpenAI',
      'anthropic': 'Anthropic',
      'google': 'Google',
      'meta-llama': 'Meta',
      'mistralai': 'Mistral AI',
      'cohere': 'Cohere',
      'deepseek': 'DeepSeek',
      'qwen': 'Alibaba',
      'huggingface': 'Hugging Face'
    };

    const provider = modelId.split('/')[0];
    return providerMap[provider] || provider.charAt(0).toUpperCase() + provider.slice(1);
  }

  /**
   * Filter models by criteria (chat models, affordable, etc.)
   */
  filterModels(models: OpenRouterModel[], criteria: {
    maxPricePerMillion?: number;
    minContextLength?: number;
    modality?: string;
  }): OpenRouterModel[] {
    return models.filter(model => {
      // Check price (prompt tokens)
      if (criteria.maxPricePerMillion) {
        const promptPrice = parseFloat(model.pricing.prompt) * 1000000;
        if (promptPrice > criteria.maxPricePerMillion) return false;
      }

      // Check context length
      if (criteria.minContextLength && model.context_length < criteria.minContextLength) {
        return false;
      }

      // Check modality (text, multimodal, etc.)
      if (criteria.modality && model.architecture.modality !== criteria.modality) {
        return false;
      }

      return true;
    });
  }
}