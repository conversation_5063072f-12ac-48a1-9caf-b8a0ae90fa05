/**
 * Utility functions for AI package
 */

/**
 * Remove timestamp prefixes from message text
 * Removes patterns like [T20:45], [Y], [2DA] from the beginning of messages
 */
export function stripTimestampPrefix(message: string | null | undefined): string | null | undefined {
  if (!message || typeof message !== 'string') {
    return message;
  }

  // Pattern to match timestamp prefixes: [T##:##], [Y], [#DA] at the start of the message
  const timestampPattern = /^\[(?:T\d{1,2}:\d{2}|Y|\d+DA)\]\s*/;
  
  return message.replace(timestampPattern, '').trim();
}

/**
 * Clean timestamp prefixes from an InstagramResponse object
 * Cleans message1-4 and followup1-6 text fields
 */
export function cleanTimestampPrefixes(response: any): any {
  if (!response || typeof response !== 'object') {
    return response;
  }

  const cleaned = { ...response };

  // Clean immediate messages
  if (cleaned.message1) cleaned.message1 = stripTimestampPrefix(cleaned.message1);
  if (cleaned.message2) cleaned.message2 = stripTimestampPrefix(cleaned.message2);
  if (cleaned.message3) cleaned.message3 = stripTimestampPrefix(cleaned.message3);
  if (cleaned.message4) cleaned.message4 = stripTimestampPrefix(cleaned.message4);

  // Clean followup messages
  if (cleaned.followup1_text) cleaned.followup1_text = stripTimestampPrefix(cleaned.followup1_text);
  if (cleaned.followup2_text) cleaned.followup2_text = stripTimestampPrefix(cleaned.followup2_text);
  if (cleaned.followup3_text) cleaned.followup3_text = stripTimestampPrefix(cleaned.followup3_text);
  if (cleaned.followup4_text) cleaned.followup4_text = stripTimestampPrefix(cleaned.followup4_text);
  if (cleaned.followup5_text) cleaned.followup5_text = stripTimestampPrefix(cleaned.followup5_text);
  if (cleaned.followup6_text) cleaned.followup6_text = stripTimestampPrefix(cleaned.followup6_text);

  return cleaned;
}