import { prisma } from '@workspace/database/client';
import type { OpenRouterResponse, ZAIResponse, AnthropicResponse } from './types';
import type Anthropic from '@anthropic-ai/sdk';

export interface UsageTrackingContext {
  organizationId: string;
  instagramContactId?: string;
  conversationId?: string;
  requestType: string;
}

export interface AiUsageData {
  provider: string;
  model: string;
  totalTokens: number;
  promptTokens: number;
  completionTokens: number;
  reasoningTokens: number;
  cachedTokens: number;
  audioTokens: number;
  totalCost: number;
  upstreamCost?: number;
  responseGenerated: boolean;
}

export class UsageService {
  // Claude pricing constants (under 200K tokens)
  private static readonly CLAUDE_PRICING = {
    INPUT_PER_1M_TOKENS: 3.00,
    OUTPUT_PER_1M_TOKENS: 15.00,
    CACHE_WRITE_5M_PER_1M_TOKENS: 3.75, // 5-minute cache creation
    CACHE_WRITE_1H_PER_1M_TOKENS: 6.00, // 1-hour cache creation
    CACHE_READ_PER_1M_TOKENS: 0.30
  };

  /**
   * Record AI usage from OpenRouter response
   */
  async recordOpenRouterUsage(
    context: UsageTrackingContext,
    response: OpenRouterResponse
  ): Promise<void> {
    console.log('🔍 USAGE TRACKING DEBUG - recordOpenRouterUsage called with:', { context, hasUsage: !!response.usage });
    
    if (!response.usage) {
      console.warn('⚠️ No usage data in OpenRouter response');
      return;
    }

    const usage = response.usage;
    
    // Parse cost - OpenRouter sometimes returns string like "0.001481728086 credits"
    const parsedCost = this.parseCost(usage.cost);
    
    const usageData: AiUsageData = {
      provider: 'openrouter',
      model: response.model || 'unknown',
      totalTokens: usage.total_tokens,
      promptTokens: usage.prompt_tokens,
      completionTokens: usage.completion_tokens,
      reasoningTokens: usage.completion_tokens_details?.reasoning_tokens || 0,
      cachedTokens: usage.prompt_tokens_details?.cached_tokens || 0,
      audioTokens: usage.prompt_tokens_details?.audio_tokens || 0,
      totalCost: parsedCost,
      upstreamCost: usage.cost_details?.upstream_inference_cost,
      responseGenerated: !!response.choices?.[0]?.message?.content
    };

    await this.saveUsageRecord(context, usageData);
  }

  /**
   * Record AI usage from Z.AI response
   */
  async recordZAIUsage(
    context: UsageTrackingContext,
    response: ZAIResponse
  ): Promise<void> {
    console.log('🔍 USAGE TRACKING DEBUG - recordZAIUsage called with:', { context, hasUsage: !!response.usage });
    
    if (!response.usage) {
      console.warn('⚠️ No usage data in Z.AI response');
      return;
    }

    const usage = response.usage;
    
    // Parse cost - Z.AI also returns string like "0.001593314658 credits" 
    const parsedCost = this.parseCost(usage.cost);
    
    const usageData: AiUsageData = {
      provider: 'zai',
      model: response.model || 'unknown',
      totalTokens: usage.total_tokens,
      promptTokens: usage.prompt_tokens,
      completionTokens: usage.completion_tokens,
      reasoningTokens: 0, // Z.AI might not have separate reasoning tokens
      cachedTokens: 0,
      audioTokens: 0,
      totalCost: parsedCost,
      upstreamCost: undefined,
      responseGenerated: !!response.choices?.[0]?.message?.content
    };

    await this.saveUsageRecord(context, usageData);
  }

  /**
   * Calculate cost for Anthropic usage based on token counts
   */
  private calculateAnthropicCost(usage: {
    input_tokens: number;
    output_tokens: number;
    cache_creation_input_tokens?: number | null;
    cache_read_input_tokens?: number | null;
  }, cacheDuration?: '5m' | '1h'): number {
    const inputCost = (usage.input_tokens / 1_000_000) * UsageService.CLAUDE_PRICING.INPUT_PER_1M_TOKENS;
    const outputCost = (usage.output_tokens / 1_000_000) * UsageService.CLAUDE_PRICING.OUTPUT_PER_1M_TOKENS;
    
    // Use appropriate cache creation cost based on duration (default to 5m)
    const cacheWriteCostPerToken = cacheDuration === '1h' 
      ? UsageService.CLAUDE_PRICING.CACHE_WRITE_1H_PER_1M_TOKENS
      : UsageService.CLAUDE_PRICING.CACHE_WRITE_5M_PER_1M_TOKENS;
    
    const cacheWriteCost = ((usage.cache_creation_input_tokens || 0) / 1_000_000) * cacheWriteCostPerToken;
    const cacheReadCost = ((usage.cache_read_input_tokens || 0) / 1_000_000) * UsageService.CLAUDE_PRICING.CACHE_READ_PER_1M_TOKENS;
    
    return inputCost + outputCost + cacheWriteCost + cacheReadCost;
  }

  /**
   * Record AI usage from Anthropic response
   */
  async recordAnthropicUsage(
    context: UsageTrackingContext,
    response: Anthropic.Message,
    cacheDuration?: '5m' | '1h'
  ): Promise<void> {
    console.log('🔍 USAGE TRACKING DEBUG - recordAnthropicUsage called with:', { context, hasUsage: !!response.usage });
    
    if (!response.usage) {
      console.warn('⚠️ No usage data in Anthropic response');
      return;
    }

    const usage = response.usage;
    
    // Calculate cost based on token usage and Claude pricing
    const totalCost = this.calculateAnthropicCost(usage, cacheDuration);
    
    const usageData: AiUsageData = {
      provider: 'anthropic',
      model: response.model || 'unknown',
      totalTokens: usage.input_tokens + usage.output_tokens,
      promptTokens: usage.input_tokens,
      completionTokens: usage.output_tokens,
      reasoningTokens: 0, // Anthropic doesn't separate reasoning tokens
      cachedTokens: usage.cache_read_input_tokens || 0,
      audioTokens: 0, // Anthropic doesn't support audio yet
      totalCost: totalCost,
      responseGenerated: !!response.content.find(block => block.type === 'text')
    };

    console.log('🔍 USAGE TRACKING DEBUG - Anthropic usageData prepared:', usageData);
    
    await this.saveUsageRecord(context, usageData);
  }

  /**
   * Parse cost from OpenRouter response (handles both number and string formats)
   */
  private parseCost(cost: any): number {
    if (typeof cost === 'number') {
      return cost;
    }
    
    if (typeof cost === 'string') {
      // Extract numeric value from strings like "0.001481728086 credits"
      const match = cost.match(/([0-9.]+)/);
      if (match) {
        const parsed = parseFloat(match[1]);
        return isNaN(parsed) ? 0 : parsed;
      }
    }
    
    return 0;
  }

  /**
   * Validate usage context before database insertion
   */
  private validateContext(context: UsageTrackingContext): void {
    if (!context.organizationId || typeof context.organizationId !== 'string') {
      throw new Error(`Invalid organizationId: ${context.organizationId}`);
    }
    if (!context.requestType || typeof context.requestType !== 'string') {
      throw new Error(`Invalid requestType: ${context.requestType}`);
    }
  }

  /**
   * Save usage record to database
   */
  private async saveUsageRecord(
    context: UsageTrackingContext,
    usage: AiUsageData
  ): Promise<void> {
    try {
      // Validate context data first
      this.validateContext(context);
      
      console.log('💾 Attempting to save usage record:', {
        organizationId: context.organizationId,
        provider: usage.provider,
        model: usage.model,
        totalTokens: usage.totalTokens,
        totalCost: usage.totalCost,
        requestType: context.requestType
      });

      await prisma.aiUsage.create({
        data: {
          organizationId: context.organizationId,
          instagramContactId: context.instagramContactId,
          conversationId: context.conversationId,
          provider: usage.provider,
          model: usage.model,
          totalTokens: usage.totalTokens,
          promptTokens: usage.promptTokens,
          completionTokens: usage.completionTokens,
          reasoningTokens: usage.reasoningTokens,
          cachedTokens: usage.cachedTokens,
          audioTokens: usage.audioTokens,
          totalCost: usage.totalCost,
          upstreamCost: usage.upstreamCost,
          requestType: context.requestType,
          responseGenerated: usage.responseGenerated
        }
      });

      console.log('💰 Usage recorded successfully:', {
        organization: context.organizationId,
        provider: usage.provider,
        model: usage.model,
        totalTokens: usage.totalTokens,
        tokenBreakdown: {
          IN: usage.promptTokens,
          CACHED: usage.cachedTokens,
          OUT: usage.completionTokens,
          REASONING: usage.reasoningTokens,
          AUDIO: usage.audioTokens
        },
        cost: usage.totalCost
      });

    } catch (error) {
      console.error('❌ Failed to save AI usage record - DETAILED ERROR:');
      console.error('📋 Context:', context);
      console.error('📊 Usage data:', usage);
      console.error('🔥 Error details:', error);
      
      // Log specific database validation errors
      if (error instanceof Error) {
        console.error('🔍 Error message:', error.message);
        console.error('📚 Error stack:', error.stack);
      }
      
      // Log more specific Prisma errors
      if (error && typeof error === 'object' && 'code' in error) {
        console.error('🔍 Prisma error code:', (error as any).code);
        console.error('🔍 Prisma error meta:', (error as any).meta);
      }
      
      // Don't throw here - usage tracking shouldn't break the main flow
    }
  }

  /**
   * Get usage statistics for an organization
   */
  async getOrganizationUsage(
    organizationId: string,
    dateFrom?: Date,
    dateTo?: Date
  ) {
    const where: any = { organizationId };
    
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = dateFrom;
      if (dateTo) where.createdAt.lte = dateTo;
    }

    const [summary, byModel, byProvider] = await Promise.all([
      // Overall summary
      prisma.aiUsage.aggregate({
        where,
        _sum: {
          totalTokens: true,
          promptTokens: true,
          completionTokens: true,
          reasoningTokens: true,
          cachedTokens: true,
          totalCost: true
        },
        _count: {
          id: true
        }
      }),
      
      // Usage by model
      prisma.aiUsage.groupBy({
        where,
        by: ['model'],
        _sum: {
          totalTokens: true,
          totalCost: true
        },
        _count: {
          id: true
        }
      }),
      
      // Usage by provider
      prisma.aiUsage.groupBy({
        where,
        by: ['provider'],
        _sum: {
          totalTokens: true,
          totalCost: true
        },
        _count: {
          id: true
        }
      })
    ]);

    return {
      summary: {
        totalRequests: summary._count.id,
        totalTokens: summary._sum.totalTokens || 0,
        promptTokens: summary._sum.promptTokens || 0,
        completionTokens: summary._sum.completionTokens || 0,
        reasoningTokens: summary._sum.reasoningTokens || 0,
        cachedTokens: summary._sum.cachedTokens || 0,
        totalCost: summary._sum.totalCost || 0
      },
      byModel,
      byProvider
    };
  }
}