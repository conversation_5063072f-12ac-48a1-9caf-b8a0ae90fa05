import { InstallProvider } from '@slack/oauth';
import { WebClient } from '@slack/web-api';
import { prisma } from '@workspace/database/client';

interface SlackInstallationData {
  organizationId: string;
  teamId: string;
  teamName?: string;
  channelId: string;
  channelName?: string;
  accessToken?: string;
  botUserId?: string;
  appId?: string;
  authedUserId?: string;
  scope?: string;
}

class SlackOAuthService {
  private installProvider: InstallProvider | null = null;

  private getInstallProvider(): InstallProvider {
    if (!this.installProvider) {
      const clientId = process.env.SLACK_CLIENT_ID;
      const clientSecret = process.env.SLACK_CLIENT_SECRET;
      const stateSecret = process.env.SLACK_STATE_SECRET;

      if (!clientId || !clientSecret || !stateSecret) {
        throw new Error('Slack OAuth credentials not configured. Please set SLACK_CLIENT_ID, SLACK_CLIENT_SECRET, and SLACK_STATE_SECRET environment variables.');
      }

      this.installProvider = new InstallProvider({
        clientId,
        clientSecret,
        stateSecret,
      
      // Custom installation store
      installationStore: {
        storeInstallation: async (installation) => {
          const metadata = installation.metadata ? JSON.parse(installation.metadata) : {};
          const { organizationId } = metadata;
          
          if (!organizationId) {
            throw new Error('Organization ID not found in metadata');
          }

          // Save installation to database
          await this.saveSlackIntegration({
            organizationId,
            teamId: installation.team?.id || '',
            teamName: installation.team?.name,
            channelId: installation.incomingWebhook?.channelId || '',
            channelName: installation.incomingWebhook?.channel,
            accessToken: installation.bot?.token,
            botUserId: installation.bot?.userId,
            appId: installation.appId,
            authedUserId: installation.user?.id,
            scope: installation.bot?.scopes?.join(','),
          });

          console.log(`✅ Slack integration stored for org: ${organizationId}`);
        },

        fetchInstallation: async (installQuery) => {
          // This is used by the SDK for token refresh, etc.
          // We'll fetch from our database
          const integration = await prisma.slackIntegration.findFirst({
            where: {
              teamId: installQuery.teamId,
            },
          });

          if (!integration) {
            throw new Error('Installation not found');
          }

          // Convert back to SDK format
          return {
            team: { id: integration.teamId, name: integration.teamName || undefined },
            bot: {
              token: integration.accessToken || '',
              userId: integration.botUserId || '',
              id: integration.botUserId || '',
              scopes: integration.scope?.split(',') || [],
            },
            user: { 
              id: integration.authedUserId || '',
              token: integration.accessToken || '',
              scopes: integration.scope?.split(',') || []
            },
            enterprise: undefined,
            appId: integration.appId || undefined,
            isEnterpriseInstall: false,
            authVersion: 'v2' as const,
          };
        },

        deleteInstallation: async (installQuery) => {
          await prisma.slackIntegration.deleteMany({
            where: {
              teamId: installQuery.teamId,
            },
          });
        },
      },
    });
    }

    return this.installProvider;
  }

  /**
   * Generate installation URL for OAuth flow
   */
  async generateInstallUrl(organizationId: string): Promise<string> {
    return this.getInstallProvider().generateInstallUrl({
      scopes: ['incoming-webhook', 'chat:write', 'chat:write.public', 'channels:join'],
      metadata: JSON.stringify({ organizationId }),
    });
  }

  /**
   * Handle OAuth callback
   */
  async handleCallback(req: any, res: any, options?: any): Promise<void> {
    return this.getInstallProvider().handleCallback(req, res, {
      success: () => {
        console.log('✅ Slack installation successful');
        res.redirect('/integrations?slack=success');
      },
      failure: (error) => {
        console.error('❌ Slack installation failed:', error);
        res.redirect('/integrations?slack=error');
      },
      ...options,
    });
  }

  /**
   * Exchange OAuth code for access token (manual flow for Next.js compatibility)
   */
  async exchangeOAuthCode(code: string, organizationId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const clientId = process.env.SLACK_CLIENT_ID;
      const clientSecret = process.env.SLACK_CLIENT_SECRET;
      const redirectUri = process.env.SLACK_REDIRECT_URI;

      if (!clientId || !clientSecret || !redirectUri) {
        throw new Error('Slack OAuth credentials not configured');
      }

      // Exchange code for access token
      const response = await fetch('https://slack.com/api/oauth.v2.access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: clientId,
          client_secret: clientSecret,
          code: code,
          redirect_uri: redirectUri,
        }),
      });

      const data = await response.json();

      if (!data.ok) {
        throw new Error(data.error || 'OAuth exchange failed');
      }

      // Save installation data
      await this.saveSlackIntegration({
        organizationId,
        teamId: data.team?.id || '',
        teamName: data.team?.name,
        channelId: data.incoming_webhook?.channel_id || '',
        channelName: data.incoming_webhook?.channel,
        accessToken: data.access_token,
        botUserId: data.bot_user_id,
        appId: data.app_id,
        authedUserId: data.authed_user?.id,
        scope: data.scope,
      });

      console.log(`✅ Slack OAuth completed successfully for org: ${organizationId}`);
      return { success: true };

    } catch (error) {
      console.error('❌ Slack OAuth exchange failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Save Slack integration to database
   */
  private async saveSlackIntegration(data: SlackInstallationData): Promise<void> {
    await prisma.slackIntegration.upsert({
      where: {
        organizationId: data.organizationId,
      },
      update: {
        teamId: data.teamId,
        teamName: data.teamName,
        channelId: data.channelId,
        channelName: data.channelName,
        accessToken: data.accessToken,
        botUserId: data.botUserId,
        appId: data.appId,
        authedUserId: data.authedUserId,
        scope: data.scope,
        enabled: true,
        updatedAt: new Date(),
      },
      create: {
        organizationId: data.organizationId,
        teamId: data.teamId,
        teamName: data.teamName,
        channelId: data.channelId,
        channelName: data.channelName,
        accessToken: data.accessToken,
        botUserId: data.botUserId,
        appId: data.appId,
        authedUserId: data.authedUserId,
        scope: data.scope,
        enabled: true,
      },
    });
  }

  /**
   * Send notification using Web API
   */
  async sendNotification(organizationId: string, message: {
    text: string;
    blocks?: any[];
  }): Promise<boolean> {
    try {
      const integration = await prisma.slackIntegration.findUnique({
        where: { organizationId, enabled: true },
      });

      if (!integration?.accessToken) {
        console.error('No Slack integration found or missing access token');
        return false;
      }

      const client = new WebClient(integration.accessToken);

      try {
        await client.chat.postMessage({
          channel: integration.channelId,
          text: message.text,
          blocks: message.blocks,
        });

        return true;
      } catch (postError: any) {
        // If bot is not in channel, try to join first
        if (postError.code === 'slack_webapi_platform_error' && postError.data?.error === 'not_in_channel') {
          console.log('Bot not in channel, attempting to join...');
          
          try {
            await client.conversations.join({
              channel: integration.channelId,
            });
            
            // Retry posting after joining
            await client.chat.postMessage({
              channel: integration.channelId,
              text: message.text,
              blocks: message.blocks,
            });

            console.log('✅ Successfully joined channel and sent notification');
            return true;
          } catch (joinError: any) {
            console.error('❌ Failed to join channel:', joinError);
            // If joining fails (e.g., private channel), provide helpful error message
            if (joinError.data?.error === 'cant_join_general') {
              console.error('Cannot join #general channel automatically. Bot must be invited manually.');
            } else if (joinError.data?.error === 'is_private') {
              console.error('Cannot join private channel automatically. Please invite the bot using: /invite @BotName');
            }
            return false;
          }
        } else {
          // Re-throw other errors
          throw postError;
        }
      }
    } catch (error) {
      console.error('Failed to send Slack notification:', error);
      return false;
    }
  }

  /**
   * Get Slack integration for organization
   */
  async getIntegration(organizationId: string) {
    return prisma.slackIntegration.findUnique({
      where: { organizationId },
    });
  }

  /**
   * Delete Slack integration
   */
  async deleteIntegration(organizationId: string): Promise<void> {
    await prisma.slackIntegration.delete({
      where: { organizationId },
    });
  }
}

export const slackOAuthService = new SlackOAuthService();