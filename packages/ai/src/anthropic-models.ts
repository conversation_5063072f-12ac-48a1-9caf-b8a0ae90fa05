import Anthropic from '@anthropic-ai/sdk';

export interface AnthropicModelInfo {
  id: string;
  name: string;
  provider: string;
  contextLength: string;
  pricing: string;
  capabilities: string[];
  isAvailable: boolean;
}

export interface AnthropicModelsResponse {
  models: AnthropicModelInfo[];
  total: number;
  lastUpdated: string;
}

export class AnthropicModelsService {
  private readonly client: Anthropic | null = null;
  private readonly apiKey: string | null = null;

  constructor(apiKey?: string) {
    if (apiKey) {
      this.apiKey = apiKey;
      this.client = new Anthropic({ apiKey });
    }
  }

  /**
   * Get available Anthropic models from the official API
   */
  async getModels(): Promise<AnthropicModelsResponse> {
    if (!this.client) {
      // Return fallback static models when no API key is available
      return this.getStaticModels();
    }

    try {
      console.log('📡 Fetching live models from Anthropic API...');
      
      // Make request to Anthropic models API
      const response = await fetch('https://api.anthropic.com/v1/models', {
        headers: {
          'x-api-key': this.apiKey!,
          'anthropic-version': '2023-06-01',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn('⚠️ Failed to fetch from Anthropic API, using static models');
        return this.getStaticModels();
      }

      const data = await response.json();
      console.log('✅ Fetched live models from Anthropic:', data);

      // Transform Anthropic API response to our format
      const models: AnthropicModelInfo[] = data.data?.map((model: any) => ({
        id: model.id,
        name: this.formatModelName(model.id),
        provider: 'Anthropic',
        contextLength: this.getContextLength(model.id),
        pricing: this.getPricing(model.id),
        capabilities: this.getCapabilities(model.id),
        isAvailable: true
      })) || [];

      return {
        models,
        total: models.length,
        lastUpdated: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ Error fetching from Anthropic API:', error);
      return this.getStaticModels();
    }
  }

  /**
   * Get static fallback models when API is unavailable
   */
  private getStaticModels(): AnthropicModelsResponse {
    const staticModels: AnthropicModelInfo[] = [
      {
        id: 'claude-sonnet-4-20250514',
        name: 'Claude Sonnet 4',
        provider: 'Anthropic',
        contextLength: '200K tokens',
        pricing: '$3/1M input, $15/1M output',
        capabilities: ['Text', 'Vision', 'Tools', 'Thinking'],
        isAvailable: true
      },
      {
        id: 'claude-3-5-sonnet-20241022',
        name: 'Claude 3.5 Sonnet',
        provider: 'Anthropic',
        contextLength: '200K tokens',
        pricing: '$3/1M input, $15/1M output',
        capabilities: ['Text', 'Vision', 'Tools', 'Thinking'],
        isAvailable: true
      },
      {
        id: 'claude-3-5-haiku-20241022',
        name: 'Claude 3.5 Haiku',
        provider: 'Anthropic',
        contextLength: '200K tokens',
        pricing: '$0.25/1M input, $1.25/1M output',
        capabilities: ['Text', 'Vision', 'Tools', 'Fast'],
        isAvailable: true
      },
      {
        id: 'claude-3-opus-20240229',
        name: 'Claude 3 Opus',
        provider: 'Anthropic',
        contextLength: '200K tokens',
        pricing: '$15/1M input, $75/1M output',
        capabilities: ['Text', 'Vision', 'Tools', 'Most Capable'],
        isAvailable: true
      },
      {
        id: 'claude-3-sonnet-20240229',
        name: 'Claude 3 Sonnet',
        provider: 'Anthropic',
        contextLength: '200K tokens',
        pricing: '$3/1M input, $15/1M output',
        capabilities: ['Text', 'Vision', 'Tools', 'Balanced'],
        isAvailable: true
      },
      {
        id: 'claude-3-haiku-20240307',
        name: 'Claude 3 Haiku',
        provider: 'Anthropic',
        contextLength: '200K tokens',
        pricing: '$0.25/1M input, $1.25/1M output',
        capabilities: ['Text', 'Vision', 'Tools', 'Fast'],
        isAvailable: true
      }
    ];

    return {
      models: staticModels,
      total: staticModels.length,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Test if a specific model is available
   */
  async testModelAvailability(modelId: string): Promise<boolean> {
    if (!this.client) {
      return false;
    }

    try {
      // Make a minimal request to test if the model is available
      await this.client.messages.create({
        model: modelId,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 1
      });
      return true;
    } catch (error) {
      console.warn(`Model ${modelId} not available:`, error);
      return false;
    }
  }

  /**
   * Get models with live availability check
   */
  async getModelsWithAvailabilityCheck(): Promise<AnthropicModelsResponse> {
    const response = await this.getModels();
    
    if (!this.client) {
      return response;
    }

    // Test availability for each model (in parallel)
    const availabilityChecks = response.models.map(async (model) => {
      const isAvailable = await this.testModelAvailability(model.id);
      return { ...model, isAvailable };
    });

    const modelsWithAvailability = await Promise.all(availabilityChecks);
    
    return {
      ...response,
      models: modelsWithAvailability
    };
  }

  /**
   * Format model ID to human-readable name
   */
  private formatModelName(modelId: string): string {
    const nameMap: Record<string, string> = {
      'claude-sonnet-4-20250514': 'Claude Sonnet 4',
      'claude-3-5-sonnet-20241022': 'Claude 3.5 Sonnet',
      'claude-3-5-haiku-20241022': 'Claude 3.5 Haiku',
      'claude-3-opus-20240229': 'Claude 3 Opus',
      'claude-3-sonnet-20240229': 'Claude 3 Sonnet',
      'claude-3-haiku-20240307': 'Claude 3 Haiku',
      'claude-3-5-sonnet-20240620': 'Claude 3.5 Sonnet (June)',
      'claude-instant-1.2': 'Claude Instant'
    };
    
    return nameMap[modelId] || modelId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Get context length for model
   */
  private getContextLength(modelId: string): string {
    if (modelId.includes('claude-3') || modelId.includes('claude-3-5') || modelId.includes('claude-sonnet-4')) {
      return '200K tokens';
    }
    return '100K tokens';
  }

  /**
   * Get pricing information for model
   */
  private getPricing(modelId: string): string {
    const pricingMap: Record<string, string> = {
      'claude-sonnet-4-20250514': '$3/1M input, $15/1M output, $3.75/$6 cache (5m/1h), $0.30 cache read',
      'claude-3-5-sonnet-20241022': '$3/1M input, $15/1M output, $3.75/$6 cache (5m/1h), $0.30 cache read',
      'claude-3-5-haiku-20241022': '$0.25/1M input, $1.25/1M output, $3.75/$6 cache (5m/1h), $0.30 cache read',
      'claude-3-opus-20240229': '$15/1M input, $75/1M output, $3.75/$6 cache (5m/1h), $0.30 cache read',
      'claude-3-sonnet-20240229': '$3/1M input, $15/1M output, $3.75/$6 cache (5m/1h), $0.30 cache read',
      'claude-3-haiku-20240307': '$0.25/1M input, $1.25/1M output, $3.75/$6 cache (5m/1h), $0.30 cache read'
    };
    
    return pricingMap[modelId] || 'See Anthropic pricing';
  }

  /**
   * Get capabilities for model
   */
  private getCapabilities(modelId: string): string[] {
    if (modelId.includes('claude-3-5') || modelId.includes('claude-sonnet-4')) {
      return ['Text', 'Vision', 'Tools', 'Thinking'];
    }
    if (modelId.includes('claude-3-opus')) {
      return ['Text', 'Vision', 'Tools', 'Most Capable'];
    }
    if (modelId.includes('claude-3-sonnet')) {
      return ['Text', 'Vision', 'Tools', 'Balanced'];
    }
    if (modelId.includes('claude-3-haiku')) {
      return ['Text', 'Vision', 'Tools', 'Fast'];
    }
    return ['Text', 'Tools'];
  }
}