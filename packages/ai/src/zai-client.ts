import { ForbiddenError } from '@workspace/common/errors';
import type { 
  ZAIConfig, 
  ZAIRequest, 
  ZAIResponse,
  ZAIMessage,
  InstagramResponse
} from './types';
import { InstagramResponseSchema } from './types';
import { UsageService, type UsageTrackingContext } from './usage-service';
import { cleanTimestampPrefixes } from './utils';

export class ZAIClient {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://api.z.ai/api/paas/v4';
  private readonly usageService: UsageService;

  constructor(config: ZAIConfig) {
    this.apiKey = config.apiKey;
    this.usageService = new UsageService();
  }


  async generateResponse(request: ZAIRequest, usageContext?: UsageTrackingContext): Promise<InstagramResponse> {
    try {
      console.log('🤖 Generating AI response with Z.AI GLM-4.5');
      
      // Log full request details
      console.log('📤 Z.AI Request:', {
        model: request.model,
        thinking: request.thinking?.type,
        messageCount: request.messages.length,
        systemPromptLength: request.messages[0]?.content?.length || 0,
        userMessageLength: request.messages[1]?.content?.length || 0
      });

      // Log full request body for debugging
      console.log('📤 Full Z.AI Request Body:', JSON.stringify(request, null, 2));
      
      // Log reasoning configuration specifically
      if (request.thinking) {
        console.log('🧠 Z.AI Reasoning Config:', {
          thinkingEnabled: request.thinking.type === 'enabled',
          model: request.model,
          supportsReasoning: request.model.includes('glm-4.5')
        });
      }
      
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'https://localhost:3000',
          'X-Title': 'AIChromaPRO Instagram AI'
        },
        body: JSON.stringify(request)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Z.AI API error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorData
        });
        
        if (response.status === 401) {
          throw new ForbiddenError('Invalid Z.AI API key');
        }
        
        throw new Error(
          `Z.AI API error: ${response.status} ${response.statusText} - ${
            errorData.error?.message || 'Unknown error'
          }`
        );
      }

      const data: ZAIResponse = await response.json();
      
      // Log full response details
      console.log('📥 Z.AI Response:', {
        choices: data.choices?.length || 0,
        finishReason: data.choices?.[0]?.finish_reason,
        model: data.model,
        hasContent: !!data.choices?.[0]?.message?.content,
        contentLength: data.choices?.[0]?.message?.content?.length || 0,
        hasReasoning: !!data.choices?.[0]?.message?.reasoning_content
      });
      
      if (!data.choices?.[0]?.message?.content) {
        console.error('❌ No response content from Z.AI API. Full response:', data);
        throw new Error('No response content from Z.AI API');
      }

      // Log and persist usage if available
      if (data.usage) {
        console.log('💰 Z.AI usage:', {
          totalTokens: data.usage.total_tokens,
          tokenBreakdown: {
            IN: data.usage.prompt_tokens,
            CACHED: 0, // Z.AI doesn't support cached tokens yet
            OUT: data.usage.completion_tokens,
            REASONING: 0 // Z.AI doesn't separate reasoning tokens
          },
          cost: data.usage.cost ? `${data.usage.cost} credits` : 'N/A'
        });

        // Persist usage data to database if context provided
        if (usageContext) {
          await this.usageService.recordZAIUsage(usageContext, data);
        }
      }

      // Log and store reasoning if available
      const reasoningContent = data.choices[0].message.reasoning_content;
      if (reasoningContent) {
        console.log('🧠 GLM-4.5 Reasoning:', reasoningContent);
      } else {
        console.log('⚠️ No reasoning_content in Z.AI response. Full message object:', JSON.stringify(data.choices[0].message, null, 2));
      }

      // Log raw response content for debugging
      console.log('📄 Raw Response Content:', data.choices[0].message.content);

      // Parse and validate the structured response
      let responseContent = data.choices[0].message.content;
      
      // Clean GLM-4.5V response content that may be wrapped in box tags
      responseContent = responseContent
        .replace(/<\|begin_of_box\|>/g, '')
        .replace(/<\|end_of_box\|>/g, '')
        .trim();
      
      let parsedResponse: unknown;
      
      try {
        parsedResponse = JSON.parse(responseContent);
        
        // Convert GLM-4.5V followup format to our expected format
        parsedResponse = this.normalizeFollowupFormat(parsedResponse);
        
        // Clean timestamp prefixes from messages
        parsedResponse = cleanTimestampPrefixes(parsedResponse);
      } catch (parseError) {
        console.error('❌ Failed to parse GLM-4.5 response as JSON:');
        console.error('📄 Raw Content (first 1000 chars):', responseContent.substring(0, 1000));
        console.error('📄 Raw Content (full):', responseContent);
        console.error('🔍 Parse Error:', parseError);
        console.error('📏 Content Length:', responseContent.length);
        console.error('🔡 Content Type Check:', typeof responseContent);
        throw new Error(`GLM-4.5 response is not valid JSON. Length: ${responseContent.length}, Type: ${typeof responseContent}`);
      }

      // Log parsed response
      console.log('🔍 Parsed JSON Response:', parsedResponse);

      // Validate against schema
      const validationResult = InstagramResponseSchema.safeParse(parsedResponse);
      
      if (!validationResult.success) {
        console.error('❌ GLM-4.5 response validation failed:', {
          errors: validationResult.error.errors,
          response: parsedResponse
        });
        throw new Error('GLM-4.5 response does not match expected schema');
      }

      // Add reasoning to the response
      const finalResponse = { 
        ...validationResult.data, 
        aiReasoning: reasoningContent 
      };

      // Log final validated response
      console.log('✅ Generated valid GLM-4.5 response:', {
        stage: finalResponse.instagramStage,
        priority: finalResponse.engagementPriority,
        isDisqualified: finalResponse.isDisqualified,
        hasMessage1: !!finalResponse.message1,
        hasMessage2: !!finalResponse.message2,
        hasMessage3: !!finalResponse.message3,
        hasMessage4: !!finalResponse.message4,
        notesLength: finalResponse.notes?.length || 0,
        hasReasoning: !!finalResponse.aiReasoning,
        reasoningLength: finalResponse.aiReasoning?.length || 0,
        followupsCount: [
          finalResponse.followup1_text,
          finalResponse.followup2_text,
          finalResponse.followup3_text,
          finalResponse.followup4_text,
          finalResponse.followup5_text,
          finalResponse.followup6_text
        ].filter(Boolean).length
      });
      
      return finalResponse;

    } catch (error) {
      console.error('❌ Error in Z.AI client:', error);
      throw error;
    }
  }

  /**
   * Create cached system message with XML prompt
   * Note: Z.AI may not support caching like OpenRouter, but we maintain the same interface
   */
  createCachedSystemMessage(xmlPrompt: string, dateTimePrompt: string): ZAIMessage {
    return {
      role: 'system',
      content: `${xmlPrompt}\n\n${dateTimePrompt}`
    };
  }

  /**
   * Create structured conversation messages for Z.AI
   */
  createConversationMessages(structuredMessages: ZAIMessage[]): ZAIMessage[] {
    // Z.AI needs content as string, so convert content arrays to strings
    return structuredMessages.map(msg => ({
      ...msg,
      content: Array.isArray(msg.content) 
        ? msg.content.map(c => c.text).join('\n')
        : msg.content
    }));
  }

  /**
   * Build Z.AI request with structured outputs and reasoning
   */
  buildRequest(
    systemMessage: ZAIMessage,
    conversationMessages: ZAIMessage[],
    config: ZAIConfig
  ): ZAIRequest {
    // Add JSON prefill to force JSON response format
    const messagesWithPrefill = [
      systemMessage, 
      ...conversationMessages,
      { role: 'assistant' as const, content: [{ type: 'text' as const, text: '{' }] }
    ];

    const request: ZAIRequest = {
      model: config.model,
      messages: messagesWithPrefill
    };

    // Add thinking configuration for GLM-4.5 if enabled
    if (config.enableReasoning) {
      request.thinking = {
        type: 'enabled'
      };
    } else {
      request.thinking = {
        type: 'disabled'
      };
    }

    return request;
  }

  /**
   * Normalize GLM-4.5V followup format to our expected format
   * GLM-4.5V uses: followUp1: {time: "", message: ""}
   * We expect: followup1_text: "", followup1_scheduledAt: ""
   */
  private normalizeFollowupFormat(response: any): any {
    if (!response || typeof response !== 'object') {
      return response;
    }

    // Convert GLM-4.5V followup format
    for (let i = 1; i <= 6; i++) {
      const followUpKey = `followUp${i}`;
      if (response[followUpKey] && typeof response[followUpKey] === 'object') {
        const followUp = response[followUpKey];
        
        // Convert to our format
        response[`followup${i}_text`] = followUp.message || followUp.text;
        response[`followup${i}_scheduledAt`] = followUp.time || followUp.scheduledAt;
        
        // Remove the original GLM-4.5V format
        delete response[followUpKey];
      }
    }

    return response;
  }
}