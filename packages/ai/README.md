# @workspace/ai

AI services for Instagram response generation using OpenRouter.

## Features

- OpenRouter API client with prompt caching
- Structured outputs with JSON schema validation
- Reasoning tokens support
- XML-tagged prompt building
- Multi-message scheduling

## Usage

```typescript
import { generateInstagramResponse } from '@workspace/ai';

const response = await generateInstagramResponse({
  messages: conversationMessages,
  contact: instagramContact,
  organizationSettings: settings
});
```