{"name": "@workspace/ai", "version": "0.0.0", "private": true, "type": "module", "exports": {".": {"import": "./src/index.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@anthropic-ai/sdk": "^0.60.0", "@slack/oauth": "^3.0.4", "@slack/web-api": "^7.10.0", "@types/handlebars": "^4.1.0", "@workspace/common": "workspace:*", "@workspace/database": "workspace:*", "handlebars": "^4.7.8", "zod": "3.25.56"}, "devDependencies": {"@types/node": "22.15.30", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "eslint": "9.28.0", "typescript": "5.8.3", "vitest": "^2.1.8"}}