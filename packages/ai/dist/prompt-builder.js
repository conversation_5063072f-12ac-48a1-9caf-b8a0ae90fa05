import { getCalendlyToolsPrompt } from './calendly-tools';
import Handlebars from 'handlebars';
export class PromptBuilder {
    constructor() {
        // Register Handlebars helpers for conditional logic (only register once)
        if (!Handlebars.helpers.eq) {
            Handlebars.registerHelper('eq', function (a, b) {
                return a === b;
            });
        }
        if (!Handlebars.helpers.or) {
            Handlebars.registerHelper('or', function (...args) {
                // Remove the options object (last argument)
                const values = args.slice(0, -1);
                return values.some(Boolean);
            });
        }
    }
    /**
     * Build system prompt by processing template variables
     * Returns clean processed prompt without XML wrappers
     */
    buildSystemPrompt(config, botStyleVariables, calendlyIntegration) {
        if (!config.botStylePrompt) {
            throw new Error('BotStyle prompt is required for AI response generation');
        }
        let processedPrompt = config.botStylePrompt;
        // Use bot style variables for template processing
        if (botStyleVariables) {
            console.log('📊 Processing bot style variables:', Object.keys(botStyleVariables));
            // First pass: Use Handlebars to process conditionals
            try {
                const template = Handlebars.compile(config.botStylePrompt);
                processedPrompt = template(botStyleVariables);
                // Decode HTML entities that Handlebars may have encoded
                processedPrompt = processedPrompt
                    .replace(/&#x3D;/g, '=')
                    .replace(/&amp;/g, '&')
                    .replace(/&lt;/g, '<')
                    .replace(/&gt;/g, '>')
                    .replace(/&quot;/g, '"')
                    .replace(/&#x27;/g, "'");
                console.log('✅ Handlebars processing successful');
            }
            catch (error) {
                console.warn('⚠️ Handlebars processing failed, using fallback:', error);
                // Fall back to simple variable replacement if Handlebars fails
                processedPrompt = config.botStylePrompt;
            }
            // Second pass: Use existing processBotStyleVariables for remaining variable processing
            processedPrompt = this.processBotStyleVariables(processedPrompt, botStyleVariables);
        }
        else {
            console.warn('⚠️ No bot style variables found - prompt will have unprocessed variables');
        }
        // Add Calendly tools if integration is enabled
        if (calendlyIntegration?.enabled) {
            console.log('📅 Adding Calendly tools to system prompt');
            const calendlyToolsPrompt = getCalendlyToolsPrompt();
            processedPrompt += '\n\n' + calendlyToolsPrompt;
        }
        return processedPrompt;
    }
    /**
     * Build dynamic date/time system prompt
     * This should not be cached as it changes every second
     */
    buildDateTimePrompt() {
        const now = new Date();
        const year = now.getFullYear();
        const month = (now.getMonth() + 1).toString().padStart(2, '0');
        const day = now.getDate().toString().padStart(2, '0');
        const currentDate = `${year}-${month}-${day}`;
        const currentTime = now.toTimeString().split(' ')[0]; // HH:MM:SS format
        return `Date now: ${currentDate} ${currentTime}`;
    }
    /**
     * Build structured messages array for all AI providers
     * Groups consecutive messages from same sender with timestamps
     */
    buildMessages(messages, contact, maxMessages) {
        const result = [];
        // Add profile context as system message if available
        if (contact.profileInsights) {
            const profileContext = this.buildProfileContext(contact);
            result.push({
                role: 'system',
                content: [{ type: 'text', text: profileContext }]
            });
            console.log('📊 Added profile context to conversation');
        }
        // Get recent messages limited by maxConversationMessages
        const recentMessages = messages.slice(-maxMessages);
        if (recentMessages.length === 0) {
            result.push({
                role: 'user',
                content: [{ type: 'text', text: '[No conversation history available]' }]
            });
            return result;
        }
        const structuredMessages = [];
        let currentMessageGroup = [];
        let currentRole = null;
        for (const msg of recentMessages) {
            const isFromUser = msg.isFromUser;
            const role = isFromUser ? 'user' : 'assistant';
            // Calculate smart timestamp format using local timezone
            const now = new Date();
            const msgDate = new Date(msg.createdAt);
            // Create date objects at midnight in local timezone
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const messageDate = new Date(msgDate.getFullYear(), msgDate.getMonth(), msgDate.getDate());
            // Calculate days difference
            const daysDiff = Math.floor((today.getTime() - messageDate.getTime()) / (24 * 60 * 60 * 1000));
            // Debug logging (remove after confirming it works)
            // if (daysDiff > 0 && daysDiff <= 3) {
            //   console.log(`📅 Date calc debug - Message date: ${msg.createdAt}, Today: ${today.toISOString()}, Message: ${messageDate.toISOString()}, Days diff: ${daysDiff}`);
            // }
            let timestamp;
            if (daysDiff === 0) {
                // Today: [T15:30] format (no seconds)
                const hours = msgDate.getHours().toString().padStart(2, '0');
                const minutes = msgDate.getMinutes().toString().padStart(2, '0');
                timestamp = `T${hours}:${minutes}`;
            }
            else if (daysDiff === 1) {
                // Yesterday: [Y] format
                timestamp = 'Y';
            }
            else {
                // Older: [2DA] format for 2+ days ago
                timestamp = `${daysDiff}DA`;
            }
            const messageText = `[${timestamp}] ${msg.content}`;
            // If role changes or first message, start new group
            if (currentRole !== role) {
                // Save previous group if exists
                if (currentMessageGroup.length > 0 && currentRole) {
                    structuredMessages.push({
                        role: currentRole,
                        content: currentMessageGroup
                    });
                }
                // Start new group
                currentRole = role;
                currentMessageGroup = [{ type: 'text', text: messageText }];
            }
            else {
                // Same role, add to current group
                currentMessageGroup.push({ type: 'text', text: messageText });
            }
        }
        // Add final group
        if (currentMessageGroup.length > 0 && currentRole) {
            structuredMessages.push({
                role: currentRole,
                content: currentMessageGroup
            });
        }
        // Ensure we end with a user message for AI to respond to
        if (structuredMessages[structuredMessages.length - 1]?.role !== 'user') {
            structuredMessages.push({
                role: 'user',
                content: [{ type: 'text', text: 'Please respond to the conversation above.' }]
            });
        }
        // Combine profile context with conversation messages
        result.push(...structuredMessages);
        return result;
    }
    /**
     * Build profile context system message
     */
    buildProfileContext(contact) {
        if (!contact.profileInsights)
            return '';
        return `[User Profile Context for @${contact.username}]\n${contact.profileInsights}`;
    }
    /**
     * Process bot style variables in template using {{variable}} format
     * Handles dynamic variables with flexible types (text, arrays, structured lists, etc.)
     */
    processBotStyleVariables(template, variables) {
        let processed = template;
        for (const [variableName, value] of Object.entries(variables)) {
            if (value === null || value === undefined) {
                // Replace with empty string for null/undefined values
                processed = processed.replace(new RegExp(`\\{\\{${variableName}\\}\\}`, 'g'), '');
                continue;
            }
            // Handle different variable types
            if (typeof value === 'string') {
                // Simple text replacement
                processed = processed.replace(new RegExp(`\\{\\{${variableName}\\}\\}`, 'g'), value);
            }
            else if (Array.isArray(value)) {
                // Check if it's a simple array or structured list
                if (value.length > 0 && typeof value[0] === 'string') {
                    // Simple array - handle {{#variable}}...{{/variable}} blocks
                    const blockRegex = new RegExp(`\\{\\{#${variableName}\\}\\}(.*?)\\{\\{\\/${variableName}\\}\\}`, 'gs');
                    processed = processed.replace(blockRegex, (_, blockContent) => {
                        return value.map((item, index) => blockContent.trim().replace(/\.\.\./g, `${index + 1}. ${item}`)).join('\n');
                    });
                }
                else if (value.length > 0 && typeof value[0] === 'object') {
                    // Structured list - format as table or list
                    const blockRegex = new RegExp(`\\{\\{#${variableName}\\}\\}(.*?)\\{\\{\\/${variableName}\\}\\}`, 'gs');
                    processed = processed.replace(blockRegex, (_, blockContent) => {
                        return value.map(item => {
                            let formattedItem = blockContent.trim();
                            // Replace any field references in the block
                            for (const [fieldName, fieldValue] of Object.entries(item)) {
                                formattedItem = formattedItem.replace(new RegExp(`\\{\\{${fieldName}\\}\\}`, 'g'), String(fieldValue || ''));
                            }
                            return formattedItem.replace(/\.\.\./g, this.formatStructuredItem(item));
                        }).join('\n');
                    });
                    // Also handle simple variable replacement with formatted list
                    processed = processed.replace(new RegExp(`\\{\\{${variableName}\\}\\}`, 'g'), value.map(item => this.formatStructuredItem(item)).join('\n'));
                }
                // Handle empty arrays
                if (value.length === 0) {
                    processed = processed.replace(new RegExp(`\\{\\{${variableName}\\}\\}`, 'g'), '');
                    const blockRegex = new RegExp(`\\{\\{#${variableName}\\}\\}(.*?)\\{\\{\\/${variableName}\\}\\}`, 'gs');
                    processed = processed.replace(blockRegex, '');
                }
            }
            else if (typeof value === 'object') {
                // Handle object types - convert to JSON or formatted string
                processed = processed.replace(new RegExp(`\\{\\{${variableName}\\}\\}`, 'g'), this.formatStructuredItem(value));
            }
            else {
                // Handle other types (numbers, booleans)
                processed = processed.replace(new RegExp(`\\{\\{${variableName}\\}\\}`, 'g'), String(value));
            }
        }
        return processed;
    }
    /**
     * Format structured item (object) for display in prompts
     */
    formatStructuredItem(item) {
        // Common structured formats
        if ('name' in item && 'price' in item) {
            // Product-like format
            const parts = [];
            if (item.name)
                parts.push(`Product: ${item.name}`);
            if (item.price)
                parts.push(`Price: ${item.price}`);
            if (item.link)
                parts.push(`Link: ${item.link}`);
            if (item.description)
                parts.push(`Description: ${item.description}`);
            return parts.join(' | ');
        }
        if ('name' in item && 'link' in item) {
            // Resource-like format
            return `${item.name}${item.link ? ` - ${item.link}` : ''}`;
        }
        // Generic object format
        return Object.entries(item)
            .filter(([_, value]) => value !== null && value !== undefined && value !== '')
            .map(([key, value]) => `${key}: ${value}`)
            .join(' | ');
    }
    /**
     * Validate that required prompts are present
     */
    validateConfig(config) {
        if (!config.botStylePrompt?.trim()) {
            throw new Error('BotStyle prompt is required for AI response generation');
        }
        if (config.maxConversationMessages < 1 || config.maxConversationMessages > 1000) {
            throw new Error('Max conversation messages must be between 1 and 1000');
        }
    }
}
