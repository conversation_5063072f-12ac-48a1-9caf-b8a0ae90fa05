export class ProfileAnalyzer {
    config;
    constructor(config) {
        this.config = config;
    }
    /**
     * Analyze Instagram profile and generate insights
     */
    async analyzeProfile(profileData, customPrompt) {
        console.log(`🔍 Analyzing profile for @${profileData.username}`);
        try {
            // Build analysis prompt
            const analysisPrompt = this.buildAnalysisPrompt(profileData, customPrompt);
            // Use appropriate AI provider
            const response = await this.makeApiRequest(analysisPrompt);
            console.log(`✅ Profile analysis completed for @${profileData.username}`);
            return response;
        }
        catch (error) {
            console.error('❌ Failed to analyze profile:', error);
            throw error;
        }
    }
    /**
     * Make direct API call to the appropriate provider
     */
    async makeApiRequest(prompt) {
        const model = this.config.model;
        // Check if it's a Claude model
        if (model.includes('claude') && this.config.anthropicApiKey) {
            return this.makeAnthropicRequest(prompt);
        }
        else {
            // Fallback to OpenRouter for other models
            return this.makeOpenRouterRequest(prompt);
        }
    }
    /**
     * Make direct Anthropic API call
     */
    async makeAnthropicRequest(prompt) {
        const response = await fetch('https://api.anthropic.com/v1/messages', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.anthropicApiKey}`,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: this.config.model,
                max_tokens: 4000,
                messages: [
                    { role: 'user', content: prompt }
                ],
                temperature: this.config.temperature || 1.0
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Anthropic API error: ${response.status} ${response.statusText} - ${errorText}`);
        }
        const data = await response.json();
        if (!data.content?.[0]?.text) {
            throw new Error('Invalid response from Anthropic API');
        }
        return data.content[0].text;
    }
    /**
     * Make direct OpenRouter API call (fallback)
     */
    async makeOpenRouterRequest(prompt) {
        const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.apiKey}`,
                'Content-Type': 'application/json',
                'X-Title': 'Profile Analysis'
            },
            body: JSON.stringify({
                model: this.config.model,
                messages: [
                    { role: 'user', content: prompt }
                ],
                temperature: this.config.temperature || 1.0,
                top_p: this.config.topP || 1.0
            })
        });
        if (!response.ok) {
            throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.choices?.[0]?.message?.content) {
            throw new Error('Invalid response from OpenRouter API');
        }
        return data.choices[0].message.content;
    }
    /**
     * Build analysis prompt with profile data
     */
    buildAnalysisPrompt(profileData, customPrompt) {
        const mediaSummary = profileData.mediaData.map((media, index) => {
            return `Post ${index + 1}: ${media.media_type} - ${media.caption || 'No caption'}`;
        }).join('\n');
        return `${customPrompt}

PROFILE DATA:
Username: @${profileData.username}
Bio: ${profileData.bio || 'No bio available'}

RECENT POSTS (Last ${profileData.mediaData.length}):
${mediaSummary}`;
    }
}
