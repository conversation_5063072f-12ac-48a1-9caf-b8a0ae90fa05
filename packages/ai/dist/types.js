import { z } from 'zod';
// Instagram Stage enum - needs to match database
export const InstagramStageSchema = z.enum([
    'FIRSTCONTACT',
    'RAPPORT',
    'DISCOVERY',
    'DISSONANCE',
    'VISION',
    'PROPOSAL',
    'QUALIFIED',
    'FORMSENT',
    'CONVERTED',
    'DISQUALIFIED',
    'SUSPICIOUS'
]);
// Engagement Priority enum (excluding NEW_FOLLOWER as per requirements)
export const EngagementPrioritySchema = z.enum([
    'VERY_HIGH',
    'HIGH',
    'MEDIUM',
    'LOW',
    'NON_RESPONDED'
]);
// Main Instagram Response Schema for OpenRouter structured outputs
export const InstagramResponseSchema = z.object({
    instagramStage: InstagramStageSchema,
    engagementPriority: EngagementPrioritySchema,
    message1: z.string().nullable().optional(), // Optional - null if disqualified
    message2: z.string().nullable().optional(),
    message3: z.string().nullable().optional(),
    message4: z.string().nullable().optional(),
    notes: z.string(),
    followup1_text: z.string().nullable().optional(),
    followup1_scheduledAt: z.string().nullable().optional(), // ISO date string
    followup2_text: z.string().nullable().optional(),
    followup2_scheduledAt: z.string().nullable().optional(),
    followup3_text: z.string().nullable().optional(),
    followup3_scheduledAt: z.string().nullable().optional(),
    followup4_text: z.string().nullable().optional(),
    followup4_scheduledAt: z.string().nullable().optional(),
    followup5_text: z.string().nullable().optional(),
    followup5_scheduledAt: z.string().nullable().optional(),
    followup6_text: z.string().nullable().optional(),
    followup6_scheduledAt: z.string().nullable().optional(),
    followup7_text: z.string().nullable().optional(),
    followup7_scheduledAt: z.string().nullable().optional(),
    followup8_text: z.string().nullable().optional(),
    followup8_scheduledAt: z.string().nullable().optional(),
    isDisqualified: z.boolean(), // Determines if AI responds
    aiReasoning: z.string().nullable().optional() // AI reasoning content for debugging
});
// GLM-4.5 Model types
export const GLM_MODELS = [
    'glm-4.5',
    'glm-4.5-air',
    'glm-4.5-x',
    'glm-4.5-airx',
    'glm-4.5-flash',
    'glm-4.5v'
];
// Claude Model types
export const CLAUDE_MODELS = [
    'claude-sonnet-4-20250514',
    'claude-3-5-sonnet-20241022',
    'claude-3-5-haiku-20241022',
    'claude-3-opus-20240229',
    'claude-3-sonnet-20240229',
    'claude-3-haiku-20240307'
];
