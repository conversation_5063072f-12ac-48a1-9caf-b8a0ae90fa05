import { OpenRouterClient } from './openrouter-client';
import { ZA<PERSON>lient } from './zai-client';
import { AnthropicClient } from './anthropic-client';
import { PromptBuilder } from './prompt-builder';
import { GLM_MODELS, CLAUDE_MODELS } from './types';
export class InstagramAIService {
    openRouterClient = null;
    zaiClient = null;
    anthropicClient = null;
    promptBuilder;
    provider;
    constructor(config) {
        this.promptBuilder = new PromptBuilder();
        // Determine provider based on model name
        if (this.isClaudeModel(config.model)) {
            this.provider = 'anthropic';
            if (!config.anthropicApiKey) {
                throw new Error('Anthropic API key is required for Claude models');
            }
            this.anthropicClient = new AnthropicClient({
                apiKey: config.anthropicApiKey,
                model: config.model,
                enableThinking: config.enableThinking,
                cacheDuration: config.cacheDuration,
                temperature: config.temperature,
                topP: config.topP
            });
        }
        else if (this.isGLMModel(config.model)) {
            this.provider = 'zai';
            if (!config.zaiApiKey) {
                throw new Error('Z.AI API key is required for GLM models');
            }
            this.zaiClient = new ZAIClient({
                apiKey: config.zaiApiKey,
                model: config.model,
                enableReasoning: config.enableReasoning,
                reasoningMaxTokens: config.reasoningMaxTokens
            });
        }
        else {
            this.provider = 'openrouter';
            this.openRouterClient = new OpenRouterClient(config);
        }
    }
    isGLMModel(model) {
        return GLM_MODELS.includes(model);
    }
    isClaudeModel(model) {
        return CLAUDE_MODELS.includes(model);
    }
    /**
     * Generate AI response for Instagram conversation
     */
    async generateResponse(request, usageContext) {
        console.log(`🚀 Starting AI response generation for contact: ${request.contact.username} using ${this.provider.toUpperCase()}`);
        try {
            // Validate prompt configuration
            this.promptBuilder.validateConfig(request.promptConfig);
            // Build cached system prompt with dynamic bot style variables
            const systemPrompt = this.promptBuilder.buildSystemPrompt(request.promptConfig, request.botStyleVariables, request.calendlyIntegration);
            const dateTimePrompt = this.promptBuilder.buildDateTimePrompt();
            console.log('📝 Built system prompt with', systemPrompt.length, 'characters');
            console.log('📄 System Prompt Content:');
            console.log(systemPrompt);
            console.log('🕒 Date/Time Prompt:', dateTimePrompt);
            // Build structured messages for conversation
            const conversationMessages = this.promptBuilder.buildMessages(request.messages, request.contact, request.promptConfig.maxConversationMessages);
            console.log('💬 Built structured messages with', request.messages.length, 'original messages');
            console.log('💬 Structured Messages:', JSON.stringify(conversationMessages, null, 2));
            let response;
            if (this.provider === 'anthropic' && this.anthropicClient) {
                // Use Anthropic for Claude models
                const systemMessage = this.anthropicClient.createCachedSystemMessage(systemPrompt, dateTimePrompt);
                const anthropicMessages = this.anthropicClient.createConversationMessages(conversationMessages);
                const anthropicRequest = this.anthropicClient.buildRequest(systemMessage, anthropicMessages, request.aiConfig, request.calendlyIntegration);
                console.log('🤖 Sending request to Claude with model:', request.aiConfig.model);
                response = await this.anthropicClient.generateResponse(anthropicRequest, usageContext, request.calendlyIntegration);
            }
            else if (this.provider === 'zai' && this.zaiClient) {
                // Use Z.AI for GLM models
                const systemMessage = this.zaiClient.createCachedSystemMessage(systemPrompt, dateTimePrompt);
                const zaiMessages = this.zaiClient.createConversationMessages(conversationMessages);
                const zaiRequest = this.zaiClient.buildRequest(systemMessage, zaiMessages, request.aiConfig);
                console.log('🤖 Sending request to Z.AI GLM-4.5 with model:', request.aiConfig.model);
                response = await this.zaiClient.generateResponse(zaiRequest, usageContext);
            }
            else if (this.provider === 'openrouter' && this.openRouterClient) {
                // Use OpenRouter for non-GLM models
                const systemMessage = this.openRouterClient.createCachedSystemMessage(systemPrompt, dateTimePrompt);
                const openRouterMessages = this.openRouterClient.createConversationMessages(conversationMessages);
                const openRouterRequest = this.openRouterClient.buildRequest(systemMessage, openRouterMessages, request.aiConfig);
                console.log('🤖 Sending request to OpenRouter with model:', request.aiConfig.model);
                response = await this.openRouterClient.generateResponse(openRouterRequest, usageContext);
            }
            else {
                throw new Error(`Invalid provider configuration: ${this.provider}`);
            }
            console.log('✅ Successfully generated AI response:', {
                provider: this.provider,
                stage: response.instagramStage,
                priority: response.engagementPriority,
                hasMessages: !!(response.message1),
                isDisqualified: response.isDisqualified
            });
            return response;
        }
        catch (error) {
            console.error('❌ Failed to generate AI response:', error);
            throw error;
        }
    }
    /**
     * Check if contact should wait for response delay
     * Based on organization settings (30-40s configurable)
     */
    calculateResponseDelay(minSeconds, maxSeconds) {
        const delayMs = (Math.random() * (maxSeconds - minSeconds) + minSeconds) * 1000;
        console.log(`⏰ Response delay: ${Math.round(delayMs / 1000)}s`);
        return delayMs;
    }
    /**
     * Calculate random delay between messages (15-40s)
     */
    calculateMessageDelay() {
        const delayMs = (Math.random() * (40 - 15) + 15) * 1000;
        console.log(`📤 Message delay: ${Math.round(delayMs / 1000)}s`);
        return delayMs;
    }
    /**
     * Extract messages to send immediately (non-empty messages)
     */
    extractImmediateMessages(response) {
        const messages = [];
        if (response.message1?.trim())
            messages.push(response.message1);
        if (response.message2?.trim())
            messages.push(response.message2);
        if (response.message3?.trim())
            messages.push(response.message3);
        if (response.message4?.trim())
            messages.push(response.message4);
        console.log(`📨 Extracted ${messages.length} immediate messages`);
        return messages;
    }
    /**
     * Extract scheduled follow-ups with dates
     */
    extractFollowups(response) {
        const followups = [];
        const pairs = [
            { text: response.followup1_text, date: response.followup1_scheduledAt },
            { text: response.followup2_text, date: response.followup2_scheduledAt },
            { text: response.followup3_text, date: response.followup3_scheduledAt },
            { text: response.followup4_text, date: response.followup4_scheduledAt },
            { text: response.followup5_text, date: response.followup5_scheduledAt },
            { text: response.followup6_text, date: response.followup6_scheduledAt }
        ];
        for (const pair of pairs) {
            if (pair.text?.trim() && pair.date) {
                try {
                    const scheduledAt = new Date(pair.date);
                    if (!isNaN(scheduledAt.getTime())) {
                        followups.push({ text: pair.text, scheduledAt });
                    }
                }
                catch (error) {
                    console.warn('⚠️ Invalid followup date:', pair.date);
                }
            }
        }
        console.log(`📅 Extracted ${followups.length} scheduled follow-ups`);
        return followups;
    }
}
