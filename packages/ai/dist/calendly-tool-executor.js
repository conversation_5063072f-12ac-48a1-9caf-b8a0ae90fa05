/**
 * <PERSON>ndly Tool Executor - Handles actual Calendly API calls for Claude tools
 */
const logger = {
    info: (message, ...args) => console.log(`[CALENDLY-TOOL-EXECUTOR] ${message}`, ...args),
    warn: (message, ...args) => console.warn(`[CALENDLY-TOOL-EXECUTOR] ${message}`, ...args),
    error: (message, ...args) => console.error(`[CALENDLY-TOOL-EXECUTOR] ${message}`, ...args)
};
export class CalendlyToolExecutor {
    /**
     * Execute a Calendly tool call
     */
    async execute(toolName, params, apiToken) {
        try {
            logger.info(`🔧 Executing Calendly tool: ${toolName}`, params);
            switch (toolName) {
                case 'check_calendly_booking':
                    return await this.checkBooking(params.email, apiToken);
                case 'get_available_slots':
                    return await this.getAvailableSlots(params.startDate, params.endDate, apiToken);
                case 'reschedule_booking':
                    return await this.rescheduleBooking(params.email, apiToken);
                default:
                    throw new Error(`Unknown Calendly tool: ${toolName}`);
            }
        }
        catch (error) {
            logger.error(`❌ Error executing tool ${toolName}:`, error);
            return `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`;
        }
    }
    /**
     * Check if a person has scheduled a meeting by email
     */
    async checkBooking(email, apiToken) {
        try {
            logger.info(`📅 Checking booking for email: ${email}`);
            logger.info(`🔑 Using API token (first 20 chars): ${apiToken.substring(0, 20)}...`);
            // First, get the current user to get their organization URI
            const userResponse = await fetch('https://api.calendly.com/users/me', {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json',
                },
            });
            if (!userResponse.ok) {
                throw new Error(`Failed to get user info: ${userResponse.status} ${userResponse.statusText}`);
            }
            const userData = await userResponse.json();
            const organizationUri = userData.resource.current_organization;
            logger.info(`🏢 Using organization URI: ${organizationUri}`);
            const url = `https://api.calendly.com/scheduled_events?invitee_email=${encodeURIComponent(email)}&status=active&organization=${encodeURIComponent(organizationUri)}`;
            logger.info(`🌐 API URL: ${url}`);
            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json',
                },
            });
            logger.info(`📡 Calendly API response: ${response.status} ${response.statusText}`);
            if (!response.ok) {
                const errorBody = await response.text();
                logger.error(`❌ Calendly API error body:`, errorBody);
                throw new Error(`Calendly API error: ${response.status} ${response.statusText} - ${errorBody}`);
            }
            const data = await response.json();
            const events = data.collection || [];
            // Filter for upcoming events only
            const now = new Date();
            const upcomingEvents = events.filter((event) => {
                const startTime = new Date(event.start_time);
                return startTime > now;
            });
            if (upcomingEvents.length === 0) {
                logger.info(`📭 No upcoming bookings found for ${email}`);
                return `No upcoming bookings found for ${email}. They have not scheduled a meeting yet.`;
            }
            const event = upcomingEvents[0];
            const eventDate = new Date(event.start_time);
            const formattedDate = eventDate.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                timeZoneName: 'short'
            });
            logger.info(`✅ Found booking for ${email}: ${event.name} on ${formattedDate}`);
            return `Yes, ${email} has scheduled a meeting: "${event.name}" on ${formattedDate}. Status: ${event.status}`;
        }
        catch (error) {
            logger.error(`❌ Error checking booking for ${email}:`, error);
            throw error;
        }
    }
    /**
     * Get available time slots for scheduling
     */
    async getAvailableSlots(startDate, endDate, apiToken) {
        try {
            if (!apiToken) {
                throw new Error('API token is required for getting available slots');
            }
            logger.info(`📅 Getting available slots from ${startDate || 'today'} to ${endDate || 'next week'}`);
            // For now, return a simple message since this requires specific event type URIs
            // which would need to be configured in the Calendly integration
            return `To see available time slots, please use the calendar link that was shared with you. The calendar will show all available times for booking.`;
        }
        catch (error) {
            logger.error(`❌ Error getting available slots:`, error);
            throw error;
        }
    }
    /**
     * Help reschedule an existing booking
     */
    async rescheduleBooking(email, apiToken) {
        try {
            logger.info(`📅 Getting reschedule info for email: ${email}`);
            // First, get the current user to get their organization URI
            const userResponse = await fetch('https://api.calendly.com/users/me', {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json',
                },
            });
            if (!userResponse.ok) {
                throw new Error(`Failed to get user info: ${userResponse.status} ${userResponse.statusText}`);
            }
            const userData = await userResponse.json();
            const organizationUri = userData.resource.current_organization;
            // First, find their existing booking
            const response = await fetch(`https://api.calendly.com/scheduled_events?invitee_email=${encodeURIComponent(email)}&status=active&organization=${encodeURIComponent(organizationUri)}`, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json',
                },
            });
            if (!response.ok) {
                throw new Error(`Calendly API error: ${response.status} ${response.statusText}`);
            }
            const data = await response.json();
            const events = data.collection || [];
            // Get current time for filtering upcoming events
            const now = new Date();
            const upcomingEvents = events.filter((event) => {
                const startTime = new Date(event.start_time);
                return startTime > now;
            });
            if (upcomingEvents.length === 0) {
                return `No upcoming bookings found for ${email}. They need to schedule a meeting first.`;
            }
            // Get invitee details for the reschedule URL
            const event = upcomingEvents[0];
            const inviteesResponse = await fetch(`${event.uri}/invitees`, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json',
                },
            });
            if (!inviteesResponse.ok) {
                throw new Error(`Failed to get invitee details: ${inviteesResponse.status}`);
            }
            const inviteesData = await inviteesResponse.json();
            const matchingInvitee = inviteesData.collection.find((invitee) => invitee.email.toLowerCase() === email.toLowerCase());
            if (!matchingInvitee || !matchingInvitee.reschedule_url) {
                return `Found booking for ${email} but unable to get reschedule link. Please contact support.`;
            }
            const eventDate = new Date(event.start_time);
            const formattedDate = eventDate.toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric',
                hour: 'numeric',
                minute: '2-digit'
            });
            return `Found your upcoming meeting "${event.name}" scheduled for ${formattedDate}. You can reschedule it using this link: ${matchingInvitee.reschedule_url}`;
        }
        catch (error) {
            logger.error(`❌ Error getting reschedule info for ${email}:`, error);
            throw error;
        }
    }
}
