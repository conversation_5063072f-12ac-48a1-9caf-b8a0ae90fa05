import { ForbiddenError } from '@workspace/common/errors';
import { InstagramResponseSchema } from './types';
import { UsageService } from './usage-service';
import { cleanTimestampPrefixes } from './utils';
export class OpenRouterClient {
    apiKey;
    baseUrl = 'https://openrouter.ai/api/v1';
    usageService;
    constructor(config) {
        this.apiKey = config.apiKey;
        this.usageService = new UsageService();
    }
    async generateResponse(request, usageContext) {
        try {
            console.log('🤖 Generating AI response with OpenRouter');
            // Log full request details
            console.log('📤 OpenRouter Request:', {
                model: request.model,
                temperature: request.temperature,
                topP: request.top_p,
                reasoning: request.reasoning?.enabled,
                messageCount: request.messages.length,
                systemPromptLength: request.messages[0]?.content?.length || 0,
                userMessageLength: request.messages[1]?.content?.length || 0
            });
            // Log full request body for debugging
            console.log('📤 Full Request Body:', JSON.stringify(request, null, 2));
            const response = await fetch(`${this.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'https://localhost:3000',
                    'X-Title': 'AIChromaPRO Instagram AI'
                },
                body: JSON.stringify(request)
            });
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('❌ OpenRouter API error:', {
                    status: response.status,
                    statusText: response.statusText,
                    error: errorData
                });
                if (response.status === 401) {
                    throw new ForbiddenError('Invalid OpenRouter API key');
                }
                throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`);
            }
            const data = await response.json();
            // Log full response details
            console.log('📥 OpenRouter Response:', {
                choices: data.choices?.length || 0,
                finishReason: data.choices?.[0]?.finish_reason,
                model: data.model,
                hasContent: !!data.choices?.[0]?.message?.content,
                contentLength: data.choices?.[0]?.message?.content?.length || 0,
                hasReasoning: !!data.choices?.[0]?.message?.reasoning
            });
            if (!data.choices?.[0]?.message?.content) {
                console.error('❌ No response content from OpenRouter API. Full response:', data);
                throw new Error('No response content from OpenRouter API');
            }
            // Log and persist usage if available
            if (data.usage) {
                console.log('💰 OpenRouter usage:', {
                    totalTokens: data.usage.total_tokens,
                    tokenBreakdown: {
                        IN: data.usage.prompt_tokens,
                        CACHED: data.usage.prompt_tokens_details?.cached_tokens || 0,
                        OUT: data.usage.completion_tokens,
                        REASONING: data.usage.completion_tokens_details?.reasoning_tokens || 0
                    },
                    cost: data.usage.cost ? `${data.usage.cost} credits` : 'N/A',
                    upstreamCost: data.usage.cost_details?.upstream_inference_cost
                });
                // Persist usage data to database if context provided
                if (usageContext) {
                    await this.usageService.recordOpenRouterUsage(usageContext, data);
                }
            }
            // Log and store reasoning if available
            const reasoningContent = data.choices[0].message.reasoning;
            if (reasoningContent) {
                console.log('🧠 AI Reasoning:', reasoningContent);
            }
            // Log raw response content for debugging
            console.log('📄 Raw Response Content:', data.choices[0].message.content);
            // Parse and validate the structured response
            let responseContent = data.choices[0].message.content;
            // Clean up response content - extract JSON from various formats
            responseContent = this.extractCleanJSON(responseContent);
            let parsedResponse;
            try {
                parsedResponse = JSON.parse(responseContent);
                // Clean timestamp prefixes from messages
                parsedResponse = cleanTimestampPrefixes(parsedResponse);
            }
            catch (parseError) {
                console.error('❌ Failed to parse AI response as JSON:');
                console.error('📄 Raw Content (first 1000 chars):', responseContent.substring(0, 1000));
                console.error('📄 Raw Content (FULL - NO TRUNCATION):');
                console.error('='.repeat(80));
                console.error(responseContent);
                console.error('='.repeat(80));
                console.error('🔍 Parse Error:', parseError);
                console.error('📏 Content Length:', responseContent.length);
                console.error('🔡 Content Type Check:', typeof responseContent);
                // Check for common truncation indicators
                const isLikelyTruncated = !responseContent.trim().endsWith('}') ||
                    !responseContent.trim().endsWith('"') && responseContent.includes('followup') ||
                    responseContent.includes(',"') && !responseContent.trim().endsWith('}');
                if (isLikelyTruncated) {
                    console.error('🔥 LIKELY TRUNCATED RESPONSE - Response appears incomplete.');
                }
                throw new Error(`AI response is not valid JSON. Length: ${responseContent.length}, Type: ${typeof responseContent}${isLikelyTruncated ? ' - LIKELY TRUNCATED' : ''}`);
            }
            // Log parsed response
            console.log('🔍 Parsed JSON Response:', parsedResponse);
            // Validate against schema
            const validationResult = InstagramResponseSchema.safeParse(parsedResponse);
            if (!validationResult.success) {
                console.error('❌ AI response validation failed:', {
                    errors: validationResult.error.errors,
                    response: parsedResponse
                });
                throw new Error('AI response does not match expected schema');
            }
            // Add reasoning to the response
            const finalResponse = {
                ...validationResult.data,
                aiReasoning: reasoningContent
            };
            // Log final validated response
            console.log('✅ Generated valid AI response:', {
                stage: finalResponse.instagramStage,
                priority: finalResponse.engagementPriority,
                isDisqualified: finalResponse.isDisqualified,
                hasMessage1: !!finalResponse.message1,
                hasMessage2: !!finalResponse.message2,
                hasMessage3: !!finalResponse.message3,
                hasMessage4: !!finalResponse.message4,
                notesLength: finalResponse.notes?.length || 0,
                hasReasoning: !!finalResponse.aiReasoning,
                reasoningLength: finalResponse.aiReasoning?.length || 0,
                followupsCount: [
                    finalResponse.followup1_text,
                    finalResponse.followup2_text,
                    finalResponse.followup3_text,
                    finalResponse.followup4_text,
                    finalResponse.followup5_text,
                    finalResponse.followup6_text
                ].filter(Boolean).length
            });
            return finalResponse;
        }
        catch (error) {
            console.error('❌ Error in OpenRouter client:', error);
            throw error;
        }
    }
    /**
     * Create cached system message with XML prompt
     * Entire system prompt gets cached as per requirements
     */
    createCachedSystemMessage(xmlPrompt, dateTimePrompt) {
        return {
            role: 'system',
            content: [
                {
                    type: 'text',
                    text: xmlPrompt,
                    cache_control: {
                        type: 'ephemeral'
                    }
                },
                {
                    type: 'text',
                    text: dateTimePrompt
                    // No cache_control here - this content changes constantly
                }
            ]
        };
    }
    /**
     * Create structured conversation messages
     */
    createConversationMessages(structuredMessages) {
        return structuredMessages;
    }
    /**
     * Build OpenRouter request with structured outputs and reasoning
     */
    /**
     * Extract clean JSON from AI response, handling thinking blocks and various formats
     */
    extractCleanJSON(responseContent) {
        let cleanContent = responseContent.trim();
        // Remove <THINK>...</THINK> blocks (case insensitive)
        cleanContent = cleanContent.replace(/<THINK>[\s\S]*?<\/THINK>/gi, '');
        // Remove markdown code blocks if present
        if (cleanContent.startsWith('```json')) {
            cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        }
        else if (cleanContent.startsWith('```')) {
            cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }
        // Clean up any remaining whitespace
        cleanContent = cleanContent.trim();
        // Try to find JSON object boundaries if response contains extra text
        const jsonStart = cleanContent.indexOf('{');
        const jsonEnd = cleanContent.lastIndexOf('}');
        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
            cleanContent = cleanContent.substring(jsonStart, jsonEnd + 1);
        }
        return cleanContent;
    }
    buildRequest(systemMessage, conversationMessages, config) {
        // Add JSON prefill to force JSON response format
        const messagesWithPrefill = [
            systemMessage,
            ...conversationMessages,
            { role: 'assistant', content: [{ type: 'text', text: '{' }] }
        ];
        const request = {
            model: config.model,
            messages: messagesWithPrefill,
            temperature: config.temperature,
            top_p: config.topP,
            usage: {
                include: true
            }
        };
        // Always add reasoning configuration using OpenRouter's standardized format
        request.reasoning = {};
        if (config.enableReasoning) {
            if (config.reasoningMaxTokens) {
                // Use specific token limit (Anthropic-style)
                request.reasoning.max_tokens = config.reasoningMaxTokens;
            }
            else {
                // Use effort level (OpenAI-style) - default to "medium"
                request.reasoning.effort = "medium";
            }
            // Enable reasoning and don't exclude from response
            request.reasoning.enabled = true;
            request.reasoning.exclude = false;
        }
        else {
            // Explicitly disable reasoning when not enabled
            request.reasoning.enabled = false;
            request.reasoning.exclude = true;
        }
        return request;
    }
}
