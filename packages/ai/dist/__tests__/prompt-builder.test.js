import { describe, it, expect } from 'vitest';
import { PromptBuilder } from '../prompt-builder';
describe('PromptBuilder - Dynamic Variable Processing', () => {
    const promptBuilder = new PromptBuilder();
    describe('processBotStyleVariables', () => {
        it('should process simple text variables', () => {
            const template = 'Hello, I am {{agent_name}} from {{company_name}}. How can I help you today?';
            const variables = {
                agent_name: '<PERSON>',
                company_name: 'TechCorp Inc'
            };
            const config = {
                botStylePrompt: template,
                maxConversationMessages: 10,
                conversationPersona: 'friendly',
                botStyleId: 'test-bot-style'
            };
            const result = promptBuilder.buildSystemPrompt(config, null, variables);
            expect(result).toBe('Hello, I am Sarah from TechCorp Inc. How can I help you today?');
        });
        it('should process dropdown/select variables', () => {
            const template = 'Our business operates in the {{industry}} sector, specifically focused on {{business_type}}.';
            const variables = {
                industry: 'Technology',
                business_type: 'SaaS'
            };
            const config = {
                botStylePrompt: template,
                maxConversationMessages: 10,
                conversationPersona: 'professional',
                botStyleId: 'test-bot-style'
            };
            const result = promptBuilder.buildSystemPrompt(config, null, variables);
            expect(result).toBe('Our business operates in the Technology sector, specifically focused on SaaS.');
        });
        it('should process array variables with block syntax', () => {
            const template = `Our services include:
{{#services}}
...
{{/services}}`;
            const variables = {
                services: ['Web Development', 'Mobile Apps', 'Cloud Solutions', 'AI Integration']
            };
            const config = {
                botStylePrompt: template,
                maxConversationMessages: 10,
                conversationPersona: 'professional',
                botStyleId: 'test-bot-style'
            };
            const result = promptBuilder.buildSystemPrompt(config, null, variables);
            expect(result).toBe(`Our services include:
1. Web Development
2. Mobile Apps
3. Cloud Solutions
4. AI Integration`);
        });
        it('should process structured list variables (products)', () => {
            const template = `Our featured products:
{{#product_list}}
{{name}} - {{price}} - {{description}}
{{/product_list}}`;
            const variables = {
                product_list: [
                    {
                        name: 'Premium Plan',
                        price: '$99/month',
                        link: 'https://example.com/premium',
                        description: 'All features included'
                    },
                    {
                        name: 'Basic Plan',
                        price: '$29/month',
                        link: 'https://example.com/basic',
                        description: 'Essential features only'
                    }
                ]
            };
            const config = {
                botStylePrompt: template,
                maxConversationMessages: 10,
                conversationPersona: 'sales',
                botStyleId: 'test-bot-style'
            };
            const result = promptBuilder.buildSystemPrompt(config, null, variables);
            expect(result).toBe(`Our featured products:
Premium Plan - $99/month - All features included
Basic Plan - $29/month - Essential features only`);
        });
        it('should handle structured list with direct variable replacement', () => {
            const template = 'Check out our products: {{product_catalog}}';
            const variables = {
                product_catalog: [
                    {
                        name: 'Widget Pro',
                        price: '$199',
                        link: 'https://store.com/widget-pro',
                        description: 'Professional widget solution'
                    },
                    {
                        name: 'Widget Lite',
                        price: '$49',
                        link: 'https://store.com/widget-lite',
                        description: 'Lightweight widget for beginners'
                    }
                ]
            };
            const config = {
                botStylePrompt: template,
                maxConversationMessages: 10,
                conversationPersona: 'sales',
                botStyleId: 'test-bot-style'
            };
            const result = promptBuilder.buildSystemPrompt(config, null, variables);
            expect(result).toBe(`Check out our products: Product: Widget Pro | Price: $199 | Link: https://store.com/widget-pro | Description: Professional widget solution
Product: Widget Lite | Price: $49 | Link: https://store.com/widget-lite | Description: Lightweight widget for beginners`);
        });
        it('should handle complex nested template with multiple variable types', () => {
            const template = `You are {{agent_name}}, a {{role}} at {{company_name}}.

Company Description: {{company_description}}

Our main goals:
{{#business_goals}}
...
{{/business_goals}}

Products we offer:
{{#products}}
- {{name}}: {{price}} ({{description}})
{{/products}}

Contact us at: {{contact_email}}
Business hours: {{business_hours}}`;
            const variables = {
                agent_name: 'Alex',
                role: 'Customer Success Manager',
                company_name: 'InnovateTech',
                company_description: 'We provide cutting-edge AI solutions for businesses',
                business_goals: [
                    'Deliver exceptional customer service',
                    'Innovate with AI technology',
                    'Build long-term partnerships'
                ],
                products: [
                    {
                        name: 'AI Assistant Pro',
                        price: '$299/month',
                        description: 'Advanced AI automation'
                    },
                    {
                        name: 'AI Assistant Basic',
                        price: '$99/month',
                        description: 'Essential AI features'
                    }
                ],
                contact_email: '<EMAIL>',
                business_hours: '9 AM - 6 PM EST'
            };
            const config = {
                botStylePrompt: template,
                maxConversationMessages: 10,
                conversationPersona: 'professional',
                botStyleId: 'test-bot-style'
            };
            const result = promptBuilder.buildSystemPrompt(config, null, variables);
            expect(result).toContain('You are Alex, a Customer Success Manager at InnovateTech.');
            expect(result).toContain('Company Description: We provide cutting-edge AI solutions for businesses');
            expect(result).toContain('1. Deliver exceptional customer service');
            expect(result).toContain('2. Innovate with AI technology');
            expect(result).toContain('3. Build long-term partnerships');
            expect(result).toContain('- AI Assistant Pro: $299/month (Advanced AI automation)');
            expect(result).toContain('- AI Assistant Basic: $99/month (Essential AI features)');
            expect(result).toContain('Contact us at: <EMAIL>');
            expect(result).toContain('Business hours: 9 AM - 6 PM EST');
        });
        it('should handle missing/null variables gracefully', () => {
            const template = 'Welcome to {{company_name}}! Our slogan: {{slogan}}. Founded in {{year}}.';
            const variables = {
                company_name: 'TechCorp',
                slogan: null,
                // year is missing
            };
            const config = {
                botStylePrompt: template,
                maxConversationMessages: 10,
                conversationPersona: 'friendly',
                botStyleId: 'test-bot-style'
            };
            const result = promptBuilder.buildSystemPrompt(config, null, variables);
            expect(result).toBe('Welcome to TechCorp! Our slogan: . Founded in {{year}}.');
        });
        it('should handle empty arrays', () => {
            const template = `Available offers:
{{#special_offers}}
- {{name}}: {{discount}}
{{/special_offers}}
End of offers.`;
            const variables = {
                special_offers: []
            };
            const config = {
                botStylePrompt: template,
                maxConversationMessages: 10,
                conversationPersona: 'sales',
                botStyleId: 'test-bot-style'
            };
            const result = promptBuilder.buildSystemPrompt(config, null, variables);
            expect(result).toBe(`Available offers:

End of offers.`);
        });
        it('should process grouped variables from different groups', () => {
            // Simulating variables from different groups like "Business Info", "Products", "Contact"
            const template = `{{business_name}} - {{tagline}}

Products:
{{#product_catalog}}
* {{name}} at {{price}}
{{/product_catalog}}

Contact Info:
Email: {{contact_email}}
Phone: {{contact_phone}}
Address: {{contact_address}}`;
            const variables = {
                // Business Info group
                business_name: 'Global Innovations Ltd',
                tagline: 'Innovation at its finest',
                // Products group
                product_catalog: [
                    { name: 'Enterprise Suite', price: '$5000/year' },
                    { name: 'Starter Pack', price: '$500/year' }
                ],
                // Contact group
                contact_email: '<EMAIL>',
                contact_phone: '******-0123',
                contact_address: '123 Innovation Drive, Tech City, TC 12345'
            };
            const config = {
                botStylePrompt: template,
                maxConversationMessages: 10,
                conversationPersona: 'professional',
                botStyleId: 'test-bot-style'
            };
            const result = promptBuilder.buildSystemPrompt(config, null, variables);
            expect(result).toBe(`Global Innovations Ltd - Innovation at its finest

Products:
* Enterprise Suite at $5000/year
* Starter Pack at $500/year

Contact Info:
Email: <EMAIL>
Phone: ******-0123
Address: 123 Innovation Drive, Tech City, TC 12345`);
        });
        it('should fallback to legacy organization prompts when no bot style variables provided', () => {
            const template = 'Hello, I am {{persona_name}} from {{business_name}}.';
            const organizationPrompts = {
                businessName: 'LegacyCorp',
                personaName: 'John',
                personaTitle: 'CEO',
                niche: 'Finance',
                language: 'English',
                problems: null,
                goals: null,
                funnelGoalName: null,
                funnelPrice: null,
                funnelLink: null,
                funnelDescription: null,
                resources: null,
                socialProofLink: null,
                additionalInfo: null
            };
            const config = {
                botStylePrompt: template,
                maxConversationMessages: 10,
                conversationPersona: 'professional',
                botStyleId: 'test-bot-style'
            };
            const result = promptBuilder.buildSystemPrompt(config, organizationPrompts, undefined);
            expect(result).toBe('Hello, I am John from LegacyCorp.');
        });
    });
    describe('Integration with conversation messages', () => {
        it('should build complete prompt with system prompt and messages', () => {
            const template = 'You are {{agent_name}} from {{company}}. Always be helpful.';
            const variables = {
                agent_name: 'Assistant',
                company: 'HelpDesk Pro'
            };
            const config = {
                botStylePrompt: template,
                maxConversationMessages: 5,
                conversationPersona: 'friendly',
                botStyleId: 'test-bot-style'
            };
            const systemPrompt = promptBuilder.buildSystemPrompt(config, null, variables);
            expect(systemPrompt).toBe('You are Assistant from HelpDesk Pro. Always be helpful.');
            // Also test date/time prompt generation
            const dateTimePrompt = promptBuilder.buildDateTimePrompt();
            expect(dateTimePrompt).toMatch(/^Date now: \d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/);
        });
    });
});
