import Anthropic from '@anthropic-ai/sdk';
import { ForbiddenError } from '@workspace/common/errors';
import { getCalendlyToolDefinitions } from './calendly-tools';
import { InstagramResponseSchema } from './types';
import { UsageService } from './usage-service';
import { CalendlyToolExecutor } from './calendly-tool-executor';
import { cleanTimestampPrefixes } from './utils';
export class AnthropicClient {
    client;
    config;
    usageService;
    calendlyExecutor;
    constructor(config) {
        this.config = config;
        this.client = new Anthropic({
            apiKey: config.apiKey,
        });
        this.usageService = new UsageService();
        this.calendlyExecutor = new CalendlyToolExecutor();
    }
    async generateResponse(request, usageContext, calendlyIntegration) {
        // Try up to 2 times: initial attempt + 1 retry for edge cases
        for (let attempt = 1; attempt <= 2; attempt++) {
            try {
                console.log(`🤖 Generating AI response with <PERSON> (attempt ${attempt}/2)`);
                // Log full request details
                console.log('📤 Claude Request:', {
                    model: request.model,
                    temperature: request.temperature,
                    topP: request.top_p,
                    thinking: request.thinking?.enabled,
                    messageCount: request.messages.length,
                    maxTokens: request.max_tokens,
                    systemPromptLength: request.messages[0]?.content?.length || 0,
                    userMessageLength: request.messages[1]?.content?.length || 0,
                    attempt
                });
                // Convert our request to Anthropic SDK format
                const anthropicParams = this.buildAnthropicParams(request);
                // Log full request body for debugging
                console.log('📤 Full Claude Request Body:', JSON.stringify(anthropicParams, null, 2));
                let response = await this.client.messages.create(anthropicParams);
                // Check if Claude wants to use tools
                const toolUseBlock = response.content.find((block) => block.type === 'tool_use');
                if (toolUseBlock && calendlyIntegration?.enabled) {
                    console.log('🔧 Claude wants to use tool:', toolUseBlock.name);
                    response = await this.handleToolUse(response, anthropicParams, calendlyIntegration.apiToken);
                }
                // Log final response details
                console.log('📥 Claude Response:', {
                    id: response.id,
                    model: response.model,
                    contentLength: response.content[0]?.type === 'text' ? response.content[0].text?.length || 0 : 0,
                    stopReason: response.stop_reason
                });
                const textContent = response.content.find((block) => block.type === 'text');
                if (!textContent || textContent.type !== 'text') {
                    console.error('❌ No text content from Claude API. Full response:', response);
                    // Check if this is a tool execution issue
                    const hasToolUse = response.content.some((block) => block.type === 'tool_use');
                    if (hasToolUse) {
                        throw new Error('AI assistant used tools but did not provide final response after maximum attempts');
                    }
                    throw new Error('No text content from Claude API');
                }
                // Log and persist usage if available
                if (response.usage) {
                    console.log('💰 Claude usage:', {
                        totalTokens: response.usage.input_tokens + response.usage.output_tokens,
                        tokenBreakdown: {
                            IN: response.usage.input_tokens,
                            CACHED: response.usage.cache_read_input_tokens || 0,
                            CACHE_CREATION: response.usage.cache_creation_input_tokens || 0,
                            OUT: response.usage.output_tokens
                        }
                    });
                    // Persist usage data to database if context provided
                    if (usageContext) {
                        await this.usageService.recordAnthropicUsage(usageContext, response, this.config.cacheDuration);
                    }
                }
                // Log raw response content for debugging
                console.log('📄 Raw Response Content:', textContent.text);
                // Parse and validate the structured response
                let responseContent = textContent.text;
                // Clean up response content - extract JSON from various formats
                responseContent = this.extractCleanJSON(responseContent);
                let parsedResponse;
                try {
                    parsedResponse = JSON.parse(responseContent);
                }
                catch (parseError) {
                    console.error('❌ Failed to parse Claude response as JSON:');
                    console.error('📄 Raw Content (first 1000 chars):', responseContent.substring(0, 1000));
                    console.error('📄 Raw Content (FULL - NO TRUNCATION):');
                    console.error('='.repeat(80));
                    console.error(responseContent);
                    console.error('='.repeat(80));
                    console.error('🔍 Parse Error:', parseError);
                    console.error('📏 Content Length:', responseContent.length);
                    console.error('🔡 Content Type Check:', typeof responseContent);
                    // Check for common truncation indicators
                    const isLikelyTruncated = !responseContent.trim().endsWith('}') ||
                        !responseContent.trim().endsWith('"') && responseContent.includes('followup') ||
                        responseContent.includes(',"') && !responseContent.trim().endsWith('}');
                    if (isLikelyTruncated) {
                        console.error('🔥 LIKELY TRUNCATED RESPONSE - Response appears incomplete.');
                    }
                    // Try to repair JSON for edge cases (1 in 1000)
                    console.log('🔧 Attempting JSON repair for edge case...');
                    const repairedContent = this.repairJSON(responseContent);
                    if (repairedContent !== responseContent) {
                        console.log('🛠️ JSON repair applied, attempting parse again...');
                        try {
                            parsedResponse = JSON.parse(repairedContent);
                            console.log('✅ JSON repair successful!');
                        }
                        catch (repairError) {
                            console.error('❌ JSON repair failed:', repairError);
                            throw new Error(`Claude response is not valid JSON. Length: ${responseContent.length}, Type: ${typeof responseContent}${isLikelyTruncated ? ' - LIKELY TRUNCATED' : ''}`);
                        }
                    }
                    else {
                        throw new Error(`Claude response is not valid JSON. Length: ${responseContent.length}, Type: ${typeof responseContent}${isLikelyTruncated ? ' - LIKELY TRUNCATED' : ''}`);
                    }
                }
                // Clean timestamp prefixes from messages
                parsedResponse = cleanTimestampPrefixes(parsedResponse);
                // Log parsed response
                console.log('🔍 Parsed JSON Response:', parsedResponse);
                // Validate against schema
                const validationResult = InstagramResponseSchema.safeParse(parsedResponse);
                if (!validationResult.success) {
                    console.error('❌ Claude response validation failed:', {
                        errors: validationResult.error.errors,
                        response: parsedResponse
                    });
                    throw new Error('Claude response does not match expected schema');
                }
                // Add thinking to the response (Claude thinking is not exposed via API)
                const finalResponse = {
                    ...validationResult.data,
                    aiReasoning: undefined
                };
                // Log final validated response
                console.log('✅ Generated valid Claude response:', {
                    stage: finalResponse.instagramStage,
                    priority: finalResponse.engagementPriority,
                    isDisqualified: finalResponse.isDisqualified,
                    hasMessage1: !!finalResponse.message1,
                    hasMessage2: !!finalResponse.message2,
                    hasMessage3: !!finalResponse.message3,
                    hasMessage4: !!finalResponse.message4,
                    notesLength: finalResponse.notes?.length || 0,
                    hasThinking: !!finalResponse.aiReasoning,
                    thinkingLength: 0, // Claude thinking not exposed via API
                    followupsCount: [
                        finalResponse.followup1_text,
                        finalResponse.followup2_text,
                        finalResponse.followup3_text,
                        finalResponse.followup4_text,
                        finalResponse.followup5_text,
                        finalResponse.followup6_text
                    ].filter(Boolean).length
                });
                return finalResponse;
            }
            catch (error) {
                console.error(`❌ Error in Claude client (attempt ${attempt}/2):`, error);
                // Check if this is a JSON parsing error that might be retryable
                const isJSONError = error instanceof Error && error.message.includes('Claude response is not valid JSON');
                if (isJSONError && attempt < 2) {
                    console.log('🔄 JSON parsing failed, retrying with fresh request...');
                    continue; // Try again
                }
                // Handle non-retryable errors immediately
                if (error instanceof Anthropic.APIError) {
                    if (error.status === 401) {
                        throw new ForbiddenError('Invalid Anthropic API key');
                    }
                }
                // If we've exhausted retries or it's not a JSON error, throw
                throw error;
            }
        }
        // This should never be reached due to throw in catch, but TypeScript needs it
        throw new Error('Max retry attempts exceeded');
    }
    /**
     * Create cached system message with XML prompt
     * Use prompt caching for better performance
     */
    createCachedSystemMessage(xmlPrompt, dateTimePrompt) {
        return {
            role: 'system',
            content: [
                {
                    type: 'text',
                    text: xmlPrompt,
                    cache_control: {
                        type: 'ephemeral',
                        ...(this.config.cacheDuration && { ttl: this.config.cacheDuration })
                    }
                },
                {
                    type: 'text',
                    text: dateTimePrompt
                    // No cache_control here - this content changes constantly
                }
            ]
        };
    }
    /**
     * Create structured conversation messages
     */
    createConversationMessages(structuredMessages) {
        return structuredMessages;
    }
    /**
     * Build Anthropic SDK request parameters
     */
    buildAnthropicParams(request) {
        const nonSystemMessages = request.messages.filter(msg => msg.role !== 'system');
        const systemMessage = request.messages.find(msg => msg.role === 'system');
        const mappedMessages = nonSystemMessages.map(msg => ({
            role: msg.role,
            content: typeof msg.content === 'string' ? msg.content : msg.content.map(c => ({
                type: c.type,
                text: c.text || '',
                ...(c.cache_control && { cache_control: c.cache_control }),
                ...(c.tool_use_id && { tool_use_id: c.tool_use_id })
            }))
        }));
        // Add JSON prefill to force JSON response format
        mappedMessages.push({
            role: 'assistant',
            content: '{'
        });
        const params = {
            model: request.model,
            messages: mappedMessages,
            max_tokens: request.max_tokens,
            temperature: request.temperature,
            top_p: request.top_p
        };
        // Add system message if present
        if (systemMessage) {
            if (typeof systemMessage.content === 'string') {
                params.system = systemMessage.content;
            }
            else {
                params.system = systemMessage.content.map(c => ({
                    type: c.type,
                    text: c.text || '',
                    ...(c.cache_control && { cache_control: c.cache_control })
                }));
            }
        }
        // Add tools if provided
        if (request.tools && request.tools.length > 0) {
            params.tools = request.tools.map(tool => ({
                name: tool.name,
                description: tool.description,
                input_schema: {
                    type: 'object',
                    properties: tool.input_schema.properties,
                    required: tool.input_schema.required
                }
            }));
        }
        return params;
    }
    /**
     * Build Anthropic request with structured outputs and thinking
     */
    buildRequest(systemMessage, conversationMessages, config, calendlyIntegration) {
        const request = {
            model: config.model,
            messages: [systemMessage, ...conversationMessages],
            max_tokens: 4096, // Default max tokens for Claude
            temperature: config.temperature,
            top_p: config.topP
        };
        // Add thinking configuration if enabled
        if (config.enableThinking) {
            request.thinking = {
                enabled: true
            };
        }
        // Add Calendly tools if integration is enabled
        if (calendlyIntegration?.enabled) {
            const calendlyTools = getCalendlyToolDefinitions();
            request.tools = calendlyTools.map(tool => ({
                name: tool.name,
                description: tool.description,
                input_schema: {
                    type: tool.parameters.type,
                    properties: tool.parameters.properties,
                    required: tool.parameters.required
                }
            }));
        }
        return request;
    }
    /**
     * Handle tool use - execute tools and continue conversation
     * Loops up to 10 times if Claude returns only tool_use blocks without text content
     */
    async handleToolUse(initialResponse, originalParams, calendlyApiToken) {
        let currentResponse = initialResponse;
        let conversationMessages = [...originalParams.messages];
        const maxIterations = 10;
        let iteration = 0;
        while (iteration < maxIterations) {
            iteration++;
            console.log(`🔄 Tool execution iteration ${iteration}/${maxIterations}`);
            const toolUseBlocks = currentResponse.content.filter((block) => block.type === 'tool_use');
            if (toolUseBlocks.length === 0) {
                // No more tools to execute, return the response
                console.log('✅ No more tools to execute, returning response');
                return currentResponse;
            }
            const toolResults = [];
            // Execute all tools in this iteration
            for (const toolBlock of toolUseBlocks) {
                console.log(`🔧 Executing tool: ${toolBlock.name} with params:`, toolBlock.input);
                try {
                    const result = await this.calendlyExecutor.execute(toolBlock.name, toolBlock.input, calendlyApiToken);
                    console.log(`✅ Tool result for ${toolBlock.name}:`, result);
                    toolResults.push({
                        type: 'tool_result',
                        tool_use_id: toolBlock.id,
                        content: result
                    });
                }
                catch (error) {
                    console.error(`❌ Tool execution failed for ${toolBlock.name}:`, error);
                    toolResults.push({
                        type: 'tool_result',
                        tool_use_id: toolBlock.id,
                        content: `Error: ${error instanceof Error ? error.message : 'Tool execution failed'}`
                    });
                }
            }
            // Add assistant message with tool uses and user message with tool results
            conversationMessages.push({ role: 'assistant', content: currentResponse.content }, { role: 'user', content: toolResults });
            // Continue conversation with tool results
            const followUpParams = {
                ...originalParams,
                messages: conversationMessages
            };
            console.log('🔄 Continuing conversation with tool results...');
            currentResponse = await this.client.messages.create(followUpParams);
            console.log('🎯 Response after tool execution:', {
                iteration,
                stopReason: currentResponse.stop_reason,
                contentTypes: currentResponse.content.map(c => c.type)
            });
            // Check if we have text content - if so, we're done
            const hasTextContent = currentResponse.content.some((block) => block.type === 'text');
            if (hasTextContent) {
                console.log(`✅ Got text content after ${iteration} iterations`);
                return currentResponse;
            }
            console.log(`⚠️ No text content in iteration ${iteration}, continuing loop...`);
        }
        // If we've reached max iterations without text content, throw an error
        console.error(`❌ Reached maximum tool execution iterations (${maxIterations}) without getting text response`);
        throw new Error('AI assistant exceeded maximum tool execution attempts without providing final response');
    }
    /**
     * Extract clean JSON from Claude response, handling thinking blocks and various formats
     */
    extractCleanJSON(responseContent) {
        let cleanContent = responseContent.trim();
        // Remove <thinking>...</thinking> blocks (case insensitive)
        cleanContent = cleanContent.replace(/<thinking>[\s\S]*?<\/thinking>/gi, '');
        // Remove markdown code blocks if present
        if (cleanContent.startsWith('```json')) {
            cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        }
        else if (cleanContent.startsWith('```')) {
            cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }
        // Clean up any remaining whitespace
        cleanContent = cleanContent.trim();
        // Handle prefill case - if we prefilled with '{', Claude continues without it
        if (!cleanContent.startsWith('{') && !cleanContent.startsWith('[')) {
            cleanContent = '{' + cleanContent;
        }
        // Try to find JSON object boundaries if response contains extra text
        const jsonStart = cleanContent.indexOf('{');
        const jsonEnd = cleanContent.lastIndexOf('}');
        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
            cleanContent = cleanContent.substring(jsonStart, jsonEnd + 1);
        }
        return cleanContent;
    }
    /**
     * Attempt to repair common JSON syntax errors
     * Handles edge cases like unescaped quotes in Polish text
     */
    repairJSON(content) {
        let repaired = content;
        // Fix common issues with quotes in text values
        // Look for unescaped quotes within string values (between : " and " ,)
        repaired = repaired.replace(/(":\s*")([^"]*)"([^",}]*)"([^",}]*)/g, (match, prefix, beforeQuote, betweenQuotes, afterQuote) => {
            // Only repair if this looks like an unescaped quote issue
            if (betweenQuotes.length > 0 && !afterQuote.startsWith(',') && !afterQuote.startsWith('}')) {
                return `${prefix}${beforeQuote}\\"${betweenQuotes}\\"${afterQuote}`;
            }
            return match;
        });
        // Fix trailing commas before closing braces
        repaired = repaired.replace(/,(\s*})/g, '$1');
        // Fix missing commas between properties (look for }" followed by ")
        repaired = repaired.replace(/}"\s*"/g, '}","');
        // Fix missing quotes around property names
        repaired = repaired.replace(/\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '{"$1":');
        return repaired;
    }
}
