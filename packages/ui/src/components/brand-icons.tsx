import * as React from 'react';

export function LinkedInIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      width="16"
      height="16"
      strokeLinejoin="round"
      color="currentcolor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M2 0a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V2a2 2 0 00-2-2H2zm3 6.75V13H3V6.75h2zM5 4.5c0 .556-.386 1-1.006 1h-.012C3.386 5.5 3 5.056 3 4.5c0-.568.398-1 1.006-1s.982.432.994 1zM8.5 13h-2s.032-5.568 0-6.25h2v1.034s.5-1.034 2-1.034 2.5.848 2.5 3.081V13h-2v-2.89s0-1.644-1.264-1.644S8.5 9.94 8.5 9.94V13z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function XIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      strokeLinejoin="round"
      color="currentcolor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M.5.5h5.25l3.734 5.21L14 .5h2l-5.61 6.474L16.5 15.5h-5.25l-3.734-5.21L3 15.5H1l5.61-6.474L.5.5zM12.02 14L3.42 2h1.56l8.6 12h-1.56z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function YouTubeIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      width="16"
      height="16"
      strokeLinejoin="round"
      color="currentcolor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M14.251 3.435a2.003 2.003 0 011.414 1.414C16 6.097 16 8.7 16 8.7s0 2.605-.335 3.851a2.003 2.003 0 01-1.415 1.415C13.006 14.3 8 14.3 8 14.3s-5.003 0-6.251-.334a2.004 2.004 0 01-1.414-1.415C0 11.305 0 8.7 0 8.7s0-2.603.335-3.851a2.003 2.003 0 011.414-1.414C2.997 3.1 8 3.1 8 3.1s5.003 0 6.251.335zM10.555 8.7L6.4 11.1V6.3l4.155 2.4z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function InstagramIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="currentColor"
        d="M10.088 7.688a2.4 2.4 0 11-4.8 0 2.4 2.4 0 014.8 0zm5.1-3.3v6.6a4.205 4.205 0 01-4.2 4.2h-6.6a4.205 4.205 0 01-4.2-4.2v-6.6a4.205 4.205 0 014.2-4.2h6.6a4.205 4.205 0 014.2 4.2zm-3.9 3.3a3.6 3.6 0 10-7.2 0 3.6 3.6 0 007.2 0zm1.2-3.9a.9.9 0 10-1.8 0 .9.9 0 001.8 0z"
      />
    </svg>
  );
}

export function TikTokIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      width="24"
      height="24"
      fill="currentColor"
      {...props}
    >
      <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z" />
    </svg>
  );
}

export function FacebookIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      width="16"
      height="16"
      strokeLinejoin="round"
      color="currentcolor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M9.003 15.938A8.001 8.001 0 008 0a8 8 0 00-1.75 15.808V10.43H4.5V8h1.75V6.94c0-2.718 1.035-3.976 3.701-3.976.505 0 1.377.099 1.734.198v2.21c-.188-.02-.517-.03-.923-.03C9.454 5.343 9 5.839 9 7.129V8h2.558l-.447 2.43H9.003v5.508z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function GitHubIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="15"
      height="15"
      fill="none"
      viewBox="0 0 15 15"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M7.5.25a7.25 7.25 0 0 0-2.292 14.13c.363.066.495-.158.495-.35 0-.172-.006-.628-.01-1.233-2.016.438-2.442-.972-2.442-.972-.33-.838-.805-1.06-.805-1.06-.658-.45.05-.441.05-.441.728.051 1.11.747 1.11.747.647 1.108 1.697.788 2.11.602.066-.468.254-.788.46-.969-1.61-.183-3.302-.805-3.302-3.583 0-.792.283-1.438.747-1.945-.075-.184-.324-.92.07-1.92 0 0 .61-.194 1.994.744A7 7 0 0 1 7.5 3.756 7 7 0 0 1 9.315 4c1.384-.938 1.992-.743 1.992-.743.396.998.147 1.735.072 1.919.465.507.745 1.153.745 1.945 0 2.785-1.695 3.398-3.31 3.577.26.224.492.667.492 1.343 0 .97-.009 1.751-.009 1.989 0 .194.131.42.499.349A7.25 7.25 0 0 0 7.499.25"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function GoogleIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="#4285F4"
        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09"
      />
      <path
        fill="#34A853"
        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23"
      />
      <path
        fill="#FBBC05"
        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22z"
      />
      <path
        fill="#EA4335"
        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53"
      />
      <path
        fill="none"
        d="M1 1h22v22H1z"
      />
    </svg>
  );
}

export function MicrosoftIcon(
  props: React.SVGAttributes<SVGSVGElement>
): React.JSX.Element {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="21"
      height="21"
      fill="none"
      viewBox="0 0 21 21"
      {...props}
    >
      <path
        fill="#f25022"
        d="M1 1h9v9H1z"
      />
      <path
        fill="#00a4ef"
        d="M1 11h9v9H1z"
      />
      <path
        fill="#7fba00"
        d="M11 1h9v9h-9z"
      />
      <path
        fill="#ffb900"
        d="M11 11h9v9h-9z"
      />
    </svg>
  );
}
