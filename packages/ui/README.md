# `@workspace/ui`

This package provides a collection of reusable UI components, hooks, styles, and utilities for building consistent and beautiful user interfaces. It includes pre-built components such as buttons, modals, forms, and more, all designed to be easily customizable and accessible, as well as a flexible design system to ensure a cohesive look and feel across your project.

## Features

- Components: A set of modular UI components that can be easily used and customized across your project.
- Hooks: Useful hooks for managing state, handling UI events, and other common tasks.
- Responsive: All components are designed to be responsive out of the box, ensuring your app looks great on all devices.
- Theming Support: Easily configure and apply themes to adjust the look and feel of the components across your app.
- Accessibility: Components are built with accessibility in mind, ensuring a better user experience for all.
