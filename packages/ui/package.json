{"name": "@workspace/ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@icons-pack/react-simple-icons": "^13.7.0", "@lexical/code": "0.32.1", "@lexical/html": "0.32.1", "@lexical/link": "0.32.1", "@lexical/list": "0.32.1", "@lexical/markdown": "0.32.1", "@lexical/react": "0.32.1", "@lexical/rich-text": "0.32.1", "@lexical/selection": "0.32.1", "@lexical/table": "0.32.1", "@lexical/utils": "0.32.1", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-use-controllable-state": "^1.2.2", "@tanstack/react-table": "8.21.3", "@workspace/common": "workspace:*", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "date-fns": "4.1.0", "embla-carousel-autoplay": "8.6.0", "embla-carousel-react": "8.6.0", "emoji-picker-react": "4.12.2", "framer-motion": "^11.18.2", "input-otp": "1.4.2", "lexical": "0.32.1", "lucide-react": "0.513.0", "motion": "12.18.1", "next": "15.3.3", "next-themes": "0.4.6", "radix-ui": "1.4.2", "react": "19.0.0", "react-accessible-treeview": "2.11.1", "react-day-picker": "9.7.0", "react-dom": "19.0.0", "react-dropzone": "14.3.8", "react-hook-form": "7.57.0", "react-image-crop": "11.0.10", "react-is": "19.0.0", "react-markdown": "^10.1.0", "react-remove-scroll": "2.7.1", "react-resizable-panels": "3.0.2", "recharts": "2.15.3", "remark-gfm": "4.0.1", "remeda": "2", "shiki": "3.6.0", "sonner": "2.0.5", "tailwind-merge": "3.3.0", "tw-animate-css": "1.3.4", "use-stick-to-bottom": "^1.1.1", "vaul": "1.1.2"}, "devDependencies": {"@tailwindcss/postcss": "4.1.8", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "postcss": "8.5.4", "tailwindcss": "4.1.8"}, "prettier": "@workspace/prettier-config", "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}}