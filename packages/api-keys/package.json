{"name": "@workspace/api-keys", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@workspace/common": "workspace:*", "@workspace/database": "workspace:*", "date-fns": "4.1.0"}, "devDependencies": {"@types/node": "22.15.30", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "prettier": "@workspace/prettier-config", "exports": {"./generate-api-key": "./src/generate-api-key.ts", "./hash-api-key": "./src/hash-api-key.ts", "./verify-api-key": "./src/verify-api-key.ts"}}