-- CreateEnum
CREATE TYPE "ActionType" AS ENUM ('create', 'update', 'delete');

-- CreateEnum
CREATE TYPE "ActorType" AS ENUM ('system', 'member', 'api');

-- CreateEnum
CREATE TYPE "DayOfWeek" AS ENUM ('sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday');

-- CreateEnum
CREATE TYPE "FeedbackCategory" AS ENUM ('suggestion', 'problem', 'question');

-- CreateEnum
CREATE TYPE "InvitationStatus" AS ENUM ('pending', 'accepted', 'revoked');

-- CreateEnum
CREATE TYPE "Role" AS ENUM ('member', 'admin');

-- CreateEnum
CREATE TYPE "InstagramStage" AS ENUM ('firstcontact', 'initial', 'engaged', 'qualified', 'formsent', 'converted', 'disqualified', 'suspicious');

-- CreateEnum
CREATE TYPE "FollowupStatus" AS ENUM ('pending', 'sent', 'failed');

-- CreateEnum
CREATE TYPE "FollowupsStyle" AS ENUM ('none', 'light', 'medium', 'hard');

-- CreateEnum
CREATE TYPE "EngagementPriority" AS ENUM ('new_follower', 'very_high', 'high', 'medium', 'low', 'non_responded');

-- CreateTable
CREATE TABLE "Account" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Account" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ApiKey" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "description" VARCHAR(70) NOT NULL,
    "hashedKey" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3),
    "lastUsedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_ApiKey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AuthenticatorApp" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "accountName" VARCHAR(255) NOT NULL,
    "issuer" VARCHAR(255) NOT NULL,
    "secret" VARCHAR(255) NOT NULL,
    "recoveryCodes" VARCHAR(1024) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_AuthenticatorApp" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChangeEmailRequest" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,
    "valid" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_ChangeEmailRequest" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Feedback" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "userId" UUID,
    "category" "FeedbackCategory" NOT NULL DEFAULT 'suggestion',
    "message" VARCHAR(4000) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Feedback" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Invitation" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "token" UUID NOT NULL,
    "email" VARCHAR(255) NOT NULL,
    "role" "Role" NOT NULL DEFAULT 'member',
    "status" "InvitationStatus" NOT NULL DEFAULT 'pending',
    "lastSentAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Invitation" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Membership" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "role" "Role" NOT NULL DEFAULT 'member',
    "isOwner" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PK_Membership" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Notification" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "subject" VARCHAR(128),
    "content" VARCHAR(8000) NOT NULL,
    "link" VARCHAR(2000),
    "seenAt" TIMESTAMP(3),
    "dismissed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Notification" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Order" (
    "id" TEXT NOT NULL,
    "organizationId" UUID NOT NULL,
    "status" VARCHAR(64) NOT NULL,
    "provider" VARCHAR(32) NOT NULL,
    "totalAmount" DECIMAL(65,30) NOT NULL DEFAULT 0,
    "currency" VARCHAR(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Order" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrderItem" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "productId" TEXT NOT NULL,
    "variantId" TEXT NOT NULL,
    "priceAmount" DOUBLE PRECISION,
    "type" TEXT,
    "model" TEXT,

    CONSTRAINT "PK_OrderItem" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Organization" (
    "id" UUID NOT NULL,
    "slug" VARCHAR(255) NOT NULL,
    "logo" VARCHAR(2048),
    "name" VARCHAR(255) NOT NULL,
    "address" VARCHAR(255),
    "phone" VARCHAR(32),
    "email" VARCHAR(255),
    "website" VARCHAR(2000),
    "linkedInProfile" VARCHAR(2000),
    "instagramProfile" VARCHAR(2000),
    "youTubeChannel" VARCHAR(2000),
    "xProfile" VARCHAR(2000),
    "tikTokProfile" VARCHAR(2000),
    "facebookPage" VARCHAR(2000),
    "billingCustomerId" TEXT,
    "billingEmail" VARCHAR(255),
    "billingLine1" VARCHAR(255),
    "billingLine2" VARCHAR(255),
    "billingCountry" VARCHAR(3),
    "billingPostalCode" VARCHAR(16),
    "billingCity" VARCHAR(255),
    "billingState" VARCHAR(255),
    "instagramBotEnabled" BOOLEAN NOT NULL DEFAULT false,
    "instagramResponseTimeMin" INTEGER NOT NULL DEFAULT 10,
    "instagramResponseTimeMax" INTEGER NOT NULL DEFAULT 180,
    "instagramFollowupsStyle" "FollowupsStyle" NOT NULL DEFAULT 'none',
    "instagramToken" TEXT,
    "instagramAccountId" TEXT,
    "instagramWebhookId" TEXT,
    "instagramUsername" TEXT,
    "instagramName" TEXT,
    "instagramProfilePicture" TEXT,
    "instagramIsConnected" BOOLEAN NOT NULL DEFAULT false,
    "instagramTokenExpiresAt" TIMESTAMP(3),
    "instagramConnectedAt" TIMESTAMP(3),
    "instagramUpdatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PK_Organization" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrganizationLogo" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "data" BYTEA,
    "contentType" VARCHAR(255),
    "hash" VARCHAR(64),

    CONSTRAINT "PK_OrganizationLogo" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ResetPasswordRequest" (
    "id" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_ResetPasswordRequest" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Session" (
    "id" UUID NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" UUID NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Session" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Subscription" (
    "id" TEXT NOT NULL,
    "organizationId" UUID NOT NULL,
    "status" VARCHAR(64) NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT false,
    "provider" VARCHAR(32) NOT NULL,
    "cancelAtPeriodEnd" BOOLEAN NOT NULL DEFAULT false,
    "currency" VARCHAR(3) NOT NULL,
    "periodStartsAt" TIMESTAMPTZ(6) NOT NULL,
    "periodEndsAt" TIMESTAMPTZ(6) NOT NULL,
    "trialStartsAt" TIMESTAMPTZ(6),
    "trialEndsAt" TIMESTAMPTZ(6),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Subscription" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SubscriptionItem" (
    "id" TEXT NOT NULL,
    "subscriptionId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "productId" TEXT NOT NULL,
    "variantId" TEXT NOT NULL,
    "priceAmount" DOUBLE PRECISION,
    "interval" TEXT NOT NULL,
    "intervalCount" INTEGER NOT NULL,
    "type" TEXT,
    "model" TEXT,

    CONSTRAINT "PK_SubscriptionItem" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" UUID NOT NULL,
    "image" VARCHAR(2048),
    "name" VARCHAR(64) NOT NULL,
    "email" TEXT,
    "emailVerified" TIMESTAMP(3),
    "password" VARCHAR(60),
    "lastLogin" TIMESTAMP(3),
    "phone" VARCHAR(32),
    "locale" VARCHAR(8) NOT NULL DEFAULT 'en-US',
    "completedOnboarding" BOOLEAN NOT NULL DEFAULT false,
    "enabledContactsNotifications" BOOLEAN NOT NULL DEFAULT false,
    "enabledInboxNotifications" BOOLEAN NOT NULL DEFAULT false,
    "enabledWeeklySummary" BOOLEAN NOT NULL DEFAULT false,
    "enabledNewsletter" BOOLEAN NOT NULL DEFAULT false,
    "enabledProductUpdates" BOOLEAN NOT NULL DEFAULT false,
    "isPlatformAdmin" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_User" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserImage" (
    "id" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "data" BYTEA,
    "contentType" VARCHAR(255),
    "hash" VARCHAR(64),

    CONSTRAINT "PK_UserImage" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateTable
CREATE TABLE "Webhook" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "url" VARCHAR(2000) NOT NULL,
    "secret" VARCHAR(1024),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_Webhook" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkHours" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "dayOfWeek" "DayOfWeek" NOT NULL DEFAULT 'sunday',

    CONSTRAINT "PK_WorkHours" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkTimeSlot" (
    "id" UUID NOT NULL,
    "workHoursId" UUID NOT NULL,
    "start" TIME(0) NOT NULL,
    "end" TIME(0) NOT NULL,

    CONSTRAINT "PK_WorkTimeSlot" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramContact" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "instagramUserId" VARCHAR(255) NOT NULL,
    "nickname" VARCHAR(255),
    "profilePhoto" VARCHAR(2048),
    "notes" VARCHAR(8000),
    "lastActivity" TIMESTAMP(3),
    "messageCount" INTEGER NOT NULL DEFAULT 0,
    "firstMessageAt" TIMESTAMP(3),
    "lastMessageAt" TIMESTAMP(3),
    "lastContactMessageAt" TIMESTAMP(3),
    "aiEnabled" BOOLEAN NOT NULL DEFAULT true,
    "stage" "InstagramStage" NOT NULL DEFAULT 'firstcontact',
    "engagementPriority" "EngagementPriority" NOT NULL DEFAULT 'new_follower',
    "followup1_text" VARCHAR(4000),
    "followup1_scheduledAt" TIMESTAMP(3),
    "followup1_status" "FollowupStatus",
    "followup1_sendViaExtension" BOOLEAN NOT NULL DEFAULT false,
    "followup2_text" VARCHAR(4000),
    "followup2_scheduledAt" TIMESTAMP(3),
    "followup2_status" "FollowupStatus",
    "followup2_sendViaExtension" BOOLEAN NOT NULL DEFAULT false,
    "followup3_text" VARCHAR(4000),
    "followup3_scheduledAt" TIMESTAMP(3),
    "followup3_status" "FollowupStatus",
    "followup3_sendViaExtension" BOOLEAN NOT NULL DEFAULT false,
    "followup4_text" VARCHAR(4000),
    "followup4_scheduledAt" TIMESTAMP(3),
    "followup4_status" "FollowupStatus",
    "followup4_sendViaExtension" BOOLEAN NOT NULL DEFAULT false,
    "followup5_text" VARCHAR(4000),
    "followup5_scheduledAt" TIMESTAMP(3),
    "followup5_status" "FollowupStatus",
    "followup5_sendViaExtension" BOOLEAN NOT NULL DEFAULT false,
    "followup6_text" VARCHAR(4000),
    "followup6_scheduledAt" TIMESTAMP(3),
    "followup6_status" "FollowupStatus",
    "followup6_sendViaExtension" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_InstagramContact" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlatformSettings" (
    "id" UUID NOT NULL,
    "generalPrompt" TEXT,
    "followupNonePrompt" TEXT,
    "followupLightPrompt" TEXT,
    "followupMediumPrompt" TEXT,
    "followupHardPrompt" TEXT,
    "followupNoneMinimum" INTEGER NOT NULL DEFAULT 0,
    "followupLightMinimum" INTEGER NOT NULL DEFAULT 0,
    "followupMediumMinimum" INTEGER NOT NULL DEFAULT 0,
    "followupHardMinimum" INTEGER NOT NULL DEFAULT 0,
    "maxConversationMessages" INTEGER NOT NULL DEFAULT 25,
    "aiModel" TEXT NOT NULL DEFAULT 'openai/gpt-4o-mini',
    "enableReasoning" BOOLEAN NOT NULL DEFAULT false,
    "reasoningMaxTokens" INTEGER,
    "maxTokens" INTEGER NOT NULL DEFAULT 4096,
    "openRouterApiKey" TEXT,
    "zaiApiKey" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_PlatformSettings" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BotStyle" (
    "id" UUID NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "description" VARCHAR(500) NOT NULL,
    "prompt" TEXT NOT NULL,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_BotStyle" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrganizationPrompts" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "botStyleId" UUID,
    "aboutUs" TEXT,
    "qualificationQuestions" TEXT,
    "additionalInformations" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_OrganizationPrompts" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramConversationIndex" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "conversationId" VARCHAR(255) NOT NULL,
    "participantId" VARCHAR(255) NOT NULL,
    "participantUsername" VARCHAR(255),
    "lastUpdated" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PK_InstagramConversationIndex" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InstagramMessage" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "conversationId" VARCHAR(255) NOT NULL,
    "instagramUserId" VARCHAR(255) NOT NULL,
    "messageId" VARCHAR(255) NOT NULL,
    "isFromBusiness" BOOLEAN NOT NULL DEFAULT false,
    "messageType" VARCHAR(50) NOT NULL DEFAULT 'text',
    "content" TEXT,
    "attachmentUrl" VARCHAR(2048),
    "aiReasoning" TEXT,
    "instagramCreatedTime" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PK_InstagramMessage" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "IX_Account_userId" ON "Account"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "ApiKey_hashedKey_key" ON "ApiKey"("hashedKey");

-- CreateIndex
CREATE INDEX "IX_ApiKey_organizationId" ON "ApiKey"("organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "AuthenticatorApp_userId_key" ON "AuthenticatorApp"("userId");

-- CreateIndex
CREATE INDEX "IX_AuthenticatorApp_userId" ON "AuthenticatorApp"("userId");

-- CreateIndex
CREATE INDEX "IX_ChangeEmailRequest_userId" ON "ChangeEmailRequest"("userId");

-- CreateIndex
CREATE INDEX "IX_Feedback_organizationId" ON "Feedback"("organizationId");

-- CreateIndex
CREATE INDEX "IX_Feedback_userId" ON "Feedback"("userId");

-- CreateIndex
CREATE INDEX "IX_Invitation_organizationId" ON "Invitation"("organizationId");

-- CreateIndex
CREATE INDEX "IX_Invitation_token" ON "Invitation"("token");

-- CreateIndex
CREATE UNIQUE INDEX "Membership_organizationId_userId_key" ON "Membership"("organizationId", "userId");

-- CreateIndex
CREATE INDEX "IX_Notification_userId" ON "Notification"("userId");

-- CreateIndex
CREATE INDEX "IX_Order_organizationId" ON "Order"("organizationId");

-- CreateIndex
CREATE INDEX "IX_OrderItem_orderId" ON "OrderItem"("orderId");

-- CreateIndex
CREATE UNIQUE INDEX "Organization_slug_key" ON "Organization"("slug");

-- CreateIndex
CREATE INDEX "IX_Organization_billingCustomerId" ON "Organization"("billingCustomerId");

-- CreateIndex
CREATE INDEX "IX_OrganizationLogo_organizationId" ON "OrganizationLogo"("organizationId");

-- CreateIndex
CREATE INDEX "IX_ResetPasswordRequest_email" ON "ResetPasswordRequest"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken");

-- CreateIndex
CREATE INDEX "IX_Session_userId" ON "Session"("userId");

-- CreateIndex
CREATE INDEX "IX_Subscription_organizationId" ON "Subscription"("organizationId");

-- CreateIndex
CREATE INDEX "IX_SubscriptionItem_subscriptionId" ON "SubscriptionItem"("subscriptionId");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE INDEX "IX_UserImage_userId" ON "UserImage"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token");

-- CreateIndex
CREATE INDEX "IX_Webhook_organizationId" ON "Webhook"("organizationId");

-- CreateIndex
CREATE INDEX "IX_WorkHours_organizationId" ON "WorkHours"("organizationId");

-- CreateIndex
CREATE INDEX "IX_WorkTimeSlot_workHoursId" ON "WorkTimeSlot"("workHoursId");

-- CreateIndex
CREATE INDEX "IX_InstagramContact_organizationId" ON "InstagramContact"("organizationId");

-- CreateIndex
CREATE INDEX "IX_InstagramContact_lastContactMessageAt" ON "InstagramContact"("lastContactMessageAt");

-- CreateIndex
CREATE INDEX "IX_InstagramContact_followup1" ON "InstagramContact"("followup1_status", "followup1_sendViaExtension", "followup1_scheduledAt");

-- CreateIndex
CREATE INDEX "IX_InstagramContact_followup2" ON "InstagramContact"("followup2_status", "followup2_sendViaExtension", "followup2_scheduledAt");

-- CreateIndex
CREATE INDEX "IX_InstagramContact_followup3" ON "InstagramContact"("followup3_status", "followup3_sendViaExtension", "followup3_scheduledAt");

-- CreateIndex
CREATE INDEX "IX_InstagramContact_followup4" ON "InstagramContact"("followup4_status", "followup4_sendViaExtension", "followup4_scheduledAt");

-- CreateIndex
CREATE INDEX "IX_InstagramContact_followup5" ON "InstagramContact"("followup5_status", "followup5_sendViaExtension", "followup5_scheduledAt");

-- CreateIndex
CREATE INDEX "IX_InstagramContact_followup6" ON "InstagramContact"("followup6_status", "followup6_sendViaExtension", "followup6_scheduledAt");

-- CreateIndex
CREATE UNIQUE INDEX "InstagramContact_instagramUserId_organizationId_key" ON "InstagramContact"("instagramUserId", "organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "OrganizationPrompts_organizationId_key" ON "OrganizationPrompts"("organizationId");

-- CreateIndex
CREATE INDEX "IX_OrganizationPrompts_organizationId" ON "OrganizationPrompts"("organizationId");

-- CreateIndex
CREATE INDEX "IX_OrganizationPrompts_botStyleId" ON "OrganizationPrompts"("botStyleId");

-- CreateIndex
CREATE INDEX "IX_InstagramConversationIndex_org_username" ON "InstagramConversationIndex"("organizationId", "participantUsername");

-- CreateIndex
CREATE INDEX "IX_InstagramConversationIndex_org_updated" ON "InstagramConversationIndex"("organizationId", "lastUpdated");

-- CreateIndex
CREATE UNIQUE INDEX "UQ_InstagramConversationIndex_org_conversation" ON "InstagramConversationIndex"("organizationId", "conversationId");

-- CreateIndex
CREATE INDEX "IX_InstagramMessage_conversation_time" ON "InstagramMessage"("conversationId", "instagramCreatedTime");

-- CreateIndex
CREATE INDEX "IX_InstagramMessage_org_user" ON "InstagramMessage"("organizationId", "instagramUserId");

-- CreateIndex
CREATE UNIQUE INDEX "UQ_InstagramMessage_messageId_org" ON "InstagramMessage"("messageId", "organizationId");

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiKey" ADD CONSTRAINT "ApiKey_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuthenticatorApp" ADD CONSTRAINT "AuthenticatorApp_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChangeEmailRequest" ADD CONSTRAINT "ChangeEmailRequest_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Feedback" ADD CONSTRAINT "Feedback_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Feedback" ADD CONSTRAINT "Feedback_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Invitation" ADD CONSTRAINT "Invitation_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Membership" ADD CONSTRAINT "Membership_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Membership" ADD CONSTRAINT "Membership_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Subscription" ADD CONSTRAINT "Subscription_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubscriptionItem" ADD CONSTRAINT "SubscriptionItem_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Webhook" ADD CONSTRAINT "Webhook_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkHours" ADD CONSTRAINT "WorkHours_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkTimeSlot" ADD CONSTRAINT "WorkTimeSlot_workHoursId_fkey" FOREIGN KEY ("workHoursId") REFERENCES "WorkHours"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramContact" ADD CONSTRAINT "InstagramContact_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrganizationPrompts" ADD CONSTRAINT "OrganizationPrompts_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrganizationPrompts" ADD CONSTRAINT "OrganizationPrompts_botStyleId_fkey" FOREIGN KEY ("botStyleId") REFERENCES "BotStyle"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramConversationIndex" ADD CONSTRAINT "InstagramConversationIndex_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InstagramMessage" ADD CONSTRAINT "InstagramMessage_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
