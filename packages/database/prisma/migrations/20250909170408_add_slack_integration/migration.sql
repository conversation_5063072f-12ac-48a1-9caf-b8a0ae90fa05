-- CreateTable
CREATE TABLE "SlackIntegration" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "webhookUrl" TEXT NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_SlackIntegration" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "SlackIntegration_organizationId_key" ON "SlackIntegration"("organizationId");

-- CreateIndex
CREATE INDEX "IX_SlackIntegration_organizationId" ON "SlackIntegration"("organizationId");

-- AddForeignKey
ALTER TABLE "SlackIntegration" ADD CONSTRAINT "SlackIntegration_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;