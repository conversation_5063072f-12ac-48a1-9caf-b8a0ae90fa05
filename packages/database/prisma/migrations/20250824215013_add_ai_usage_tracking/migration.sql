-- CreateTable
CREATE TABLE "AiUsage" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "instagramContactId" UUID,
    "conversationId" VARCHAR(255),
    "provider" VARCHAR(50) NOT NULL,
    "model" VARCHAR(255) NOT NULL,
    "totalTokens" INTEGER NOT NULL,
    "promptTokens" INTEGER NOT NULL,
    "completionTokens" INTEGER NOT NULL,
    "reasoningTokens" INTEGER NOT NULL DEFAULT 0,
    "cachedTokens" INTEGER NOT NULL DEFAULT 0,
    "audioTokens" INTEGER NOT NULL DEFAULT 0,
    "totalCost" DECIMAL(10,6) NOT NULL,
    "upstreamCost" DECIMAL(10,6),
    "requestType" VARCHAR(50) NOT NULL,
    "responseGenerated" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PK_AiUsage" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "IX_AiUsage_org_date" ON "AiUsage"("organizationId", "createdAt");

-- CreateIndex
CREATE INDEX "IX_AiUsage_org_provider" ON "AiUsage"("organizationId", "provider");

-- CreateIndex
CREATE INDEX "IX_AiUsage_date" ON "AiUsage"("createdAt");

-- AddForeignKey
ALTER TABLE "AiUsage" ADD CONSTRAINT "AiUsage_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
