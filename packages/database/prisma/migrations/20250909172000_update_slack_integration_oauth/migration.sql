-- Convert SlackIntegration from webhook to OAuth
-- This migration safely updates existing SlackIntegration records

-- First, add new OAuth columns as nullable
ALTER TABLE "SlackIntegration" ADD COLUMN "teamId" TEXT;
ALTER TABLE "SlackIntegration" ADD COLUMN "teamName" TEXT;
ALTER TABLE "SlackIntegration" ADD COLUMN "channelId" TEXT;
ALTER TABLE "SlackIntegration" ADD COLUMN "channelName" TEXT;
ALTER TABLE "SlackIntegration" ADD COLUMN "accessToken" TEXT;
ALTER TABLE "SlackIntegration" ADD COLUMN "botUserId" TEXT;
ALTER TABLE "SlackIntegration" ADD COLUMN "appId" TEXT;
ALTER TABLE "SlackIntegration" ADD COLUMN "authedUserId" TEXT;
ALTER TABLE "SlackIntegration" ADD COLUMN "scope" TEXT;

-- Remove the old webhookUrl column
ALTER TABLE "SlackIntegration" DROP COLUMN "webhookUrl";

-- Make teamId and channelId non-null (required fields)
-- Note: This will fail if there are existing records without these values
-- In production, you should populate these fields first or handle existing records
UPDATE "SlackIntegration" SET "teamId" = 'MIGRATION_PLACEHOLDER', "channelId" = 'MIGRATION_PLACEHOLDER' WHERE "teamId" IS NULL;
ALTER TABLE "SlackIntegration" ALTER COLUMN "teamId" SET NOT NULL;
ALTER TABLE "SlackIntegration" ALTER COLUMN "channelId" SET NOT NULL;