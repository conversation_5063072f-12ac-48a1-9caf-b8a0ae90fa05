generator client {
  provider        = "prisma-client-js"
  previewFeatures = []
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ---- Enumerations ---- //

enum ActionType {
  CREATE @map("create")
  UPDATE @map("update")
  DELETE @map("delete")
}

enum ActorType {
  SYSTEM @map("system")
  MEMBER @map("member")
  API    @map("api")
}



enum FeedbackCategory {
  SUGGESTION @map("suggestion")
  PROBLEM    @map("problem")
  QUESTION   @map("question")
}

enum InvitationStatus {
  PENDING  @map("pending")
  ACCEPTED @map("accepted")
  REVOKED  @map("revoked")
}

enum Role {
  MEMBER @map("member")
  ADMIN  @map("admin")
}


enum InstagramStage {
  FIRSTCONTACT   @map("firstcontact")
  RAPPORT        @map("rapport")
  DISCOVERY      @map("discovery")
  DISSONANCE     @map("dissonance")
  VISION         @map("vision")
  PROPOSAL       @map("proposal")
  QUALIFIED      @map("qualified")
  FORMSENT       @map("formsent")
  CONVERTED      @map("converted")
  DISQUALIFIED   @map("disqualified")
  SUSPICIOUS     @map("suspicious")
}

enum FollowupStatus {
  PENDING @map("pending")
  QUEUED  @map("queued")
  SENT    @map("sent")
  FAILED  @map("failed")
}


enum EngagementPriority {
  NEW_FOLLOWER    @map("new_follower")     // Chrome extension uploads
  VERY_HIGH       @map("very_high")        // AI determines from conversation
  HIGH            @map("high")             // AI determines from conversation
  MEDIUM          @map("medium")           // AI determines from conversation  
  LOW             @map("low")              // AI determines from conversation
  NON_RESPONDED   @map("non_responded")    // AI determines from conversation
}

enum ScrapingStatus {
  INITIAL_SCRAPING    @map("initial_scraping")   // Currently doing initial follower collection
  SCRAPING_COMPLETED  @map("scraping_completed") // Initial scraping done, ready for automation
}

// ---- Models ---- //

model Account {
  id                String   @id(map: "PK_Account") @default(uuid()) @db.Uuid
  userId            String   @db.Uuid
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?  @db.Text
  access_token      String?  @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?  @db.Text
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId], map: "IX_Account_userId")
}

model ApiKey {
  id             String       @id(map: "PK_ApiKey") @default(uuid()) @db.Uuid
  organizationId String       @db.Uuid
  description    String       @db.VarChar(70)
  hashedKey      String       @unique()
  expiresAt      DateTime?
  lastUsedAt     DateTime?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([organizationId], map: "IX_ApiKey_organizationId")
}

model AuthenticatorApp {
  id            String   @id(map: "PK_AuthenticatorApp") @default(uuid()) @db.Uuid
  userId        String   @unique @db.Uuid
  accountName   String   @db.VarChar(255)
  issuer        String   @db.VarChar(255)
  secret        String   @db.VarChar(255)
  recoveryCodes String   @db.VarChar(1024)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([userId], map: "IX_AuthenticatorApp_userId")
}

model ChangeEmailRequest {
  id        String   @id(map: "PK_ChangeEmailRequest") @default(uuid()) @db.Uuid
  userId    String   @db.Uuid
  email     String
  expires   DateTime
  valid     Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([userId], map: "IX_ChangeEmailRequest_userId")
}










model Feedback {
  id             String           @id(map: "PK_Feedback") @default(uuid()) @db.Uuid
  organizationId String           @db.Uuid
  userId         String?          @db.Uuid
  category       FeedbackCategory @default(SUGGESTION)
  message        String           @db.VarChar(4000)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  user           User?            @relation(fields: [userId], references: [id], onDelete: SetNull, onUpdate: Cascade)

  @@index([organizationId], map: "IX_Feedback_organizationId")
  @@index([userId], map: "IX_Feedback_userId")
}

model Invitation {
  id             String           @id(map: "PK_Invitation") @default(uuid()) @db.Uuid
  organizationId String           @db.Uuid
  token          String           @default(uuid()) @db.Uuid
  email          String           @db.VarChar(255)
  role           Role             @default(MEMBER)
  status         InvitationStatus @default(PENDING)
  lastSentAt     DateTime?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([organizationId], map: "IX_Invitation_organizationId")
  @@index([token], map: "IX_Invitation_token")
}

model Membership {
  id             String       @id(map: "PK_Membership") @default(uuid()) @db.Uuid
  organizationId String       @db.Uuid
  userId         String       @db.Uuid
  role           Role         @default(MEMBER)
  isOwner        Boolean      @default(false)
  createdAt      DateTime     @default(now())
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([organizationId, userId])
}

model Notification {
  id        String    @id(map: "PK_Notification") @default(uuid()) @db.Uuid
  userId    String    @db.Uuid
  subject   String?   @db.VarChar(128)
  content   String    @db.VarChar(8000)
  link      String?   @db.VarChar(2000)
  seenAt    DateTime?
  dismissed Boolean   @default(false)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([userId], map: "IX_Notification_userId")
}

model Order {
  id             String       @id(map: "PK_Order") @db.Text
  organizationId String       @db.Uuid
  status         String       @db.VarChar(64)
  provider       String       @db.VarChar(32)
  totalAmount    Decimal      @default(0)
  currency       String       @db.VarChar(3)
  items          OrderItem[]
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId], map: "IX_Order_organizationId")
}

model OrderItem {
  id          String  @id(map: "PK_OrderItem") @db.Text
  orderId     String  @db.Text
  quantity    Int
  productId   String
  variantId   String
  priceAmount Float?
  type        String?
  model       String?
  order       Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId], map: "IX_OrderItem_orderId")
}

model Organization {
  id                String         @id(map: "PK_Organization") @default(uuid()) @db.Uuid
  slug              String         @unique @db.VarChar(255)
  logo              String?        @db.VarChar(2048)
  name              String         @db.VarChar(255)
  email             String?        @db.VarChar(255)
  billingCustomerId String?
  billingEmail      String?        @db.VarChar(255)
  billingLine1      String?        @db.VarChar(255)
  billingLine2      String?        @db.VarChar(255)
  billingCountry    String?        @db.VarChar(3)
  billingPostalCode String?        @db.VarChar(16)
  billingCity       String?        @db.VarChar(255)
  billingState      String?        @db.VarChar(255)
  
  // Instagram Bot Settings
  instagramBotEnabled         Boolean               @default(false)
  instagramResponseTimeMin    Int                   @default(10)    // seconds, min 10s
  instagramResponseTimeMax    Int                   @default(180)   // seconds, max 3 mins
  instagramFollowupsEnabled   Boolean               @default(true)
  
  // AI Bot Style
  botStyleId                  String?               @db.Uuid
  botStyle                    BotStyle?             @relation(fields: [botStyleId], references: [id], onDelete: SetNull, onUpdate: Cascade)

  // Instagram Connection
  instagramToken              String?
  instagramAccountId          String?
  instagramWebhookId          String?
  instagramUsername           String?
  instagramName               String?
  instagramProfilePicture     String?
  instagramIsConnected        Boolean               @default(false)
  instagramTokenExpiresAt     DateTime?
  instagramConnectedAt        DateTime?
  instagramUpdatedAt          DateTime              @updatedAt @default(now())
  
  apiKeys                     ApiKey[]
  commentAutomations          CommentAutomation[]
  calendlyIntegration         CalendlyIntegration?
  slackIntegration            SlackIntegration?
  feedback                    Feedback[]
  instagramContacts           InstagramContact[]
  instagramMessages           InstagramMessage[]
  invitations                 Invitation[]
  memberships                 Membership[]
  orders                      Order[]
  subscriptions               Subscription[]
  webhooks                    Webhook[]
  chromeExtensionSettings     ChromeExtensionSettings?
  chromeExtensionFollowers    ChromeExtensionFollowers[]
  chromeExtensionAttackList   ChromeExtensionAttackList[]
  chromeExtensionMessageBatch ChromeExtensionMessageBatch[]
  instagramConversationIndex  InstagramConversationIndex[]
  aiUsage                     AiUsage[]
  botStyleVariables           OrganizationBotStyleVariable[]

  @@index([billingCustomerId], map: "IX_Organization_billingCustomerId")
}

model OrganizationLogo {
  id             String  @id(map: "PK_OrganizationLogo") @default(uuid()) @db.Uuid
  organizationId String  @db.Uuid
  data           Bytes?
  contentType    String? @db.VarChar(255)
  hash           String? @db.VarChar(64)

  @@index([organizationId], map: "IX_OrganizationLogo_organizationId")
}

model ResetPasswordRequest {
  id        String   @id(map: "PK_ResetPasswordRequest") @default(uuid()) @db.Uuid
  email     String
  expires   DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email], map: "IX_ResetPasswordRequest_email")
}

model Session {
  id           String   @id(map: "PK_Session") @default(uuid()) @db.Uuid
  sessionToken String   @unique
  userId       String   @db.Uuid
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([userId], map: "IX_Session_userId")
}

model Subscription {
  id                String             @id(map: "PK_Subscription") @db.Text
  organizationId    String             @db.Uuid
  status            String             @db.VarChar(64)
  active            Boolean            @default(false)
  provider          String             @db.VarChar(32)
  cancelAtPeriodEnd Boolean            @default(false)
  currency          String             @db.VarChar(3)
  periodStartsAt    DateTime           @db.Timestamptz(6)
  periodEndsAt      DateTime           @db.Timestamptz(6)
  trialStartsAt     DateTime?          @db.Timestamptz(6)
  trialEndsAt       DateTime?          @db.Timestamptz(6)
  items             SubscriptionItem[]
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  organization      Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId], map: "IX_Subscription_organizationId")
}

model SubscriptionItem {
  id             String       @id(map: "PK_SubscriptionItem") @db.Text
  subscriptionId String       @db.Text
  quantity       Int
  productId      String
  variantId      String
  priceAmount    Float?
  interval       String
  intervalCount  Int
  type           String?
  model          String?
  subscription   Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@index([subscriptionId], map: "IX_SubscriptionItem_subscriptionId")
}

model User {
  id                           String               @id(map: "PK_User") @default(uuid()) @db.Uuid
  image                        String?              @db.VarChar(2048)
  name                         String               @db.VarChar(64)
  email                        String?              @unique
  emailVerified                DateTime?
  password                     String?              @db.VarChar(60)
  lastLogin                    DateTime?
  locale                       String               @default("en-US") @db.VarChar(8)
  completedOnboarding          Boolean              @default(false)
  enabledContactsNotifications Boolean              @default(false)
  enabledInboxNotifications    Boolean              @default(false)
  enabledWeeklySummary         Boolean              @default(false)
  enabledNewsletter            Boolean              @default(false)
  enabledProductUpdates        Boolean              @default(false)
  isPlatformAdmin              Boolean              @default(false)
  createdAt                    DateTime             @default(now())
  updatedAt                    DateTime             @updatedAt
  accounts                     Account[]
  authenticatorApp             AuthenticatorApp?
  changeEmailRequests          ChangeEmailRequest[]
  feedback                     Feedback[]
  memberships                  Membership[]
  notifications                Notification[]
  sessions                     Session[]
}

model UserImage {
  id          String  @id(map: "PK_UserImage") @default(uuid()) @db.Uuid
  userId      String  @db.Uuid
  data        Bytes?
  contentType String? @db.VarChar(255)
  hash        String? @db.VarChar(64)

  @@index([userId], map: "IX_UserImage_userId")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Webhook {
  id             String       @id(map: "PK_Webhook") @default(uuid()) @db.Uuid
  organizationId String       @db.Uuid
  url            String       @db.VarChar(2000)
  secret         String?      @db.VarChar(1024)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId], map: "IX_Webhook_organizationId")
}



model InstagramContact {
  id             String       @id(map: "PK_InstagramContact") @default(uuid()) @db.Uuid
  organizationId String       @db.Uuid
  instagramUserId String      @db.VarChar(255)
  nickname       String?      @db.VarChar(255)
  profilePhoto   String?      @db.VarChar(2048)
  bio            String?      @db.VarChar(2000)  // Instagram bio
  notes          String?      @db.VarChar(8000)

  // AI Profile Analysis
  profileInsights    Json?         // AI-generated profile analysis
  profileAnalyzedAt  DateTime?     // When profile was last analyzed
  lastMediaUrls      Json?         // Array of last 9 media URLs
  
  // Communication tracking
  lastActivity          DateTime?
  messageCount          Int       @default(0)
  firstMessageAt        DateTime?
  lastMessageAt         DateTime?
  lastContactMessageAt  DateTime?
  
  // AI & Stage management
  aiEnabled  Boolean        @default(true)
  stage      InstagramStage @default(FIRSTCONTACT)
  
  // Engagement priority for follow-up scheduling
  engagementPriority     EngagementPriority @default(NEW_FOLLOWER)
  
  // Chrome Extension tracking
  sentByExtension        Boolean    @default(false)
  batchId               String?    @db.VarChar(255)
  
  // Follow-up 1
  followup1_text             String?       @db.VarChar(4000)
  followup1_scheduledAt      DateTime?
  followup1_status           FollowupStatus?
  followup1_sendViaExtension Boolean       @default(false)
  
  // Follow-up 2
  followup2_text             String?       @db.VarChar(4000)
  followup2_scheduledAt      DateTime?
  followup2_status           FollowupStatus?
  followup2_sendViaExtension Boolean       @default(false)
  
  // Follow-up 3
  followup3_text             String?       @db.VarChar(4000)
  followup3_scheduledAt      DateTime?
  followup3_status           FollowupStatus?
  followup3_sendViaExtension Boolean       @default(false)
  
  // Follow-up 4
  followup4_text             String?       @db.VarChar(4000)
  followup4_scheduledAt      DateTime?
  followup4_status           FollowupStatus?
  followup4_sendViaExtension Boolean       @default(false)
  
  // Follow-up 5
  followup5_text             String?       @db.VarChar(4000)
  followup5_scheduledAt      DateTime?
  followup5_status           FollowupStatus?
  followup5_sendViaExtension Boolean       @default(false)
  
  // Follow-up 6
  followup6_text             String?       @db.VarChar(4000)
  followup6_scheduledAt      DateTime?
  followup6_status           FollowupStatus?
  followup6_sendViaExtension Boolean       @default(false)
  
  // Follow-up 7
  followup7_text             String?       @db.VarChar(4000)
  followup7_scheduledAt      DateTime?
  followup7_status           FollowupStatus?
  followup7_sendViaExtension Boolean       @default(false)
  
  // Follow-up 8
  followup8_text             String?       @db.VarChar(4000)
  followup8_scheduledAt      DateTime?
  followup8_status           FollowupStatus?
  followup8_sendViaExtension Boolean       @default(false)
  
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([instagramUserId, organizationId])
  @@index([organizationId], map: "IX_InstagramContact_organizationId")
  @@index([lastContactMessageAt], map: "IX_InstagramContact_lastContactMessageAt")
  @@index([followup1_status, followup1_sendViaExtension, followup1_scheduledAt], map: "IX_InstagramContact_followup1")
  @@index([followup2_status, followup2_sendViaExtension, followup2_scheduledAt], map: "IX_InstagramContact_followup2")
  @@index([followup3_status, followup3_sendViaExtension, followup3_scheduledAt], map: "IX_InstagramContact_followup3")
  @@index([followup4_status, followup4_sendViaExtension, followup4_scheduledAt], map: "IX_InstagramContact_followup4")
  @@index([followup5_status, followup5_sendViaExtension, followup5_scheduledAt], map: "IX_InstagramContact_followup5")
  @@index([followup6_status, followup6_sendViaExtension, followup6_scheduledAt], map: "IX_InstagramContact_followup6")
  @@index([followup7_status, followup7_sendViaExtension, followup7_scheduledAt], map: "IX_InstagramContact_followup7")
  @@index([followup8_status, followup8_sendViaExtension, followup8_scheduledAt], map: "IX_InstagramContact_followup8")
}

model PlatformSettings {
  id            String   @id(map: "PK_PlatformSettings") @default(uuid()) @db.Uuid
  
  // AI Context Settings
  maxConversationMessages    Int     @default(25)  // 25 to 1000 (FULL)
  
  // AI Model Settings (SaaS Admin Only)
  aiModel                    String  @default("openai/gpt-4o-mini")
  enableReasoning            Boolean @default(false)
  reasoningMaxTokens         Int?    // Max tokens for reasoning
  temperature                Float   @default(1.0)     // AI temperature (0.0-2.0)
  topP                       Float   @default(1.0)     // AI top P (0.0-1.0)
  openRouterApiKey           String? @db.Text          // OpenRouter API key
  zaiApiKey                  String? @db.Text          // Z.AI API key
  anthropicApiKey            String? @db.Text          // Anthropic API key
  enableThinking             Boolean @default(false)   // Enable Claude thinking mode
  promptCacheDuration        String  @default("5m")    // Prompt cache duration: "5m" or "1h"

  // AI Profile Analysis Settings
  enableProfileAnalysis      Boolean @default(false) // Enable AI profile analysis
  profileAnalysisPrompt      String? @db.Text        // Custom prompt for profile analysis

  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

model BotStyle {
  id                    String                  @id(map: "PK_BotStyle") @default(uuid()) @db.Uuid
  title                 String                  @db.VarChar(255)
  description           String                  @db.VarChar(500)
  prompt                String                  @db.Text
  isDefault             Boolean                 @default(false)
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  organizations         Organization[]
  variableGroups        BotStyleVariableGroup[]
  variables             BotStyleVariable[]
}

model BotStyleVariableGroup {
  id          String             @id(map: "PK_BotStyleVariableGroup") @default(uuid()) @db.Uuid
  botStyleId  String             @db.Uuid
  name        String             @db.VarChar(100)
  description String?            @db.VarChar(500)
  order       Int                @default(0)
  collapsible Boolean            @default(true)
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  botStyle    BotStyle           @relation(fields: [botStyleId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  variables   BotStyleVariable[]

  @@unique([botStyleId, name], map: "UQ_BotStyleVariableGroup_botStyleId_name")
  @@index([botStyleId, order], map: "IX_BotStyleVariableGroup_botStyle_order")
}

model BotStyleVariable {
  id           String                           @id(map: "PK_BotStyleVariable") @default(uuid()) @db.Uuid
  botStyleId   String                           @db.Uuid
  groupId      String?                          @db.Uuid
  name         String                           @db.VarChar(100)  // {{variable_name}}
  displayName  String                           @db.VarChar(255)  // Human readable name
  type         String                           @db.VarChar(50)   // text, textarea, dropdown, array, structured_list
  required     Boolean                          @default(false)
  helpText     String?                          @db.VarChar(1000)
  defaultValue String?                          @db.Text
  config       Json?                            // Type-specific configuration
  order        Int                              @default(0)
  createdAt    DateTime                         @default(now())
  updatedAt    DateTime                         @updatedAt
  botStyle     BotStyle                         @relation(fields: [botStyleId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  group        BotStyleVariableGroup?           @relation(fields: [groupId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  values       OrganizationBotStyleVariable[]

  @@unique([botStyleId, name], map: "UQ_BotStyleVariable_botStyle_name")
  @@index([botStyleId, groupId, order], map: "IX_BotStyleVariable_botStyle_group_order")
}

model OrganizationBotStyleVariable {
  id           String            @id(map: "PK_OrganizationBotStyleVariable") @default(uuid()) @db.Uuid
  organizationId String          @db.Uuid
  botStyleId   String            @db.Uuid
  variableId   String            @db.Uuid
  value        Json              // Variable value (can be string, array, object)
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  organization Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  variable     BotStyleVariable  @relation(fields: [variableId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([organizationId, botStyleId, variableId], map: "UQ_OrgBotStyleVariable_org_botStyle_variable")
  @@index([organizationId, botStyleId], map: "IX_OrgBotStyleVariable_org_botStyle")
}


model InstagramConversationIndex {
  id                  String       @id(map: "PK_InstagramConversationIndex") @default(uuid()) @db.Uuid
  organizationId      String       @db.Uuid
  conversationId      String       @db.VarChar(255)
  participantId       String       @db.VarChar(255)
  participantUsername String?      @db.VarChar(255)
  lastUpdated         DateTime
  createdAt           DateTime     @default(now())
  organization        Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([organizationId, conversationId], map: "UQ_InstagramConversationIndex_org_conversation")
  @@index([organizationId, participantUsername], map: "IX_InstagramConversationIndex_org_username")
  @@index([organizationId, lastUpdated], map: "IX_InstagramConversationIndex_org_updated")
}

model InstagramMessage {
  id                   String       @id(map: "PK_InstagramMessage") @default(uuid()) @db.Uuid
  organizationId       String       @db.Uuid
  conversationId       String       @db.VarChar(255)
  instagramUserId      String       @db.VarChar(255)
  messageId            String       @db.VarChar(255)
  isFromBusiness       Boolean      @default(false)
  messageType          String       @default("text") @db.VarChar(50)
  content              String?      @db.Text
  attachmentUrl        String?      @db.VarChar(2048)
  aiReasoning          String?      @db.Text
  instagramCreatedTime DateTime
  createdAt            DateTime     @default(now())
  organization         Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([messageId, organizationId], map: "UQ_InstagramMessage_messageId_org")
  @@index([conversationId, instagramCreatedTime], map: "IX_InstagramMessage_conversation_time")
  @@index([organizationId, instagramUserId], map: "IX_InstagramMessage_org_user")
}

model CommentAutomation {
  id             String       @id(map: "PK_CommentAutomation") @default(uuid()) @db.Uuid
  organizationId String       @db.Uuid
  name           String       @db.VarChar(255)
  enabled        Boolean      @default(true)
  
  // Post targeting
  selectedPostIds Json?       // Array of Instagram post IDs
  applyToAll     Boolean      @default(false)
  
  // Trigger configuration
  keywords       Json         // Array of keyword strings (case-insensitive)
  
  // Response configuration  
  dmMessage      String       @db.Text
  replyToComment Boolean      @default(false)
  commentReply   String?      @db.Text
  
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([organizationId, enabled], map: "IX_CommentAutomation_org_enabled")
}

model CalendlyIntegration {
  id                      String       @id(map: "PK_CalendlyIntegration") @default(uuid()) @db.Uuid
  organizationId          String       @unique @db.Uuid
  apiToken                String       @db.Text // Encrypted Personal Access Token
  eventTypeUri            String?      @db.VarChar(500) // Event type URI from Calendly
  webhookId               String?      @db.VarChar(255) // Webhook subscription ID
  calendlyOrganizationUri String?      @db.VarChar(500) // Calendly organization URI
  enabled                 Boolean      @default(false)
  
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt
  organization     Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([organizationId], map: "IX_CalendlyIntegration_organizationId")
}

model SlackIntegration {
  id              String       @id(map: "PK_SlackIntegration") @default(uuid()) @db.Uuid
  organizationId  String       @unique @db.Uuid
  
  // OAuth data
  teamId          String       // Slack workspace ID
  teamName        String?      // Workspace name
  channelId       String       // Selected channel ID
  channelName     String?      // Channel name
  
  // Tokens
  accessToken     String?      @db.Text // Bot token for sending messages
  botUserId       String?      // Bot user ID
  
  // Installation data
  appId           String?      // Slack app ID
  authedUserId    String?      // User who installed
  scope           String?      // Granted scopes
  
  enabled         Boolean      @default(true)
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  organization    Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([organizationId], map: "IX_SlackIntegration_organizationId")
}

model AiUsage {
  id                      String       @id(map: "PK_AiUsage") @default(uuid()) @db.Uuid
  organizationId          String       @db.Uuid
  instagramContactId      String?      @db.Uuid
  conversationId          String?      @db.VarChar(255)
  
  // AI Model & Provider Info
  provider                String       @db.VarChar(50)   // "openrouter", "zai"
  model                   String       @db.VarChar(255)  // "anthropic/claude-3-opus"
  
  // Token Usage
  totalTokens             Int
  promptTokens            Int
  completionTokens        Int
  reasoningTokens         Int          @default(0)
  cachedTokens            Int          @default(0)
  audioTokens             Int          @default(0)
  
  // Cost Information (in credits/cents)
  totalCost               Decimal      @db.Decimal(10, 6)
  upstreamCost            Decimal?     @db.Decimal(10, 6)
  
  // Request Context
  requestType             String       @db.VarChar(50)   // "instagram_response", "followup", etc.
  responseGenerated       Boolean      @default(true)
  
  createdAt               DateTime     @default(now())
  organization            Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  
  @@index([organizationId, createdAt], map: "IX_AiUsage_org_date")
  @@index([organizationId, provider], map: "IX_AiUsage_org_provider")
  @@index([createdAt], map: "IX_AiUsage_date")
}

model ChromeExtensionSettings {
  id                    String         @id(map: "PK_ChromeExtensionSettings") @default(uuid()) @db.Uuid
  organizationId        String         @unique @db.Uuid
  maxActionsPerDay      Int            @default(50)
  dmBeforeBreakMin      Int            @default(7)
  dmBeforeBreakMax      Int            @default(10)
  breakTimeMin          Int            @default(30)
  breakTimeMax          Int            @default(60)
  delayBetweenDmsMin    Int            @default(5)
  delayBetweenDmsMax    Int            @default(10)
  naturalStopStart      String         @default("00:00") @db.VarChar(5)
  naturalStopEnd        String         @default("23:59") @db.VarChar(5)
  hasConversationIndex  Boolean        @default(false)
  scrapingStatus        ScrapingStatus @default(INITIAL_SCRAPING)
  apiKey                String         @unique @db.VarChar(255)
  createdAt             DateTime       @default(now())
  updatedAt             DateTime       @updatedAt
  organization          Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([organizationId], map: "IX_ChromeExtensionSettings_organizationId")
  @@index([apiKey], map: "IX_ChromeExtensionSettings_apiKey")
}

model ChromeExtensionFollowers {
  id                    String       @id(map: "PK_ChromeExtensionFollowers") @default(uuid()) @db.Uuid
  organizationId        String       @db.Uuid
  instagramUserId       String       @db.VarChar(255)
  nickname              String?      @db.VarChar(255)
  profilePhoto          String?      @db.VarChar(2048)
  collectedAt           DateTime     @default(now())
  isProcessed           Boolean      @default(false)
  createdAt             DateTime     @default(now())
  organization          Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([organizationId, instagramUserId], map: "UQ_ChromeExtensionFollowers_org_user")
  @@index([organizationId, isProcessed, collectedAt], map: "IX_ChromeExtensionFollowers_org_processed_collected")
}

model ChromeExtensionAttackList {
  id                    String       @id(map: "PK_ChromeExtensionAttackList") @default(uuid()) @db.Uuid
  organizationId        String       @db.Uuid
  instagramUserId       String       @db.VarChar(255)
  nickname              String?      @db.VarChar(255)
  profilePhoto          String?      @db.VarChar(2048)
  priority              Int          // 1=new followers, 2=saas followups, 3=non-responders
  messageContent        Json?        // Array of messages - empty for extension batches, content for followups
  messageSource         String       @db.VarChar(50) // "chrome_extension_batch" or "saas_followup"
  messageBatchId        String?      @db.Uuid // Reference to ChromeExtensionMessageBatch
  scheduledAt           DateTime
  status                String       @default("pending") @db.VarChar(20) // pending, sent, failed, skipped
  sentAt                DateTime?
  createdAt             DateTime     @default(now())
  organization          Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  messageBatch          ChromeExtensionMessageBatch? @relation(fields: [messageBatchId], references: [id], onDelete: SetNull)

  @@index([organizationId, priority, scheduledAt, status], map: "IX_ChromeExtensionAttackList_priority_queue")
  @@index([messageBatchId], map: "IX_ChromeExtensionAttackList_messageBatchId")
}

model ChromeExtensionMessageBatch {
  id                    String       @id(map: "PK_ChromeExtensionMessageBatch") @default(uuid()) @db.Uuid
  organizationId        String       @db.Uuid
  name                  String       @db.VarChar(255)
  messages              Json         // ["Hej", "dzieki za follow", "jestes tu po wiedze czy po cos innego"]
  isActive              Boolean      @default(true)
  createdAt             DateTime     @default(now())
  updatedAt             DateTime     @updatedAt
  organization          Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  attackListItems       ChromeExtensionAttackList[]

  @@index([organizationId, isActive], map: "IX_ChromeExtensionMessageBatch_org_active")
}

