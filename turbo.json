{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalEnv": ["NEXT_PUBLIC_ANALYTICS_GA_MEASUREMENT_ID", "NEXT_PUBLIC_ANALYTICS_GA_DISABLE_LOCALHOST_TRACKING", "NEXT_PUBLIC_ANALYTICS_GA_DISABLE_PAGE_VIEWS_TRACKING", "NEXT_PUBLIC_ANALYTICS_POSTHOG_KEY", "NEXT_PUBLIC_ANALYTICS_POSTHOG_HOST", "NEXT_PUBLIC_ANALYTICS_UMAMI_HOST", "NEXT_PUBLIC_ANALYTICS_UMAMI_WEBSITE_ID", "NEXT_PUBLIC_ANALYTICS_UMAMI_DISABLE_LOCALHOST_TRACKING", "AUTH_SECRET", "AUTH_GOOGLE_CLIENT_ID", "AUTH_GOOGLE_CLIENT_SECRET", "AUTH_MICROSOFT_ENTRA_ID_CLIENT_ID", "AUTH_MICROSOFT_ENTRA_ID_CLIENT_SECRET", "BILLING_STRIPE_SECRET_KEY", "BILLING_STRIPE_WEBHOOK_SECRET", "NEXT_PUBLIC_BILLING_PRICE_PRO_MONTH_ID", "NEXT_PUBLIC_BILLING_PRICE_PRO_YEAR_ID", "NEXT_PUBLIC_BILLING_PRICE_LIFETIME_ID", "NEXT_PUBLIC_BILLING_PRICE_ENTERPRISE_MONTH_ID", "NEXT_PUBLIC_BILLING_PRICE_ENTERPRISE_YEAR_ID", "DATABASE_URL", "EMAIL_FROM", "EMAIL_FEEDBACK_INBOX", "EMAIL_NODEMAILER_URL", "EMAIL_POSTMARK_SERVER_TOKEN", "EMAIL_RESEND_API_KEY", "EMAIL_SENDGRID_API_KEY", "MONITORING_SENTRY_ORG", "MONITORING_SENTRY_PROJECT", "MONITORING_SENTRY_AUTH_TOKEN", "NEXT_PUBLIC_MONITORING_SENTRY_DSN", "SENTRY_SUPPRESS_TURBOPACK_WARNING", "NEXT_PUBLIC_DASHBOARD_URL", "NEXT_PUBLIC_MARKETING_URL", "NEXT_PUBLIC_PUBLIC_API_URL"], "tasks": {"dev": {"dependsOn": ["^generate"], "cache": false, "persistent": true}, "build": {"dependsOn": ["^generate"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "start": {"cache": false, "persistent": true}, "clean": {"cache": false}, "//#clean": {"cache": false}, "check-types": {"dependsOn": ["^check-types"]}, "format": {"dependsOn": ["^format"]}, "format:fix": {"dependsOn": ["^format:fix"]}, "lint": {"dependsOn": ["^lint"]}, "typecheck": {"dependsOn": ["^typecheck"]}, "analyze": {"dependsOn": ["^analyze"]}, "license#dev": {"outputLogs": "errors-only"}, "generate": {"cache": false}}}