{"name": "@workspace/prettier-config", "version": "0.0.0", "private": true, "type": "module", "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "typecheck": "tsc --noEmit"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.4.2", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.12"}, "prettier": "./index.js", "exports": {".": "./index.js"}}