{"name": "@workspace/eslint-config", "version": "0.0.0", "private": true, "type": "module", "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "typecheck": "tsc --noEmit"}, "devDependencies": {"@eslint/js": "9.28.0", "@next/eslint-plugin-next": "15.3.3", "@typescript-eslint/eslint-plugin": "8.33.1", "@typescript-eslint/parser": "8.33.1", "eslint": "9.28.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-only-warn": "1.1.0", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-turbo": "2.5.4", "globals": "16.2.0", "typescript": "5.8.3", "typescript-eslint": "8.33.1"}, "exports": {"./base": "./base.js", "./next": "./next.js", "./react-internal": "./react-internal.js"}}